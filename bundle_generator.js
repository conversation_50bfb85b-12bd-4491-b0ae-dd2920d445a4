var fs = require('fs');
var path = require('path');


var listBundleName = [];
var listVersion = []
var bundleVersionData = {};
var urlBundle = "https://hupdate.bavenoth.com/remote"
var assetsUrl = "build/android/assets/remote"

// Parse arguments
var i = 2;
while (i < process.argv.length) {
    var arg = process.argv[i];
    switch (arg) {
        case '--url':
        case '-u':
            urlBundle = process.argv[i + 1];
            i += 2;
            break;
        case '--src':
        case '-s':
            assetsUrl = process.argv[i + 1];
            i += 2;
            break;
        case '--dest':
        case '-d':
            dest = process.argv[i + 1];
            i += 2;
            break;
        default:
            i++;
            break;
    }
}

fs.readdir(assetsUrl, (err, files) => {
    listVersion = [];
    files.forEach(file => {
        listBundleName.push(file);
        getVersionBundle(path.join(assetsUrl, file), file);
    });
    setTimeout(() => {
        createVersionJson();
    }, 2000);
});
var getVersionBundle = function (dir, bundleName) {
    let status = fs.statSync(dir);
    if (status.isDirectory()) {
        let bundleUrl = urlBundle + "/" + bundleName;
        fs.readdir(dir, (err, files) => {
            files.forEach(file => {
                if (file.includes("index")) {
                    let dataBundle = {};
                    let arrName = file.split('.');
                    let hash = '';
                    if(arrName.length > 2 ){
                        hash = arrName[1];
                    }
                    listVersion.push(hash);
                    dataBundle['hash'] = hash;
                    dataBundle.url = bundleUrl;
                    bundleVersionData[bundleName] = dataBundle;
                    console.log("dataBundle:", dataBundle)
                }
            });
        });
    }
}
var createVersionJson = function () {
    let str = JSON.stringify(bundleVersionData);
    str = str.replace(/\\/g, "/");
    console.log(str);
    fs.writeFileSync(path.join(assetsUrl, 'BundleVersion.json'), str);
    console.log('Generate BundleVersion.json successfully!');
}

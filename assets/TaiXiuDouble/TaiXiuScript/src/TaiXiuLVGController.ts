import { _decorator, Component, Sprite<PERSON><PERSON>e, Label, Node, Button, BitmapFont, Prefab, EditBox, Event, EventTouch, Sprite, instantiate, Animation, tween, v2, v3, Tween, UIOpacity } from "cc";
import SignalRClient from "db://assets/Lobby/scripts/common/networks/Network.SignalRClient";
import Http from "db://assets/Lobby/scripts/common/Http";
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import MiniGameTX1SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX1SignalRClient";
import MiniGameTX2SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX2SignalRClient";
import MiniGameTX3SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX3SignalRClient";
import MiniGameSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameSignalRClient";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import TaiXiuDoubleController from "db://assets/TaiXiuDouble/TaiXiuScript/src/TaiXiuDouble.Controller";
import PopupDetailSession from "./TaiXiuJP.PopupDetailSession";
import Config from "db://assets/Lobby/scripts/common/Config";
const {ccclass, property, menu} = _decorator;

@ccclass('TaiXiuLVGController')
@menu("TaiXiuJP/TaiXiuCenterController")
export default class TaiXiuLVGController extends Component {
    static instance: TaiXiuLVGController = null;
    @property(SpriteFrame)
    sprFrameTai: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameXiu: SpriteFrame | null = null;
    @property(Label)
    lblSession: Label | null = null;
    @property(Label)
    lblRemainTime: Label | null = null;
    @property(Label)
    lblRemainWaiting: Label | null = null;
    @property(Label)
    lblSumDices: Label | null = null;
    @property(Label)
    lblTotalBetTaiAll: Label | null = null;
    @property(Label)
    lblTotalBetXiuAll: Label | null = null;
    @property(Label)
    lblTotalBetTaiCurrent: Label | null = null;
    @property(Label)
    lblTotalBetXiuCurrent: Label | null = null;
    @property(Label)
    lblBetTai: Label | null = null;
    @property(Label)
    lblBetXiu: Label | null = null;
    @property(Label)
    lblBetTaiXu: Label | null = null;
    @property(Label)
    lblBetXiuXu: Label | null = null;
    @property(Label)
    lblUserTai: Label | null = null;
    @property(Label)
    lblUserXiu: Label | null = null;
    @property(Node)
    dicesContainer: Node | null = null;
    @property([SpriteFrame])
    listSprDice: SpriteFrame[] = [];
    @property(Node)
    nodeTai: Node | null = null;
    @property(Node)
    nodeXiu: Node | null = null;
    @property(Button)
    buttonNan: Button | null = null;
    @property(Node)
    bowl: Node | null = null;
    @property(Node)
    btnHistories: Node | null = null;
    @property(Label)
    lblToast: Label | null = null;
    @property([BitmapFont])
    fontTime: BitmapFont[] = [];
    @property(Node)
    animationDragonTipzoLVG: Node | null = null;
    @property(Node)
    coinGold: Node | null = null;
    @property(Node)
    coinSliver: Node | null = null;
    @property(Node)
    wheelNode: Node | null = null;
    @property(Node)
    historyList: Node | null = null;
    @property(Node)
    historyItem: Node | null = null;
    historySessions = [];
    @property(Prefab)
    public popupDetailHistory: Prefab | null = null;
    @property(Node)
    popupContainer: Node | null = null;
    @property(Node)
    nodeWheelSpin: Node | null = null;
    @property(Node)
    nodeJackPot: Node | null = null;
    @property(Node)
    lblJackPot: Node | null = null;
    private isCoinGold: boolean = true;
    private currentTotalBetTai: number = 0;
    private currentTotalBetXiu: number = 0;
    private allTotalBetTai: number = 0;
    private allTotalBetXiu: number = 0;
    private resetLabels = [];
    private isBetting = false;
    private isNan = false;
    private lastLocationIDWin = 0;
    private lastScore = 0;
    private readonly bowlStartPos = v2(-252, 35);
    private arrTimeoutDice = [];
    private popupDetailSession = null;
    @property(EditBox)
    editBoxBetTai: EditBox | null = null;
    @property(EditBox)
    editBoxBetXiu: EditBox | null = null;
    @property(Node)
    layoutBet1: Node | null = null;
    @property(Node)
    layoutBet2: Node | null = null;
    private gameID: number;
    private hub: SignalRClient = null;
    private betTypeLocation: number = 0; // 1: Xiu, 2: Tai
    private currentBetType: string = "";
    // EventSH
    @property([Node])
    boxes: Node[] = [];
    @property([SpriteFrame])
    sprFrameBoxes: SpriteFrame[] = [];
    @property([SpriteFrame])
    sprFramePrizes: SpriteFrame[] = [];
    @property(SpriteFrame)
    sprFrameBoxDefault: SpriteFrame | null = null;
    @property(Label)
    lblJackpotCoin: Label | null = null;
    @property(Node)
    winTextNode: Node | null = null;
    private countdownRemainTime: Function = null;
    private countdownWaitingTime: Function = null;
    private isOpenBowl = false;
    private isShowWheel = false;
    private jackpotInfo: any = null;
    private lastBetAmount: number = 0;
    private lastBetAmountXu: number = 0;
    onLoad() {
        TaiXiuLVGController.instance = this;

        this.resetLabels = [
            this.lblTotalBetTaiAll,
            this.lblTotalBetXiuAll,
            this.lblTotalBetTaiCurrent,
            this.lblTotalBetXiuCurrent,
        ];
    }

    onDisable() {
        for (var i = 0; i < this.arrTimeoutDice.length; i++) {
            clearTimeout(this.arrTimeoutDice[i]);
        }
        this.arrTimeoutDice = [];
    }

    start() {
        this.bowl.on(
            Node.EventType.TOUCH_MOVE,
            (event: EventTouch) => {
                var pos = this.bowl.getPosition();
                pos.x += event.getDeltaX();
                pos.y += event.getDeltaY();
                this.bowl.position = pos;

                let distance = Utils.v2Distance(
                    v2(pos.x, pos.y),
                    this.bowlStartPos
                );
                if (Math.abs(distance) > 300) {
                    this.showResult();
                    this.isOpenBowl = true;
                    this.scheduleOnce(() => {
                        this.bowl.active = false;
                    }, 2);
                }
            },
            this
        );

        this.lblToast.node.parent.active = false;
        this.bowl.active = false;
        this.dicesContainer.active = false;
    }

    loadEventChestCheck() {
        if (Config.Login.IsLogin == false) return;
        Http.get(Configs.App.DOMAIN_CONFIG['GetEventChestAccountInfo'], {GameID: this.gameID, currencyID: Configs.Login.CurrencyID}, (_status, res) => {
            if (res.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            if (!res.d) {
                return;
            }

            this.boxes.forEach(box => {
                box.getComponent(Sprite).spriteFrame = this.sprFrameBoxDefault;
                box.off(Node.EventType.TOUCH_END);
                box.getChildByName('time').active = false;
            });

            res.d.ListChest.forEach((data: any) => {
                if (data.GameID != this.gameID) {
                    return;
                }

                let awardTime = data.AwardTimeCount;
                if (data.ExpireTimeCount < 0) {
                    return;
                }

                for (var i = 0; i < 4; i++) {
                    let box: Node = this.boxes[i];
                    if (box.getComponent(Sprite).spriteFrame != this.sprFrameBoxDefault) {
                        continue;
                    }

                    if (awardTime > 0) {
                        const prize = data.PrizeValue > 0 ? data.PrizeValue : data.SpecialGift;
                        if (prize <= 0) continue;

                        var targetFrame = this.sprFramePrizes.find(sprFrame => sprFrame.name === `prize-value-${Utils.formatMoney(prize, true)}`);
                        if (targetFrame) {
                            box.getComponent(Sprite).spriteFrame = targetFrame;
                            let timeNode = box.getChildByName('time');
                            timeNode.active = true;

                            let countdownInterval = setInterval(() => {
                                if (awardTime < 0) {
                                    clearInterval(countdownInterval);
                                    timeNode.getComponentInChildren(Label).string = "00:00";
                                    return;
                                }

                                var minutes = Math.floor(awardTime / 60);
                                var seconds = awardTime % 60;
                                timeNode.getComponentInChildren(Label).string = `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                                awardTime--;
                            }, 1000);
                        }

                        break;
                    }

                    box.getComponent(Sprite).spriteFrame = this.sprFrameBoxes[data.PrizeID - 1];
                    box.on(Node.EventType.TOUCH_END, () => {
                        if (data.AwardTimeCount > 0) {
                            this.showToast(App.instance.getTextLang("ev43"));
                        }

                        this.openEventChest(box, data.ChestID);
                    });
                    break;
                }
            });
        });
    }

    openEventChest(_box: Node, ChestID: number) {
        Http.post(Configs.App.DOMAIN_CONFIG['PostEventChestOpen'], {GameID: this.gameID, CurrencyID: Configs.Login.CurrencyID, ChestID: ChestID}, (_status, res) => {
            if (res.c < 0) {
                this.showToast(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            var data = res.d;

            var award: string;
            if (data.PrizeValue === 0 && data.SpecialGift === 0) {
                award = "";
            } else {
                if (data.PrizeValue > 0) {
                    award = Utils.formatNumber(data.PrizeValue);
                } else {
                    if (data.SpecialGift == 1) {
                        award = "SH";
                    } else if (data.SpecialGift == 2) {
                        award = "Iphone";
                    } else {
                        award = "";
                    }
                }
            }

            this.showToast(App.instance.getTextLang("txt_reward") + " " + award);

            this.loadEventChestCheck();
        });
    }

    getLuckyDiceJackPot() {
        if (Config.Login.IsLogin == false) return;
        Http.get(Configs.App.DOMAIN_CONFIG['LuckyDiceJackPot'], {gameID: this.gameID}, (_status, res) => {
            if (res.c < 0) {
                return;
            }

            this.lblJackpotCoin.string = Utils.formatNumber(res.d);
        });
    }

    initHubs(gameID: number) {
        this.gameID = gameID;
        if (this.gameID == 1) {
            this.hub = MiniGameTX1SignalRClient.getInstance();
        } else if (this.gameID == 2) {
            this.hub = MiniGameTX2SignalRClient.getInstance();
        } else if (this.gameID == 3) {
            this.hub = MiniGameTX3SignalRClient.getInstance();
        }

        this.getLuckyDiceJackPot();

        this.hub.receive("currentSessionLD", (res) => {
            if (res.GameID !== this.gameID) return;

            this.lblSession.string = `#${res.GameSessionID}`;
            this.unschedule(this.countdownRemainTime);
            this.unschedule(this.countdownWaitingTime);
            if (res.GameStatus === 1) {
                MiniGameSignalRClient.getInstance().send("GetCurrentRoomsLD", [{GameID: this.gameID, CurrencyID: Configs.Login.CurrencyID, BetType: this.isCoinGold ? 1 : 2}], () => {});
                this.handleBettingPhase(res.RemainBetting);
            } else {
                this.handleWaitingPhase(res.RemainWaiting);
                this.hub.send("GetAccountResultLD", [{GameID: this.gameID, CurrencyID: Configs.Login.CurrencyID, GameSessionID: res.GameSessionID}], () => {});
            }

            this.loadEventChestCheck();
        });

        this.hub.receive("currentResultLD", (res) => {
            if (res.GameID != this.gameID) {
                return;
            }
            this.buttonNan.enabled = false;
            this.editBoxBetTai.enabled = false;
            this.editBoxBetXiu.enabled = false;
            this.lblRemainTime.node.active = false;
            this.lblRemainWaiting.node.parent.active = false;
            this.animationDragonTipzoLVG.active = false;
            const aminResult = this.dicesContainer.getChildByName('anim');
            const resultNode = this.dicesContainer.getChildByName('result');
            resultNode.active = false;
            aminResult.active = true;
            const dice_1 = resultNode.getChildByName('dice_1');
            const dice_2 = resultNode.getChildByName('dice_2');
            const dice_3 = resultNode.getChildByName('dice_3');
            dice_1.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice1 - 1];
            dice_2.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice2 - 1];
            dice_3.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice3 - 1];
            this.isShowWheel = (res.Dice1 == 1 && res.Dice2 == 1 && res.Dice3 == 1) || (res.Dice1 == 6 && res.Dice2 == 6 && res.Dice3 == 6);
            this.jackpotInfo = res.JackPotInfo || null;
            this.dicesContainer.active = true;
            const anim = aminResult.getComponent(Animation);
            anim.play();
            this.scheduleOnce(() => {
                anim.stop();
                aminResult.active = false;

                this.lastLocationIDWin = res.LocationIDWin;
                this.lastScore = res.Dice1 + res.Dice2 + res.Dice3;
                if (this.isNan) {
                    this.bowl.setPosition(v3(this.bowlStartPos.x, this.bowlStartPos.y, 0));
                    this.bowl.active = true;

                    this.scheduleOnce(() => {
                        if (this.isOpenBowl) {
                            return;
                        }
                        this.isOpenBowl = true;
                        tween(this.bowl)
                            .to(0.5, {position: v3(0, -220, 0)})
                            .call(() => {
                                this.showResult();
                                this.scheduleOnce(() => {
                                    this.bowl.active = false;
                                }, 2);
                            })
                            .start();
                    }, 10);
                } else {
                    this.showResult();
                }
                resultNode.active = true;
            }, 3);
        });

        this.hub.receive("currentRoomsInfoLD", (res) => {
            this.handleRoomInfo(res, true);
        });

        MiniGameSignalRClient.getInstance().receive("currentRoomsInfoLD", (res) => {
            this.handleRoomInfo(res, false);
        });

        this.hub.receive("gameHistoryLD", (res) => {
            if (res == null || res.length == 0) {
                return;
            }
            var item: Node = null;

            if (res[0].GameID == this.gameID) {
                this.historyList.removeAllChildren();
            } else {
                return;
            }

            var isReset = false;
            for (let i = res.length - 1; i >= 0; i--) {
                if (res[i].GameID != this.gameID) {
                    continue;
                }

                if (!isReset) {
                    this.historySessions = [];
                    isReset = true;
                }

                this.historySessions.push(res[i]);

                let item = instantiate(this.historyItem);
                item.getComponent(Sprite).spriteFrame = res[i].LocationIDWin == 1 ? this.sprFrameXiu : this.sprFrameTai;
                item.on(Node.EventType.TOUCH_END, () => {
                    this.actPopupHistorySession(res[i].GameSessionID, this.historySessions);
                });
                this.historyList.addChild(item);

                if (i === 0) {
                    item.getChildByName("last").active = true;
                    Tween.stopAllByTarget(item);

                    this.scheduleOnce(() => {
                        const posUp = v3(item.position.x, 5, item.position.z);
                        const posDown = v3(item.position.x, -5, item.position.z);

                        tween(item)
                            .repeatForever(
                                tween()
                                    .to(0.3, { position: posUp })
                                    .to(0.3, { position: posDown })
                            )
                            .start();
                    }, 0);
                }
            }
        });

        this.hub.receive("betOfAccountLD", (res) => {
            res.forEach((item: any) => {
                if (item.GameID != this.gameID) {
                    return;
                }

                this.editBoxBetTai.string = '';
                this.editBoxBetXiu.string = '';

                if (item.BetType == 1) {
                    const betLocation = item.LocationID == 1 ? this.lblBetXiu : this.lblBetTai;
                    betLocation.string = Utils.formatMoneyOnlyK(item.BetValue);
                } else if (item.BetType == 2) {
                    const betLocation = item.LocationID == 1 ? this.lblBetXiuXu : this.lblBetTaiXu;
                    betLocation.string = Utils.formatMoneyOnlyK(item.BetValue);
                }
            })
        });

        this.hub.receive("resultOfAccountLD", (res) => {
            let totalPrize = 0;

            res.forEach((item: any) => {
                if (item.GameID === this.gameID && item.PrizeValue > 0) {
                    totalPrize += item.PrizeValue;
                }
            });

            if (totalPrize <= 0) {
                this.winTextNode.active = false;
                return;
            }

            if (this.isCoinGold) {
                Configs.Login.GoldBalance += totalPrize;
            } else {
                Configs.Login.CoinBalance += totalPrize;
            }
            this.handleWinOrRefundAmount(totalPrize);
        });
    }

    private handleBettingPhase(remainTime: number) {
        if (remainTime === 60) {
            this.showToast(App.instance.getTextLang("txt_taixiu_new_session"));
            this.getLuckyDiceJackPot();
            this.dicesContainer.active = false;
            this.isOpenBowl = false;
            this.arrTimeoutDice = [];
            this.resetSessionLabels();
            this.lblBetTai.string = "0";
            this.lblBetXiu.string = "0";
            this.lblBetTaiXu.string = "0";
            this.lblBetXiuXu.string = "0";
            this.betTypeLocation = 0;
            this.lastScore = 0;
            this.lblSumDices.string = "";
            this.lblRemainTime.string = "60";
            this.lblRemainTime.font = this.fontTime[0];
            this.hideResult();
            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        }

        if (remainTime < 3) {
            this.isBetting = false;
            return;
        }

        this.isBetting = true;
        this.lblRemainTime.node.active = true;
        this.editBoxBetTai.enabled = true;
        this.editBoxBetXiu.enabled = true;
        this.buttonNan.enabled = true;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblRemainWaiting.node.active = false;

        let secondsLeft = remainTime;
        this.unschedule(this.countdownRemainTime);
        this.schedule(this.countdownRemainTime = () => {
            this.hideResult();
            try {
                if (secondsLeft < 0) {
                    this.animationDragonTipzoLVG.active = false;
                    this.unschedule(this.countdownRemainTime);
                    return;
                }

                this.animationDragonTipzoLVG.active = secondsLeft <= 30;
                this.lblRemainTime.string = secondsLeft < 10 ? `0${secondsLeft}` : `${secondsLeft}`;
                this.lblRemainTime.font = secondsLeft < 10 ? this.fontTime[1] : this.fontTime[0];

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownRemainTime);
            }
        }, 1);
    }

    private handleWaitingPhase(waitingTime: number) {
        if (waitingTime > 0) this.isBetting = false;

        if (waitingTime < 19) {
            this.hub.send("GetCurrentResultLD", [{ GameID: this.gameID }], () => {});
        }

        this.lblRemainTime.node.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        this.animationDragonTipzoLVG.active = false;
        let secondsLeft = waitingTime;
        this.unschedule(this.countdownWaitingTime);
        this.schedule(this.countdownWaitingTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownWaitingTime);
                    return;
                }

                if (secondsLeft < 28) {
                    if (this.isNan && this.isOpenBowl === false && secondsLeft > 17) {
                        const secondsLeftOpenBowl = secondsLeft - 17;
                        this.lblRemainWaiting.string = `00:${secondsLeftOpenBowl < 10 ? "0" + secondsLeftOpenBowl : secondsLeftOpenBowl}`;
                        this.lblRemainWaiting.node.active = true;
                        this.lblRemainWaiting.node.parent.active = true;
                        this.lblSumDices.node.active = false;
                    } else {
                        this.lblSumDices.node.active = secondsLeft > 15;
                        this.lblRemainWaiting.node.active = secondsLeft <= 15;
                        this.lblRemainWaiting.string = `00:${secondsLeft < 10 ? "0" + secondsLeft : secondsLeft}`;
                    }
                }

                if (secondsLeft == 3 && Configs.Login.IsLogin) {
                    Http.get(Configs.App.DOMAIN_CONFIG['GetAllBalance'], {}, (status, json) => {
                        if (status === 200) {
                            const goldFromApi = json['d'][0]['goldBalance'];
                            const coinFromApi = json['d'][0]['coinBalance'];
                            if (this.isCoinGold && goldFromApi > Configs.Login.GoldBalance) {
                                this.handleWinOrRefundAmount(goldFromApi - Configs.Login.GoldBalance);
                            } else if (!this.isCoinGold && coinFromApi > Configs.Login.CoinBalance) {
                                this.handleWinOrRefundAmount(coinFromApi - Configs.Login.CoinBalance);
                            }
                        }
                    });
                }

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownWaitingTime);
            }
        }, 1);
    }

    private handleRoomInfo(res: any[], isCurrent: boolean) {
        for (const room of res) {
            if (room == null) continue;
            if (room.GameID !== this.gameID) continue;

            const isGoldRoom = room.BetType === 1;
            const isXuRoom = room.BetType === 2;

            if ((this.isCoinGold && isXuRoom) || (!this.isCoinGold && isGoldRoom)) {
                continue;
            }

            // Update shared user info
            this.lblUserXiu.string = `(${Utils.formatNumber(room.TotalAccount1)})`;
            this.lblUserTai.string = `(${Utils.formatNumber(room.TotalAccount2)})`;

            if (isCurrent) {
                this.allTotalBetXiu += (room.TotalBetValue1 - this.currentTotalBetXiu);
                this.allTotalBetTai += (room.TotalBetValue2 - this.currentTotalBetTai);
                this.currentTotalBetXiu = room.TotalBetValue1;
                this.currentTotalBetTai = room.TotalBetValue2;
            } else {
                this.allTotalBetXiu = room.TotalBetValue1 + this.currentTotalBetXiu;
                this.allTotalBetTai = room.TotalBetValue2 + this.currentTotalBetTai;
            }

            this.lblTotalBetXiuCurrent.string = Utils.formatMoneyOnlyK(this.currentTotalBetXiu);
            this.lblTotalBetTaiCurrent.string = Utils.formatMoneyOnlyK(this.currentTotalBetTai);
            this.lblTotalBetXiuAll.string = Utils.formatMoneyOnlyK(this.allTotalBetXiu);
            this.lblTotalBetTaiAll.string = Utils.formatMoneyOnlyK(this.allTotalBetTai);
        }
    }

    private handleWinOrRefundAmount(amount: number) {
        this.winTextNode.active = true;
        this.winTextNode.getComponent(Label).string = '+ ' + Utils.formatNumber(amount);
        this.winTextNode.position = v3(this.winTextNode.x, -50);
        tween(this.winTextNode)
            .to(3, {position: v3(this.winTextNode.x, 150)})
            .call(() => {
                this.winTextNode.active = false;
            })
            .start();
    }

    actBet(_event: any, data: any) {
        if (!this.isBetting) {
            return;
        }
        var isXiu = data == "1";
        var betValue = isXiu ? this.editBoxBetXiu.string : this.editBoxBetTai.string;

        this.betTypeLocation = isXiu ? 1 : 2;

        this.hub.send("SetBetLD", [{
            GameID: this.gameID,
            CurrencyID: Configs.Login.CurrencyID,
            BetType: this.isCoinGold ? 1 : 2,
            Location: isXiu ? 1 : 2,
            Amount: parseInt(betValue)
        }], (res) => {
            if (res < 0) {
                this.showToast(App.instance.getTextLang(`me${res}`));
            }

            if (this.isCoinGold) {
                this.lastBetAmount = parseInt(betValue);
            } else {
                this.lastBetAmountXu = parseInt(betValue);
            }
            this.editBoxBetTai.string = "";
            this.editBoxBetXiu.string = "";
            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            this.layoutBet1.active = false;
            this.layoutBet2.active = false;
            TaiXiuDoubleController.instance.backgroundLayoutBet.active = false;
        });
    }

    hideLayoutBet() {
        this.layoutBet1.active = false;
        this.layoutBet2.active = false;
    }

    actShowLayOutBet() {
        if (!this.editBoxBetTai.enabled || !this.editBoxBetXiu.enabled) {
            return;
        }

        TaiXiuDoubleController.instance.backgroundLayoutBet.active = true;
        this.layoutBet1.active = true;
        this.layoutBet2.active = false;
    }

    actShowLayoutBetCustom() {
        this.layoutBet1.active = false;
        this.layoutBet2.active = true;
    }

    updateCurrentBetType(_event: Event, type: string) {
        this.currentBetType = type;
    }

    updateBetAmount(_event: Event, amount: string) {
        let currentAmount = 0;

        if (this.currentBetType == "1") {
            currentAmount = parseInt(this.editBoxBetXiu.string) || 0;
            this.editBoxBetXiu.string = (currentAmount + parseInt(amount)).toString();
        } else if (this.currentBetType == "2") {
            currentAmount = parseInt(this.editBoxBetTai.string) || 0;
            this.editBoxBetTai.string = (currentAmount + parseInt(amount)).toString();
        }
    }

    updateBetAmountCustom(_event: Event, amount: string) {
        if (this.currentBetType == "1") {
            this.editBoxBetXiu.string += amount;
        } else if (this.currentBetType == "2") {
            this.editBoxBetTai.string += amount;
        }
    }

    deleteBetAmount() {
        if (this.currentBetType == "1") {
            this.editBoxBetXiu.string = this.editBoxBetXiu.string.slice(0, -1);
        } else if (this.currentBetType == "2") {
            this.editBoxBetTai.string = this.editBoxBetTai.string.slice(0, -1);
        }
    }

    x2Bet() {
        var lastBetAmount = this.isCoinGold ? this.lastBetAmount : this.lastBetAmountXu;
        var currentBetAmountXiu = parseInt(this.editBoxBetXiu.string) || 0;
        var currentBetAmountTai = parseInt(this.editBoxBetTai.string) || 0;

        if (!this.isBetting || (lastBetAmount == 0 && currentBetAmountTai == 0 && currentBetAmountXiu == 0)) return;
        if (this.currentBetType == "1") {
            if (currentBetAmountXiu > 0) {
                this.editBoxBetXiu.string = (currentBetAmountXiu * 2).toString();
            } else {
                this.editBoxBetXiu.string = (lastBetAmount * 2).toString();
            }
        } else if (this.currentBetType == "2") {
            if (currentBetAmountTai > 0) {
                this.editBoxBetTai.string = (currentBetAmountTai * 2).toString();
            } else {
                this.editBoxBetTai.string = (lastBetAmount * 2).toString();
            }
        }
    }

    actCancel() {
        this.clearBetAmount();
    }

    clearBetAmount() {
        if (this.currentBetType == "1") {
            this.editBoxBetXiu.string = "";
        } else if (this.currentBetType == "2") {
            this.editBoxBetTai.string = "";
        }
    }

    actAgree() {
        if (!this.isBetting) {
            return;
        }

        let betValue = "";

        if (this.currentBetType == "1") {
            betValue = this.editBoxBetXiu.string;
        } else if (this.currentBetType == "2") {
            betValue = this.editBoxBetTai.string;
        }

        if (!betValue || parseInt(betValue) <= 0) {
            return;
        }

        if (this.currentBetType == "1") {
            this.actBet(null, "1");
        } else if (this.currentBetType == "2") {
            this.actBet(null, "2");
        }
    }

    actClose() {
        TaiXiuDoubleController.instance.dismiss();
    }

    actNan(event: any) {
        var target = event.target;
        var on = target.getChildByName('on');
        var off = target.getChildByName('off');
        on.active = !on.active;
        off.active = !off.active;
        this.isNan = !this.isNan;
    }

    public actSwitchCoin() {
        this.isCoinGold = !this.isCoinGold;
        this.coinGold.active = this.isCoinGold;
        this.coinSliver.active = !this.isCoinGold;
        this.resetSessionLabels();
        this.lblBetTai.node.active = this.isCoinGold;
        this.lblBetXiu.node.active = this.isCoinGold;
        this.lblBetTaiXu.node.active = !this.isCoinGold;
        this.lblBetXiuXu.node.active = !this.isCoinGold;
    }

    private resetSessionLabels() {
        this.resetLabels.forEach(label => label.string = "0");
        this.lblUserTai.string = "(0)";
        this.lblUserXiu.string = "(0)";
        this.currentTotalBetTai = 0;
        this.currentTotalBetXiu = 0;
        this.allTotalBetTai = 0;
        this.allTotalBetXiu = 0;
    }

    actPopupHistorySession(sessionId: number, detailSessions: any[]) {
        if (this.popupDetailSession == null) {
            this.popupDetailSession = instantiate(this.popupDetailHistory).getComponent(PopupDetailSession);
            this.popupDetailSession.node.parent = this.popupContainer;
            this.popupDetailSession.showDetail(sessionId, this.gameID, detailSessions);
            App.instance.showLoading(false);
        } else {
            this.popupDetailSession.showDetail(sessionId, this.gameID, detailSessions);
        }
    }

    private showResult() {
        var nodeResult: Node;
        if (this.lastLocationIDWin === 1) {
            nodeResult = this.nodeXiu;
        } else if (this.lastLocationIDWin === 2) {
            nodeResult = this.nodeTai;
        } else {
            return;
        }
        this.hideResult();
        tween(nodeResult)
            .repeatForever(
                tween()
                    .to(0.25, { scale: v3(1.25, 1.25, 1.25) }, { easing: "quadOut" })
                    .to(0.2, { scale: v3(0.9, 0.9, 0.9) }, { easing: "quadIn" })
                    .to(0.15, { scale: v3(1.1, 1.1, 1.1) }, { easing: "sineOut" })
                    .to(0.1, { scale: v3(1.0, 1.0, 1.0) }, { easing: "sineInOut" })
            )
            .start();

        this.lblRemainWaiting.node.parent.active = true;
        this.lblRemainWaiting.node.active = false;
        this.lblSumDices.string = this.lastScore.toString();

        if (!this.isShowWheel) {
            return;
        }

        this.scheduleOnce(() => {
            this.nodeWheelSpin.active = true;
            this.handleJackpotFund(this.jackpotInfo ? this.jackpotInfo.JackpotLocationID : 0);

            this.scheduleOnce(() => {
                this.nodeJackPot.active = false;
                this.nodeWheelSpin.active = false;
            }, 12);
        }, 3);
    }

    handleJackpotFund(locationID: number) {
        let baseAngle = 0;

        if (locationID === 1) {
            baseAngle = 120;
        } else if (locationID === 2) {
            baseAngle = 60;
        }

        Tween.stopAllByTarget(this.wheelNode);
        this.wheelNode.angle = this.wheelNode.angle % 360;
        let totalRounds = 8;
        let targetAngle = 360 * totalRounds + baseAngle;

        tween(this.wheelNode)
            .to(4, {angle: -targetAngle}, {easing: "quartOut"})
            .call(() => {
                if (this.betTypeLocation === 0 || this.betTypeLocation !== locationID) {
                    return;
                }

                this.nodeJackPot.active = true;
                this.lblJackPot.getComponent(Label).string = Utils.formatNumber(this.jackpotInfo.JackpotFund);
            })
            .start();
    }

    private hideResult() {
        this.lblRemainWaiting.node.parent.active = false;
        Tween.stopAllByTarget(this.nodeTai);
        Tween.stopAllByTarget(this.nodeXiu);
        this.nodeTai.setScale(v3(1, 1, 1));
        this.nodeXiu.setScale(v3(1, 1, 1));
    }

    private showToast(message: string) {
        this.lblToast.string = message;
        let parent = this.lblToast.node.parent;
        Tween.stopAllByTarget(parent);
        parent.active = true;
        let uiOpacity = parent.getComponent(UIOpacity);
        if (!uiOpacity) {
            uiOpacity = parent.addComponent(UIOpacity);
        }
        uiOpacity.opacity = 0;
        tween(uiOpacity)
            .to(0.1, { opacity: 255 })  // fadeIn 0.1s
            .delay(2.0)                 // delay 2s
            .to(0.2, { opacity: 0 })    // fadeOut 0.2s
            .call(() => {
                parent.active = false;
            })
            .start();
    }
}
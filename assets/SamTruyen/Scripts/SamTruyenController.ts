import { _decorator, Component, easing, math, Node, tween, Vec3 } from "cc";
const { ccclass, property } = _decorator;

@ccclass("SamTruyenController")
export class SamTruyenController extends Component {
  @property(Node)
  Content: Node;

  @property(Node)
  Line: Node;

  private delayBetween: number = 0.2;

  private spinDuration: number = 1;

  private columnHeight: number = 542;
  private isSpinning: boolean = false;

  private isFastSpinMode: boolean = false;

  private spinAllColumns() {
    if (this.isSpinning) return;
    this.isSpinning = true;
    if (this.Line) {
      this.Line.active = false;
    }
    const columns = this.Content.children;
    if (columns && columns.length) {
      for (let i = 0; i < columns.length; i++) {
        this.scheduleOnce(() => {
          this.spinColumn(columns[i], i === 4);
        }, i * this.delayBetween);
      }
    }
  }

  spinColumn(column: Node, isShowLine: boolean) {
    if (!column) return;
    const startY = column.position.y;
    const endY = startY - this.columnHeight * 3;

    tween(column)
      .to(this.spinDuration, { position: new Vec3(column.position.x, endY, column.position.z) }, { easing: "linear" })
      .call(() => {
        column.setPosition(new Vec3(column.position.x, startY, column.position.z));
        if (isShowLine) {
          this.scheduleOnce(() => {
            this.showLine(Math.floor(math.random() * 25));
          }, 1);
        }
      })
      .start();
  }

  private showLine(lineNum: number) {
    if (this.Line) {
      this.isSpinning = false;
      this.Line.active = true;
      const lines = this.Line.children;
      if (lines && lines.length) {
        lines.forEach((e, i) => {
          e.active = i === lineNum;
        });
      }
    }
  }

  private setFastMode() {
    this.isFastSpinMode = !this.isFastSpinMode;
    this.delayBetween = this.isFastSpinMode ? 0.1 : 0.2;
    this.spinDuration = this.isFastSpinMode ? 0.5 : 1;
    console.log("Fast mode: ", this.isFastSpinMode);
  }
}

import { _decorator, Component, math, Node } from "cc";
import { SamTruyenBonusButtonItemController } from "./SamTruyenBonusButtonItemController";
const { ccclass, property } = _decorator;

@ccclass("SamTruyenScatterContrller")
export class SamTruyenScatterContrller extends Component {
  @property(Node)
  private bonusSelect: Node;

  @property(SamTruyenBonusButtonItemController)
  private bonusItems: SamTruyenBonusButtonItemController[] = [];

  protected start(): void {
    this.showBonusSelect();
  }

  showBonusSelect() {
    if (this.bonusSelect) {
      this.bonusSelect.active = true;
      if (this.bonusItems && this.bonusItems.length) {
        this.bonusItems.forEach((e, i) => {
          e.setData(i + 1, () => {
            this.lockAllBonus();
            this.scheduleOnce(() => {
              if (this.bonusSelect) {
                this.bonusSelect.active = false;
              }
            }, 5);
          });
        });
      }
    }
  }

  lockAllBonus() {
    if (this.bonusItems && this.bonusItems.length) {
      this.bonusItems.forEach((e) => {
        e.setLocking();
      });
      this.scheduleOnce(() => {
        this.bonusItems.forEach((e) => {
          e.openBonus();
        });
      }, 2);
    }
  }
}

import { _decorator, Component, Node, Animation, Input } from "cc";
const { ccclass, property } = _decorator;

enum SCOPION_STATE {
  NONE = "none",
  INTRO = "ScopionIntro",
  IDLE = "ScopionIdle",
  HURT = "ScopionHurt",
  ATTACK = "ScopionAttack",
  EXIT = "ScopionExit",
}

@ccclass("SamTruyenScopionController")
export class SamTruyenScopionController extends Component {
  @property(Animation)
  private Ground: Animation;

  @property(Animation)
  private Blood: Animation;

  private currentState: SCOPION_STATE = SCOPION_STATE.NONE;
  private anim: Animation;
  private isLocking: boolean = false;
  private isDone: boolean = false;

  protected onLoad(): void {
    this.anim = this.getComponent(Animation);
    this.node.on(Input.EventType.TOUCH_START, this.onTouch, this);
  }

  protected onDestroy(): void {
    this.anim.off(
      Animation.EventType.FINISHED,
      () => {
        this.onFinishStateAnimation();
      },
      this
    );
  }

  protected start(): void {
    this.setState(SCOPION_STATE.INTRO);
    this.scheduleOnce(() => {
      this.setState(SCOPION_STATE.ATTACK);
      this.isDone = true;
    }, 7);
  }

  private onTouch() {
    if (this.isDone) return;
    if (this.isLocking) return;
    this.isLocking = true;
    this.scheduleOnce(() => {
      this.isLocking = false;
    }, 1);
    this.setState(SCOPION_STATE.HURT);
    if (this.Blood) {
      this.Blood.node.active = true;
      const clip = this.Blood.getState("Blood");
      if (clip) {
        this.Blood.play("Blood");
        this.Blood.once(
          Animation.EventType.FINISHED,
          () => {
            this.Blood.node.active = false;
          },
          this
        );
      }
    }
  }

  setState(state: SCOPION_STATE) {
    if (state && state !== this.currentState) {
      this.currentState = state;
      if (this.anim) {
        const clip = this.anim.getState(this.currentState);
        if (clip) {
          this.anim.play(this.currentState);
          this.anim.once(
            Animation.EventType.FINISHED,
            () => {
              this.onFinishStateAnimation();
            },
            this
          );
        }
      }
      if (state === SCOPION_STATE.INTRO || state === SCOPION_STATE.EXIT) {
        if (this.Ground) {
          this.Ground.node.active = true;
          const clip = this.Ground.getState("Ground");
          if (clip) {
            this.Ground.play("Ground");
            this.Ground.once(
              Animation.EventType.FINISHED,
              () => {
                this.Ground.node.active = false;
              },
              this
            );
          }
        }
      }
    }
  }

  private onFinishStateAnimation() {
    if (this.anim) {
      if (this.currentState == SCOPION_STATE.HURT || this.currentState == SCOPION_STATE.INTRO) {
        this.setState(SCOPION_STATE.IDLE);
      } else if (this.currentState == SCOPION_STATE.ATTACK) {
        this.setState(SCOPION_STATE.EXIT);
      } else if (this.currentState == SCOPION_STATE.EXIT) {
      }
    }
  }
}

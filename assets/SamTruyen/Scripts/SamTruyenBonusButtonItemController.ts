import { _decorator, Component, math, Node, random, Sprite, Sprite<PERSON>rame, tween, Vec3 } from "cc";
const { ccclass, property } = _decorator;

@ccclass("SamTruyenBonusButtonItemController")
export class SamTruyenBonusButtonItemController extends Component {
  @property(SpriteFrame)
  bonusSprites: SpriteFrame[] = [];

  @property(Node)
  effect: Node;

  private bonusSprite: Sprite;
  private isLocking: boolean = false;
  private isOpened: boolean = false;
  private bonusId: number;
  private callback: () => void;

  protected onLoad(): void {
    this.bonusSprite = this.getComponent(Sprite);
  }

  setData(id: number, cb: () => void) {
    this.bonusId = id;
    this.isLocking = false;
    this.isOpened = false;
    if (this.bonusSprite && this.bonusSprites[0]) {
      this.bonusSprite.spriteFrame = this.bonusSprites[0];
    }
    if (this.effect) {
      this.effect.active = false;
    }
    this.callback = cb;
  }

  openBonus() {
    if (this.isOpened) return;
    tween(this.node)
      .to(0.5, { scale: new Vec3(0, 1, 1) }, { easing: "bounceIn" })
      .call(() => {
        if (this.bonusSprite && this.bonusSprites[this.bonusId]) {
          this.bonusSprite.spriteFrame = this.bonusSprites[this.bonusId];
        }
        tween(this.node).to(0.5, { scale: Vec3.ONE }, { easing: "bounceOut" }).start();
      })
      .start();
  }

  setLocking() {
    this.isLocking = true;
  }

  onClick() {
    if (this.isLocking) return;
    this.isLocking = true;
    this.isOpened = true;
    this.callback && this.callback();
    tween(this.node)
      .to(0.5, { scale: new Vec3(0, 1, 1) }, { easing: "bounceIn" })
      .call(() => {
        if (this.bonusSprite && this.bonusSprites[this.bonusId]) {
          this.bonusSprite.spriteFrame = this.bonusSprites[this.bonusId];
        }
        tween(this.node)
          .to(0.5, { scale: Vec3.ONE }, { easing: "bounceOut" })
          .call(() => {
            if (this.effect) {
              this.effect.active = true;
            }
            this.node.setSiblingIndex(0);
          })
          .start();
      })
      .start();
  }
}

import { _decorator, Component, Node, Sprite, Label, Prefab, instantiate, Event, Button, EditBox, ToggleContainer, SpriteFrame, Vec2, v2, v3, tween, WebView, Animation, EventTouch, Tween, UIOpacity } from 'cc';
import SignalRClient from '../../Lobby/scripts/common/networks/Network.SignalRClient';
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import MiniGameTX1SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX1SignalRClient";
import MiniGameTX3SignalRClient from '../../Lobby/scripts/common/networks/MiniGameTX3SignalRClient';
import ChatHubSignalRClient from "db://assets/Lobby/scripts/common/networks/ChatHubSignalRClient";
import MiniGameTX2SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX2SignalRClient";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import ButtonMiniGame from "db://assets/Lobby/scripts/common/ButtonMiniGame";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("TaiXiuLiveJP/Controller")
export default class TaiXiuLiveJPController extends Component {

    static instance: TaiXiuLiveJPController = null;

    @property(Sprite)
    avatar: Sprite = null;
    @property(Label)
    labelBalance: Label = null;
    @property(Label)
    labelNickname: Label = null;

    // 1: Gold 2: Xu
    private isBetTypeGold = true;

    // Box
    @property(Node)
    chatBox: Node = null;
    @property(Node)
    settingBox: Node = null;
    @property(Node)
    statisticalBox: Node = null;

    // Popup Container
    @property(Node)
    popupContainer: Node = null;
    @property(Prefab)
    popupEventTienTriPrefab: Prefab = null;
    @property(Prefab)
    popupEventSHPrefab: Prefab = null;
    @property(Prefab)
    popupEventJackpotPrefab: Prefab = null;
    @property(Prefab)
    popupGuidePrefab: Prefab = null;
    @property(Prefab)
    popupHistoryPrefab: Prefab = null;
    @property(Prefab)
    popupEventHonorPrefab: Prefab = null;
    @property(Prefab)
    popupDetailSessionPrefab: Prefab = null;

    private popupEventTienTri: any = null;
    private popupEventSH: any = null;
    private popupEventJackpot: any = null;
    private popupHonor: any = null;
    private popupHistory: any = null;
    private popupGuide: any = null;
    private popupDetailSession = null;

    private currentGameIds = [1, 2, 3]; // center, left, right
    private nameTX = ["HK", "LVG", "MC"];

    @property(Node)
    leftContainer: Node = null;
    @property(Node)
    rightContainer: Node = null;
    @property(Node)
    centerLabels: Node = null;

    // GAME FIELDS
    @property(SpriteFrame)
    sprFrameTai: SpriteFrame = null;
    @property(SpriteFrame)
    sprFrameXiu: SpriteFrame = null;
    @property(Label)
    lblSession: Label = null;
    @property(Node)
    containerTimer: Node = null;
    @property(Label)
    lblRemainTime: Label = null;
    @property(Label)
    lblRemainWaiting: Label = null;
    @property(Label)
    lblSumDices: Label = null;
    @property(Label)
    lblTotalBetTaiCurrent: Label = null;
    @property(Label)
    lblTotalBetXiuCurrent: Label = null;
    @property(Label)
    lblBetTai: Label = null;
    @property(Label)
    lblBetXiu: Label = null;
    @property(Label)
    lblUserTai: Label = null;
    @property(Label)
    lblUserXiu: Label = null;

    @property(Node)
    dicesContainer: Node = null;
    @property([SpriteFrame])
    listSprDice: SpriteFrame[] = [];
    @property(Node)
    gateTaiWin: Node = null;
    @property(Node)
    nodeTaiWin: Node = null;
    @property(Node)
    gateXiuWin: Node = null;
    @property(Node)
    nodeXiuWin: Node = null;
    @property(Node)
    bowl: Node = null;
    @property(Button)
    buttonNan: Button = null;
    @property(Label)
    lblToast: Label = null;
    @property(Node)
    historyList: Node = null;
    @property(Node)
    historyItem: Node = null;
    detailSessions = [];

    // JACKPOT
    @property(Node)
    nodeWheelSpin: Node = null;
    @property(Node)
    nodeJackPot: Node = null;
    @property(Node)
    lblJackPot: Node = null;
    @property(Node)
    wheelNode: Node = null;

    // LIVE
    @property(Node)
    liveNode: Node = null;
    @property(WebView)
    webView: WebView = null;
    @property(Node)
    liveNodeMini: Node = null;
    @property(WebView)
    webViewMini: WebView = null;
    private streamURL: string = "";

    private isCoinGold: boolean = true;
    private lastBetAmount: number = 0;

    private isBetting = false;
    private isNan = false;
    private lastLocationIDWin = 0;
    private lastScore = 0;
    private readonly bowlStartPos = v2(0, 25);

    @property(EditBox)
    editBoxBetTai: EditBox = null;
    @property(EditBox)
    editBoxBetXiu: EditBox = null;
    @property(Node)
    confirmBetButton: Node = null;
    @property(Node)
    cancelBetButton: Node = null;
    @property(ToggleContainer)
    chipToggleContainer: ToggleContainer = null;

    private gameID: number;
    private hub: SignalRClient = null;
    private betTypeLocation: number = 0;
    private selectedChipValue: number = 0;

    @property(Label)
    labelJackpotBalance: Label = null;
    @property(Node)
    winTextTaiNode: Node = null;
    @property(Node)
    winTextXiuNode: Node = null;

    @property(Node)
    layoutBetChip: Node = null;
    @property(Node)
    layoutBetNumber: Node = null;

    private countdownRemainTime: Function = null;
    private countdownWaitingTime: Function = null;
    private isOpenBowl = false;
    private isShowWheel = false;
    private jackpotInfo: any = null;
    private readonly STREAM_TEMPLATE = "https://demo.nanocosmos.de/nanoplayer/embed/1.3.3/nanoplayer.html?group.id=STREAM_ID&group.security.jwtoken=JWT_TOKEN";
    private currentStreamID = -1;

    public onEnable() {
        TaiXiuLiveJPController.instance = this;
    }

    public start() {
        this.settingBox.active = false;
        this.statisticalBox.active = false;
        this.chatBox.active = false;

        this.labelNickname.string = Configs.Login.Nickname;
        this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
        this.avatar.spriteFrame = App.instance.getAvatarSpriteFrame(Configs.Login.Avatar);
        this.gameID = this.getCurrentGameID();
        this.changeServer();
        this.bowl.on(
            Node.EventType.TOUCH_MOVE,
            (event: EventTouch) => {
                var pos = this.bowl.getPosition();
                pos.x += event.getDeltaX();
                pos.y += event.getDeltaY();
                this.bowl.position = pos;

                let distance = Utils.v2Distance(
                    new Vec2(pos.x, pos.y),
                    this.bowlStartPos
                );
                if (Math.abs(distance) > 200) {
                    this.showResult();
                    this.isOpenBowl = true;
                    this.scheduleOnce(() => {
                        this.bowl.active = false;
                    }, 2);
                }
            },
            this
        );
        this.bowl.active = false;
    }

    getStreamURL() {
        Http.get('https://gameapi-alpha.bavenoth.com/api/v1/global/livestreaminfo', {gameId: 117}, (status, res) => {
            if (res.c < 0 || status !== 200) {
                return;
            }

            var streamObj = res.d.find((item: any) => item.gameID === 117 && item.roomID === this.getCurrentGameID());
            if (streamObj && streamObj.streamID && streamObj.tokenPlayBack) {
                if (this.currentStreamID !== streamObj.streamID) {
                    this.currentStreamID = streamObj.streamID;
                    this.streamURL = this.STREAM_TEMPLATE.replace("STREAM_ID", streamObj.streamID).replace("JWT_TOKEN", streamObj.tokenPlayBack);
                }
            } else {
                this.streamURL = "";
            }
        });
    }

    protected update(_dt: number) {
        if (this.streamURL === "") {
            this.liveNode.active = false;
            this.liveNodeMini.active = false;
            return;
        }

        if (this.webView.url !== this.streamURL) {
            this.webView.url = this.streamURL;
            this.webViewMini.url = this.streamURL;
        }

        let isShowWebView = true;
        let isShowMiniWebView = false;

        for (let child of this.popupContainer.children) {
            if (child.active) {
                isShowWebView = false;
                break;
            }
        }

        if (isShowWebView) {
            for (let child of App.instance.miniGameNode.children) {
                if (child.active) {
                    isShowWebView = false;
                    isShowMiniWebView = true;
                    break;
                }
            }
        }

        for (let child of App.instance.tipzoJackpotEventX2X6Node.children) {
            if (child.active) {
                isShowWebView = false;
                break;
            }
        }

        if (ButtonMiniGame.instance.panel.active) {
            isShowWebView = false;
        }

        this.liveNode.active = isShowWebView;
        if (isShowMiniWebView) {
            if (App.instance.tipzoMiniLiveNode.children.length == 0) {
                var mini = instantiate(this.liveNodeMini);
                mini.active = true;
                App.instance.tipzoMiniLiveNode.addChild(mini);
            }
        } else {
            App.instance.tipzoMiniLiveNode.removeAllChildren();
        }
    }

    onDestroy() {
        MiniGameTX1SignalRClient.getInstance().dontReceive();
        MiniGameTX2SignalRClient.getInstance().dontReceive();
        MiniGameTX3SignalRClient.getInstance().dontReceive();
        ChatHubSignalRClient.getInstance().dontReceive();
        App.instance.tipzoMiniLiveNode.removeAllChildren();
        Utils.setStorageValue("last_open_game_id", "");
    }

    leaveRoom() {
        TaiXiuLiveJPController.instance = null;
        this.node.destroy();
    }

    getCurrentGameID(): number {
        return this.currentGameIds[0];
    }

    changeServer() {
        if (this.hub) {
            this.hub.send("HideLD", [{GameID: this.gameID}], () => {});
        }

        this.resetForNew();
        this.unscheduleAllCallbacks();
        this.getStreamURL();
        this.gameID = this.getCurrentGameID();
        if (this.gameID == 1) {
            this.hub = MiniGameTX1SignalRClient.getInstance();
        } else if (this.gameID == 2) {
            this.hub = MiniGameTX2SignalRClient.getInstance();
        } else if (this.gameID == 3) {
            this.hub = MiniGameTX3SignalRClient.getInstance();
        }

        this.hub.send("GetCurrentRoomsLD", [{GameID: this.gameID, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
        this.initHubs();
    }

    onClickLeft() {
        this.currentGameIds = [this.currentGameIds[1], this.currentGameIds[0], this.currentGameIds[2]];
        var leftGameID = this.currentGameIds[1];
        var centerGameID = this.currentGameIds[0];

        this.nameTX.forEach((name, index) => {
            this.leftContainer.getChildByName(name).active = index === leftGameID - 1;
            this.centerLabels.getChildByName(name).active = index === centerGameID - 1;
        })

        this.changeServer();
    }

    onClickRight() {
        this.currentGameIds = [this.currentGameIds[2], this.currentGameIds[1], this.currentGameIds[0]];
        var rightGameID = this.currentGameIds[2];
        var centerGameID = this.currentGameIds[0];

        this.nameTX.forEach((name, index) => {
            this.rightContainer.getChildByName(name).active = index === rightGameID - 1;
            this.centerLabels.getChildByName(name).active = index === centerGameID - 1;
        })

        this.changeServer();
    }

    getLuckyDiceJackPot() {
        this.labelJackpotBalance.string = "0";
        Http.get(Configs.App.DOMAIN_CONFIG['LuckyDiceJackPot'], {gameID: this.gameID}, (status, res) => {
            if (res.c < 0) {
                return;
            }

            this.labelJackpotBalance.string = Utils.formatNumber(res.d);
        });
    }

    initHubs() {
        this.getLuckyDiceJackPot();

        this.hub.receive("currentSessionLD", (res) => {
            if (res.GameID !== this.gameID) return;

            this.lblSession.string = `#${res.GameSessionID}`;
            this.unschedule(this.countdownRemainTime);
            this.unschedule(this.countdownWaitingTime);
            this.confirmBetButton.active = false;
            this.cancelBetButton.active = false;
            if (res.GameStatus === 1) {
                this.getStreamURL();
                this.handleBettingPhase(res.RemainBetting);
            } else {
                this.handleWaitingPhase(res.RemainWaiting);
                this.hub.send("GetAccountResultLD", [{GameID: this.gameID, CurrencyID: Configs.Login.CurrencyID, GameSessionID: res.GameSessionID}], () => {});
            }
        });

        this.hub.receive("currentResultLD", (res) => {
            if (res.GameID != this.gameID) {
                return;
            }
            this.confirmBetButton.active = false;
            this.cancelBetButton.active = false;
            this.editBoxBetTai.enabled = false;
            this.editBoxBetXiu.enabled = false;
            this.buttonNan.enabled = false;
            const aminResult = this.dicesContainer.getChildByName('anim');
            const resultNode = this.dicesContainer.getChildByName('result');
            resultNode.active = false;
            aminResult.active = true;
            const dice_1 = resultNode.getChildByName('dice_1');
            const dice_2 = resultNode.getChildByName('dice_2');
            const dice_3 = resultNode.getChildByName('dice_3');
            dice_1.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice1 - 1];
            dice_2.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice2 - 1];
            dice_3.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice3 - 1];
            this.isShowWheel = (res.Dice1 == 1 && res.Dice2 == 1 && res.Dice3 == 1) || (res.Dice1 == 6 && res.Dice2 == 6 && res.Dice3 == 6);
            this.jackpotInfo = res.JackPotInfo || null;
            this.dicesContainer.active = true;
            // const anim = aminResult.getComponent(Animation);
            // anim.play();
            const anim1 = aminResult.getChildByName('animDice1').getComponent(Animation);
            const anim2 = aminResult.getChildByName('animDice2').getComponent(Animation);
            const anim3 = aminResult.getChildByName('animDice3').getComponent(Animation);

            anim1.play();
            anim2.play();
            anim3.play();

            this.scheduleOnce(() => {
                anim1.stop();
                anim2.stop();
                anim3.stop();
                aminResult.active = false;

                this.lastLocationIDWin = res.LocationIDWin;
                this.lastScore = res.Dice1 + res.Dice2 + res.Dice3;
                if (this.isNan) {
                    this.bowl.setPosition(v3(this.bowlStartPos.x, this.bowlStartPos.y, 0));
                    this.bowl.active = true;

                    this.scheduleOnce(() => {
                        if (this.isOpenBowl) {
                            return;
                        }
                        this.isOpenBowl = true;
                        tween(this.bowl)
                            .to(0.5, {position: v3(250, 150, 0)})
                            .call(() => {
                                this.showResult();
                                this.scheduleOnce(() => {
                                    this.bowl.active = false;
                                }, 2);
                            })
                            .start();
                    }, 12);
                } else {
                    this.showResult();
                }
                resultNode.active = true;
            }, 3);
        });

        this.hub.receive("currentRoomsInfoLD", (res) => {
            for (const room of res) {
                if (room == null) continue;
                if (room.GameID !== this.gameID) continue;

                const isGoldRoom = room.BetType === 1;
                const isXuRoom = room.BetType === 2;

                if ((this.isCoinGold && isXuRoom) || (!this.isCoinGold && isGoldRoom)) {
                    continue;
                }

                this.lblUserXiu.string = `(${Utils.formatNumber(room.TotalAccount1)})`;
                this.lblUserTai.string = `(${Utils.formatNumber(room.TotalAccount2)})`;

                this.lblTotalBetXiuCurrent.string = Utils.formatNumber(room.TotalBetValue1);
                this.lblTotalBetTaiCurrent.string = Utils.formatNumber(room.TotalBetValue2);
            }
        });

        this.hub.receive("gameHistoryLD", (res) => {
            if (res == null || res.length == 0) {
                return;
            }
            this.detailSessions = [];
            this.historyList.removeAllChildren();
            for (let i = res.length - 1; i >= 0; i--) {
                this.detailSessions.push(res[i]);

                let item = instantiate(this.historyItem);
                item.getComponent(Sprite).spriteFrame = res[i].LocationIDWin == 1 ? this.sprFrameXiu : this.sprFrameTai;
                item.getChildByName("last").active = i === 0;
                item.on(Node.EventType.TOUCH_END, () => {
                    this.actPopupDetailSession(res[i].GameSessionID);
                });

                if (i === 0) {
                    Tween.stopAllByTarget(item);
                    this.scheduleOnce(() => {
                        const posUp = v3(item.position.x, 5, item.position.z);
                        const posDown = v3(item.position.x, -5, item.position.z);

                        tween(item)
                            .repeatForever(
                                tween()
                                    .to(0.3, { position: posUp })
                                    .to(0.3, { position: posDown })
                            )
                            .start();
                    }, 0);
                }

                this.historyList.addChild(item);
            }
        });

        this.hub.receive("betOfAccountLD", (res) => {
            res.forEach((item: any) => {
                if (item.GameID != this.gameID) {
                    return;
                }

                this.editBoxBetTai.string = '';
                this.editBoxBetXiu.string = '';

                if (item.BetType == 1) {
                    const betLocation = item.LocationID == 1 ? this.lblBetXiu : this.lblBetTai;
                    betLocation.string = Utils.formatNumber(item.BetValue);
                }
            })
        });

        this.hub.receive("resultOfAccountLD", (res) => {
            if (this.betTypeLocation === 0) return;
            let totalPrize = 0;

            res.forEach((item: any) => {
                if (item.GameID === this.gameID && item.PrizeValue > 0) {
                    totalPrize += item.PrizeValue;
                }
            });

            const winTextNode = this.betTypeLocation === 1 ? this.winTextXiuNode : this.winTextTaiNode;

            if (totalPrize <= 0) {
                winTextNode.active = false;
                return;
            }

            winTextNode.active = true;
            winTextNode.getComponentInChildren(Label).string = '+ ' + Utils.formatNumber(totalPrize);
            winTextNode.position = v3(winTextNode.x, -60);
            tween(winTextNode)
                .to(3, {position: v3(winTextNode.x, 60)})
                .call(() => {
                    winTextNode.active = false;
                })
                .start();

            this.updateBalanceTaiXiu();
        });
    }

    private resetForNew() {
        this.getLuckyDiceJackPot();
        this.dicesContainer.active = false;
        this.isOpenBowl = false;
        this.lblBetTai.string = "0";
        this.lblBetXiu.string = "0";
        this.betTypeLocation = 0;
        this.lastScore = 0;
        this.lblSumDices.string = "";
        this.lblRemainTime.string = "";
        this.confirmBetButton.active = false;
        this.cancelBetButton.active = false;
        this.bowl.active = false;
        this.clearChipToggle();
        this.hideResult();
        this.actCancelBet();
    }

    private handleBettingPhase(remainTime: number) {
        this.containerTimer.active = true;

        if (remainTime === 60) {
            this.showToast(App.instance.getTextLang("txt_taixiu_new_session"));
            this.resetForNew();
        }

        if (remainTime < 3) {
            this.isBetting = false;
        }

        this.isBetting = true;
        this.editBoxBetTai.enabled = true;
        this.editBoxBetXiu.enabled = true;
        this.buttonNan.enabled = true;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblRemainWaiting.node.active = false;

        let secondsLeft = remainTime;
        this.unschedule(this.countdownRemainTime);
        this.schedule(this.countdownRemainTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownRemainTime);
                    return;
                }

                if (secondsLeft <= 5) {
                    this.isBetting = false;
                }

                this.lblRemainTime.string = secondsLeft < 10 ? `0${secondsLeft}` : `${secondsLeft}`;

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownRemainTime);
            }
        }, 1);
    }

    private handleWaitingPhase(waitingTime: number) {
        if (waitingTime > 0) this.isBetting = false;

        if (waitingTime < 19) {
            this.hub.send("GetCurrentResultLD", [{ GameID: this.gameID }], () => {});
        }

        this.containerTimer.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        let secondsLeft = waitingTime;
        this.unschedule(this.countdownWaitingTime);
        this.schedule(this.countdownWaitingTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownWaitingTime);
                    return;
                }

                if (this.isNan && this.isOpenBowl === false && secondsLeft > 15) {
                    const secondsLeftOpenBowl = secondsLeft - 15;
                    this.lblRemainWaiting.string = `${secondsLeftOpenBowl < 10 ? "0" + secondsLeftOpenBowl : secondsLeftOpenBowl}`;
                    this.lblRemainWaiting.node.active = true;
                    this.lblRemainWaiting.node.parent.active = true;
                    this.lblSumDices.node.active = false;
                } else {
                    this.lblSumDices.node.active = secondsLeft > 12;
                    this.lblRemainWaiting.node.active = secondsLeft <= 12;
                    this.lblRemainWaiting.string = `${secondsLeft < 10 ? "0" + secondsLeft : secondsLeft}`;
                }

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownWaitingTime);
            }
        }, 1);
    }

    actBet() {
        if (!this.isBetting) {
            this.showToast(App.instance.getTextLang("me-207"));
            this.actCancelBet();
            return;
        }
        if (this.betTypeLocation == 0) {
            return;
        }
        var betValue = this.betTypeLocation == 1 ? this.editBoxBetXiu.string : this.editBoxBetTai.string;
        if (parseInt(betValue) <= 0) {
            this.showToast(App.instance.getTextLang("txt_taixiu_enter_bet_value")); //@TODO
            return;
        }

        this.hub.send("SetBetLD", [{
            GameID: this.gameID,
            CurrencyID: Configs.Login.CurrencyID,
            BetType: this.isCoinGold ? 1 : 2,
            Location: this.betTypeLocation,
            Amount: parseInt(betValue)
        }], (res) => {
            if (res < 0) {
                this.showToast(App.instance.getTextLang(`me${res}`));
                this.actCancelBet();
                return;
            }

            this.showToast(App.instance.getTextLang("tx3_live").replace('{0}', this.betTypeLocation === 1 ? App.instance.getTextLang("tx44") : App.instance.getTextLang("tx43")));

            this.lastBetAmount = parseInt(betValue);
            this.editBoxBetTai.string = "";
            this.editBoxBetXiu.string = "";
            this.gateXiuWin.active = false;
            this.gateTaiWin.active = false;
            this.clearChipToggle();
            this.updateBalanceTaiXiu();
        });
    }

    x2Bet() {
        var lastBetAmount = this.lastBetAmount;
        var currentBetAmountXiu = parseInt(this.editBoxBetXiu.string) || 0;
        var currentBetAmountTai = parseInt(this.editBoxBetTai.string) || 0;

        if (!this.isBetting || (lastBetAmount == 0 && currentBetAmountTai == 0 && currentBetAmountXiu == 0)) return;
        if (this.betTypeLocation == 1) {
            if (currentBetAmountXiu > 0) {
                this.editBoxBetXiu.string = (currentBetAmountXiu * 2).toString();
            } else {
                this.editBoxBetXiu.string = (lastBetAmount * 2).toString();
            }
        } else if (this.betTypeLocation == 2) {
            if (currentBetAmountTai > 0) {
                this.editBoxBetTai.string = (currentBetAmountTai * 2).toString();
            } else {
                this.editBoxBetTai.string = (lastBetAmount * 2).toString();
            }
        }
        this.confirmBetButton.active = true;
        this.cancelBetButton.active = true;
    }

    updateBalanceTaiXiu() {
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
    }

    updateCurrentBetType(_event: Event, type: string) {
        if (!this.isBetting) return;
        this.betTypeLocation = parseInt(type);
        this.gateXiuWin.active = type == "1";
        this.gateTaiWin.active = type == "2";
        this.confirmBetButton.active = true;
        this.cancelBetButton.active = true;
    }

    selectedChip(_event: Event, value: number) {
        if (!this.isBetting) return;

        if (this.betTypeLocation == 0) {
            this.clearChipToggle();
            return; //@TODO
        } else if (this.betTypeLocation == 1) {
            this.editBoxBetXiu.string = value.toString();
        } else if (this.betTypeLocation == 2) {
            this.editBoxBetTai.string = value.toString();
        }
    }

    clearChipToggle() {
        for (let i = 0; i < this.chipToggleContainer.toggleItems.length; i++) {
            this.chipToggleContainer.toggleItems[i].isChecked = false;
        }
    }

    actConfirmBet() {
        this.actBet();
        this.confirmBetButton.active = false;
        this.cancelBetButton.active = false;
    }

    actCancelBet() {
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
    }

    showResult() {
        var nodeResult: Node;
        this.hideResult();

        if (this.lastLocationIDWin === 1) {
            nodeResult = this.nodeXiuWin;
            this.gateXiuWin.active = true;
            this.gateTaiWin.active = false;
            this.nodeTaiWin.active = false;
        } else if (this.lastLocationIDWin === 2) {
            nodeResult = this.nodeTaiWin;
            this.gateTaiWin.active = true;
            this.gateXiuWin.active = false;
            this.nodeXiuWin.active = false;
        } else {
            return;
        }
        nodeResult.active = true;

        var nodeResultOpacity = nodeResult.getComponent(UIOpacity);
        tween(nodeResultOpacity)
            .repeatForever(
                tween()
                    .to(0.2, { opacity: 100 })
                    .to(0.2, { opacity: 255 })
            )
            .start();

        this.lblRemainWaiting.node.parent.active = true;
        this.lblRemainWaiting.node.active = false;
        this.lblSumDices.string = this.lastScore.toString();

        if (!this.isShowWheel) {
            return;
        }

        this.scheduleOnce(() => {
            this.nodeWheelSpin.active = true;
            this.handleJackpotFund(this.jackpotInfo ? this.jackpotInfo.JackpotLocationID : 0);

            this.scheduleOnce(() => {
                this.nodeJackPot.active = false;
                this.nodeWheelSpin.active = false;
            }, 12);
        }, 3);
    }

    hideResult() {
        Tween.stopAllByTarget(this.nodeTaiWin);
        Tween.stopAllByTarget(this.nodeXiuWin);
        Tween.stopAllByTarget(this.wheelNode);
        this.nodeTaiWin.active = false;
        this.nodeXiuWin.active = false;
        this.gateTaiWin.active = false;
        this.gateXiuWin.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        this.nodeJackPot.active = false;
        this.nodeWheelSpin.active = false;
    }

    handleJackpotFund(locationID: number) {
        let baseAngle = 0;

        if (locationID === 1) {
            baseAngle = 120;
        } else if (locationID === 2) {
            baseAngle = 240;
        }

        Tween.stopAllByTarget(this.wheelNode);
        this.wheelNode.angle = this.wheelNode.angle % 360;
        let totalRounds = 8;
        let targetAngle = 360 * totalRounds + baseAngle;

        tween(this.wheelNode)
            .to(4, {angle: -targetAngle}, {easing: "quartOut"})
            .call(() => {
                if (this.betTypeLocation === 0 || this.betTypeLocation !== locationID) {
                    return;
                }

                this.nodeJackPot.active = true;
                this.lblJackPot.getComponent(Label).string = Utils.formatNumber(this.jackpotInfo.JackpotFund);
            })
            .start();
    }

    toggleLayoutBet(event: any) {
        var target = event.target;
        var textOther = target.getChildByName('text');
        var betFast = target.getChildByName('betFast');
        this.layoutBetChip.active = !this.layoutBetChip.active;
        this.layoutBetNumber.active = !this.layoutBetNumber.active;
        betFast.active = !this.layoutBetChip.active;
        textOther.active = !this.layoutBetNumber.active;
    }

    updateBetAmountCustom(_event: Event, amount: string) {
        if (this.betTypeLocation == 1) {
            this.editBoxBetXiu.string += amount;
        } else if (this.betTypeLocation == 2) {
            this.editBoxBetTai.string += amount;
        }
    }

    deleteBetAmount() {
        if (this.betTypeLocation == 1) {
            this.editBoxBetXiu.string = this.editBoxBetXiu.string.slice(0, -1);
        } else if (this.betTypeLocation == 2) {
            this.editBoxBetTai.string = this.editBoxBetTai.string.slice(0, -1);
        }
    }

    toggleNan(event: any) {
        var target = event.target;
        var on = target.getChildByName('on');
        var off = target.getChildByName('off');
        on.active = !on.active;
        off.active = !off.active;

        this.isNan = !this.isNan;
    }

    toggleChatBox() {
        this.chatBox.active = !this.chatBox.active;
    }

    toggleSettingBox() {
        this.settingBox.active = !this.settingBox.active;
    }

    toggleStatisticalBox() {
        this.statisticalBox.active = !this.statisticalBox.active;
    }

    actPopupEventTienTri() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventTienTri == null) {
            this.popupEventTienTri = instantiate(this.popupEventTienTriPrefab)
                .getComponent("TaiXiuLive.EventTienTri");
            this.popupEventTienTri.node.parent = this.popupContainer;
            this.popupEventTienTri.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventTienTri.showDetail(gameID);
        }
    }

    actPopupDetailSession(session: number) {
        if (this.popupDetailSession == null) {
            this.popupDetailSession = instantiate(this.popupDetailSessionPrefab).getComponent("TaiXiuLive.PopupDetailSession");
            this.popupDetailSession.node.parent = this.popupContainer;
            this.popupDetailSession.showDetail(session, this.getCurrentGameID(), this.detailSessions);
            App.instance.showLoading(false);
        } else {
            this.popupDetailSession.showDetail(session, this.getCurrentGameID(), this.detailSessions);
        }
    }

    actPopupEventSH() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventSH == null) {
            this.popupEventSH = instantiate(this.popupEventSHPrefab)
                .getComponent("TaiXiuLive.EventSH");
            this.popupEventSH.node.parent = this.popupContainer;
            this.popupEventSH.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventSH.showDetail(gameID);
        }
    }

    actPopupEventJackpot() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventJackpot == null) {
            this.popupEventJackpot = instantiate(this.popupEventJackpotPrefab)
                .getComponent("TaiXiuLive.PopupHistoryJackpot");
            this.popupEventJackpot.node.parent = this.popupContainer;
            this.popupEventJackpot.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventJackpot.showDetail(gameID);
        }
    }

    actPopupHonor() {
        if (this.popupHonor == null) {
            this.popupHonor = instantiate(this.popupEventHonorPrefab)
                .getComponent("TaiXiuLive.PopupHonors");
            this.popupHonor.node.parent = this.popupContainer;
            this.popupHonor.show();
            App.instance.showLoading(false);
        } else {
            this.popupHonor.show();
        }
    }

    actPopupHistory() {
        if (this.popupHistory == null) {
            this.popupHistory = instantiate(this.popupHistoryPrefab)
                .getComponent("TaiXiuLive.PopupHistory");
            this.popupHistory.node.parent = this.popupContainer;
            this.popupHistory.show();
        } else {
            this.popupHistory.show();
        }
    }

    actPopupGuide() {
        if (this.popupGuide == null) {
            this.popupGuide = instantiate(this.popupGuidePrefab).getComponent("Dialog");
            this.popupGuide.node.parent = this.popupContainer;
            this.popupGuide.show();
            App.instance.showLoading(false);
        } else {
            this.popupGuide.show();
        }
    }

    public showToast(message: string) {
        this.lblToast.string = message;

        const parent = this.lblToast.node.parent!;
        const uiOpacity = parent.getComponent(UIOpacity) || parent.addComponent(UIOpacity);

        Tween.stopAllByTarget(uiOpacity);
        parent.active = true;
        uiOpacity.opacity = 0;

        tween(uiOpacity)
            .to(0.1, { opacity: 255 })
            .delay(2)
            .to(0.2, { opacity: 0 })
            .call(() => {
                parent.active = false;
            })
            .start();
    }
}
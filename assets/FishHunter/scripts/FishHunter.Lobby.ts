import { _decorator, Component, Label, Sprite } from 'cc';
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import {FishHunterPhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/FishHunterPhotonClient";
const { ccclass, property, menu } = _decorator;

@ccclass('FishHunterLobby')
@menu('FishHunter/Lobby')
export class FishHunterLobby extends Component {

    @property(Label)
    lblLoginStatus: Label = null;
    @property(Label)
    lblGoldBalance: Label = null;
    @property(Label)
    lblGemBalance: Label = null;
    @property(Sprite)
    sprAvatar: Sprite = null;

    private photonClient: FishHunterPhotonClient = null;

    protected start() {
        this.photonClient = FishHunterPhotonClient.getInstance();
        this.photonClient.connect();
        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Account, (res: any) => {
            if (res.errCode < 0) {
                this.lblLoginStatus.string = 'Login failed';
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            App.instance.showLoading(false);
            this.photonClient.isLoggedIn = true;
            this.lblLoginStatus.string = 'Login successful';

            var IAccountModel = JSON.parse(res.vals[FishHunterPhotonClient.EParameterCodes.LoginResponse]);
            this.lblGoldBalance.string = `Gold: ${Utils.formatNumber(IAccountModel.go)}`;
            this.lblGemBalance.string = `Gem: ${Utils.formatNumber(IAccountModel.ge)}`;
            this.sprAvatar.spriteFrame = App.instance.getAvatarSpriteFrame(IAccountModel.ai);
        });
    }

    dismiss() {
        App.instance.gotoLobby();
        FishHunterPhotonClient.getInstance().peer.disconnect();
    }
}


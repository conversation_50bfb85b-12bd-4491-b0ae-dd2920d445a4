import { _decorator, Node, Label, instantiate } from "cc";
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import RouletteSignalRClient from "db://assets/Lobby/scripts/common/networks/RouletteSignalRClient";
import RoulettePlay from "db://assets/Roulette/RouletteScript/Roulette.Play";
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Roulette/PopupJackpot")
export default class RoulettePopupJackpot extends Dialog {

    @property(Node)
    listItem: Node = null;
    @property(Node)
    itemTemplate: Node = null;

    show() {
        this.listItem.removeAllChildren();
        RouletteSignalRClient.getInstance().send('GetJackpotWinner', [RoulettePlay.instance.currency], (data) => {
            if (data.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`ca${data.c}`));
                return;
            }

            if (data.r.length == 0) {
                return;
            }

            data.r.forEach((item: any, index: number) => {
                var itemNode = instantiate(this.itemTemplate);
                itemNode.getChildByName("bg1").active = index % 2 == 0;
                itemNode.getChildByName("bg2").active = index % 2 != 0;
                var content = itemNode.getChildByName('content');
                content.getChildByName("TIME").getComponent(Label).string = Utils.formatDatetime(item.createdTime, 'dd/MM/yyyy HH:mm:ss');
                content.getChildByName("ACCOUNT").getComponent(Label).string = item.nickname;
                content.getChildByName("BET").getComponent(Label).string = Utils.formatNumber(item.parValue);
                content.getChildByName("PRIZE").getComponent(Label).string = Utils.formatNumber(item.prizeValue);

                this.listItem.addChild(itemNode);
            });
        });
        super.show();
    }
}

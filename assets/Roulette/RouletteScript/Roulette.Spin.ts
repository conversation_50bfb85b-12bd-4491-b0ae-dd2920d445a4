import { _decorator, Component, Node, Label, tween, v3 } from "cc";
const { ccclass, property } = _decorator;

@ccclass('RouletteSpin')
export default class RouletteSpin extends Component {
    @property(Node)
    ballPivot: Node = null;

    @property(Node)
    ball: Node = null;

    @property(Node)
    resultNode: Node = null;

    private isSpinning = false;
    private lastResult: number = 0;
    private spinDuration: number = 4; // seconds
    private orbitDuration: number = 4; // seconds

    startSpin(result: number) {
        if (this.isSpinning) return;
        this.isSpinning = true;

        this.ball.active = false;
        this.resultNode.active = false;

        const totalRotation = 360 * 5 + this.getRandomTargetAngle();

        tween(this.node)
            .by(this.spinDuration, { eulerAngles: v3(0, 0, -totalRotation) }, { easing: 'cubicOut' })
            .call(() => {
                this.isSpinning = false;
                this.startBallOrbit(result);
            })
            .start();
    }

    private getRandomTargetAngle(): number {
        const numberOfSlots = 37;
        const randomSlot = Math.floor(Math.random() * numberOfSlots);
        return (360 / numberOfSlots) * randomSlot;
    }

    private getTargetAngle(result: number, from: number): number {
        const rouletteOrder = [
            0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30,
            8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7,
            28, 12, 35, 3, 26
        ];

        const totalSlots = rouletteOrder.length;
        const lastIndex = rouletteOrder.indexOf(from);
        const targetIndex = rouletteOrder.indexOf(result);

        if (lastIndex === -1 || targetIndex === -1) return 0;

        let slotDiff = targetIndex - lastIndex;
        if (slotDiff < 0) slotDiff += totalSlots;

        const anglePerSlot = 360 / totalSlots;
        return anglePerSlot * slotDiff;
    }

    private startBallOrbit(result: number) {
        if (!this.ballPivot || !this.ball) return;

        this.ball.active = true;

        const angleToTarget = this.getTargetAngle(result, this.lastResult);
        const fullRotations = 3 * 360;
        const finalRotation = fullRotations + angleToTarget;

        tween(this.ballPivot)
            .by(this.orbitDuration, { eulerAngles: v3(0, 0, -finalRotation) }, { easing: 'cubicOut' })
            .call(() => {
                this.lastResult = result;
                this.resultNode.active = true;
                const label = this.resultNode.getComponentInChildren(Label);
                if (label) {
                    label.string = result.toString();
                }
                const jackpotNode = this.resultNode.getChildByName('jackpot');
                if (jackpotNode) {
                    jackpotNode.active = result === 0;
                }
            })
            .start();
    }
}
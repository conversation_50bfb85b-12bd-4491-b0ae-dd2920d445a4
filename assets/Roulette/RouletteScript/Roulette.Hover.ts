import { _decorator, Component, Node, TextAsset, Label, game, sys, v2, Vec3, UITransform } from "cc";
import CasinoLobby from "db://assets/Lobby/scripts/common/casino/Casino.Lobby";
import RoulettePlay from "db://assets/Roulette/RouletteScript/Roulette.Play";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Roulette/Hover")
export default class RouletteHover extends Component {
    @property(TextAsset)
    json: TextAsset = null;
    @property(Node)
    betPositions: Node = null;
    @property(Node)
    infoNode: Node = null;
    private texts: any[] = [];

    onLoad() {
        const isVip = CasinoLobby.instance.isTableVip;
        this.texts = JSON.parse(this.json.text);
        this.betPositions.children.forEach((node: Node) => {
            const gateId = parseInt(node.name);
            if (isNaN(gateId)) return;

            const item = this.texts.find((item: any) => item.id === gateId);
            const hoverChild = isVip ? node.getChildByName("hover") : node.getChildByName("hoverNormal");

            node.on(Node.EventType.MOUSE_ENTER, () => {
                game.canvas.style.cursor = "pointer";
                if (hoverChild) hoverChild.active = true;

                item.values.forEach((val: number) => {
                    val += 13;
                    var node = this.betPositions.getChildByName(val.toString());
                    if (node) {
                        const hover = isVip ? node.getChildByName("hover") : node.getChildByName("hoverNormal");
                        if (hover) hover.active = true;
                    }
                });

                var me_bet = RoulettePlay.instance.betAmountLogs.find((bet) => bet.gate === gateId);
                var me_bet_amount = me_bet ? me_bet.amount : 0;
                var total_bet = RoulettePlay.instance.allBetAmountLogs.find((bet) => bet.gate === gateId);
                var total_bet_amount = total_bet ? total_bet.amount : 0;

                var lang = sys.localStorage.getItem("langCode") || "vi";

                this.infoNode.getChildByName("description").getComponent(Label).string = lang === "vi" ? item.description : (item["description_" + lang] || item.description);
                this.infoNode.getChildByName("fund_rate").getComponent(Label).string = `1:${item.fund_rate}`;
                this.infoNode.getChildByName("max_factor").getComponent(Label).string = Utils.formatNumber(item.max_factor * RoulettePlay.instance.minBet);
                this.infoNode.getChildByName("me_bet").getComponent(Label).string = Utils.formatMoney(me_bet_amount, true);
                this.infoNode.getChildByName("total_bet").getComponent(Label).string = Utils.formatMoney(total_bet_amount, true);
                const worldPos = node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                const localPos = this.infoNode.parent.parent.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
                this.infoNode.setPosition(localPos.x + 180, localPos.y - 120);
                this.infoNode.active = true;
            });

            node.on(Node.EventType.MOUSE_LEAVE, () => {
                if (hoverChild) hoverChild.active = false;
                game.canvas.style.cursor = "default";
                this.infoNode.active = false;
                item.values.forEach((val: number) => {
                    val += 13;
                    var node = this.betPositions.getChildByName(val.toString());
                    if (node) {
                        const hover = isVip ? node.getChildByName("hover") : node.getChildByName("hoverNormal");
                        if (hover) hover.active = false;
                    }
                });
            });
        });
    }
}

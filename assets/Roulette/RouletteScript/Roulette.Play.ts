import { _decorator, Component, Node, Label, sys, v3, Vec3, instantiate, SpriteFrame, Sprite, Prefab, EditBox, tween, Tween, Color, Animation, UIOpacity, UITransform } from "cc";
import CasinoPlayer from "db://assets/Lobby/scripts/common/casino/Casino.Player";
import App from "db://assets/Lobby/scripts/common/App";
import RouletteSignalRClient from "db://assets/Lobby/scripts/common/networks/RouletteSignalRClient";
import CasinoLobby from "db://assets/Lobby/scripts/common/casino/Casino.Lobby";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import ChatInGame from "db://assets/Lobby/scripts/common/ChatInGame";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import RouletteSpin from "db://assets/Roulette/RouletteScript/Roulette.Spin";

const { ccclass, property, menu } = _decorator;

enum PHASE {
    NONE = 0,
    BETTING = 1,
    RESULT = 2,
}

@ccclass
@menu("Roulette/Play")
export default class RoulettePlay extends Component {
    static instance: RoulettePlay = null;

    // JOIN ROOM
    @property(Node)
    roomDetail: Node = null;
    @property([SpriteFrame])
    listTableSpr: SpriteFrame[] = [];
    @property(Sprite)
    tableSpr: Sprite = null;
    @property(Label)
    lblToast: Label = null;
    @property([CasinoPlayer])
    players: CasinoPlayer[] = [];
    @property(CasinoPlayer)
    mePlayer: CasinoPlayer = null;
    @property(Node)
    chipNodes: Node = null;
    amounts = [];
    amounts_1 = [
        1,
        5,
        10,
        50,
        100
    ];
    amounts_2 = [
        1,
        2,
        10,
        20,
        100
    ]
    minBet: number = 0;
    sessionId: number = 0;
    roomId: number;
    roomValue: number;
    currency: number;

    // BET
    @property(Node)
    chipContainer: Node = null;
    @property(Node)
    betPositions: Node = null;
    @property(Label)
    countdownSecond: Label = null;
    @property(Sprite)
    progressSprite: Sprite = null;
    @property(SpriteFrame)
    progressSpriteGreen: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteYellow: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteRed: SpriteFrame = null;
    @property(Node)
    nodeDisableClearBet: Node = null;
    @property(Node)
    nodeDisableRebet: Node = null;
    @property(Node)
    nodeDisableBetX2: Node = null;
    @property(Node)
    nodeDisableFinishBet: Node = null;

    amountSelected: number = 0;
    betLogs: any[] = [];
    flagLog: number = -1;

    // POPUP
    @property(Node)
    boxChat: Node = null;
    @property(Node)
    boxSoiCau: Node = null;
    @property(Prefab)
    popupRank: Prefab = null;
    @property(Prefab)
    popupJackpot: Prefab = null;
    @property(Node)
    popupContainer: Node = null;

    // CAU
    @property(Node)
    listCauMini: Node = null;
    @property(Node)
    itemCauMini: Node = null;
    @property(Node)
    listCauNumber: Node = null;
    @property(Node)
    itemCauNumber: Node = null;
    @property([SpriteFrame])
    listCauNumberSpr: SpriteFrame[] = [];

    // SEND MESSAGE
    @property(EditBox)
    editBoxChat: EditBox = null;

    // SHOW RESULT
    @property(Node)
    spinNode: Node = null;
    @property(Node)
    dealerNode: Node = null;

    // BOX SETTING
    @property(Node)
    boxSettingContainer: Node = null;
    @property(Label)
    labelTotalBet: Label = null;
    totalBet: number = 0;

    // JACKPOT
    @property(Label)
    lblJackpotFund: Label = null;
    @property(Label)
    lblJackpotRate: Label = null;
    @property(Label)
    lblJackpotValue: Label = null;
    @property(Node)
    jackpotNode: Node = null;

    //DEALER BOX
    @property(Label)
    labelDealerNotify: Label = null;

    @property(Node)
    guideBG: Node = null;
    @property(Node)
    guide: Node = null;

    private winGates = [];
    betAmountLogs: any[] = [];
    allBetAmountLogs: any[] = [];

    init(roomValue: number, currency: number) {
        this.roomValue = roomValue;
        this.currency = currency;
    }

    protected start() {
        RoulettePlay.instance = this;
        App.instance.showLoading(true);
        RouletteSignalRClient.getInstance().send('EnterRoom', [this.roomValue, this.currency], (data) => {
            App.instance.showLoading(false);
            if (data.c < 0) {
                App.instance.alertDialog.showMsg(RouletteSignalRClient.getErrMsg(data.c));
                this.node.destroy();
                return;
            }

            this.joinRoom(data.r);
            if (sys.localStorage.getItem("CA_RT_first_time") === null) {
                sys.localStorage.setItem("CA_RT_first_time", "1");
                this.showGuide();
            }
        });

        const isVip = CasinoLobby.instance.isTableVip;

        this.betPositions.children.forEach(child => {
            const bg = child.getChildByName('bg');
            const bgNormal = child.getChildByName('bgNormal');
            if (bg) {
                bg.active = isVip;
            }
            if (bgNormal) {
                bgNormal.active = !isVip;
            }
        });
        this.flagLog = -1;
    }

    onLoad() {
        this.chipNodes.children.forEach((button, index) => {
            const chip = button.getChildByName("chip");
            const text = button.getChildByName("text");

            chip.on(Node.EventType.TOUCH_END, () => {
                const isSelected = chip["_isSelected"];

                if (isSelected) {
                    Tween.stopAllByTarget(chip);
                    chip.y = 0;
                    text.getComponent(Label).color = Color.WHITE;
                    chip["_isSelected"] = false;
                    return;
                }

                this.chipNodes.children.forEach((otherButton) => {
                    const otherChip = otherButton.getChildByName("chip");
                    const otherText = otherButton.getChildByName("text");
                    Tween.stopAllByTarget(otherChip);
                    otherChip.y = 0;
                    otherText.getComponent(Label).color = Color.WHITE;
                    otherChip["_isSelected"] = false;
                });

                text.getComponent(Label).color = new Color(252, 255, 0);
                chip["_isSelected"] = true;
                this.amountSelected = this.amounts[index] * this.minBet;

                tween(chip)
                    .repeatForever(
                        tween()
                            .to(0.3, { y: 15 })
                            .to(0.3, { y: 0 })
                    )
                    .start();
            });
        });

        this.betPositions.children.forEach((node: Node) => {
            const position = parseInt(node.name);
            if (isNaN(position)) return;

            node.on(Node.EventType.TOUCH_END, () => {
                if (this.amountSelected == 0) {
                    return;
                }

                this.actBet(this.amountSelected, position);
            });
        });

        RouletteSignalRClient.getInstance().receive('roomData', (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            this.sessionId = data.r.SessionId;
            this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${this.sessionId}`;
            this.handleSession(data.r.Session);
            data.r.Players.forEach((player: any) => {
                var playerObj = this.getAllPlayersById(player.AccountId);
                if (playerObj) {
                    this.scheduleOnce(() => {
                        playerObj.setCoin(player.Balance);
                    }, data.r.Session.Phrase == PHASE.RESULT ? 12 : 0);
                }
            });
        });

        RouletteSignalRClient.getInstance().receive('joinRoom', (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            if (data.r.AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                this.randomFreePlayer().set(data.r);
            }
        });

        RouletteSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
            var playerRM = this.getAllPlayersById(accountId);
            playerRM.showChatMsg(content);
        });

        RouletteSignalRClient.getInstance().receive('leaveRoom', (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            for (var i = 0; i < data.r.length; i++) {
                var item = data.r[i];
                if (item.id == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    if (item.reason < 0) {
                        App.instance.alertDialog.showMsg(App.instance.getTextLang(`ca${item.reason}`));
                    }
                    RouletteSignalRClient.getInstance().dontReceive();
                    BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                    this.node.destroy();
                } else {
                    this.getPlayerById(item.id).leave();
                }
            }
        })

        RouletteSignalRClient.getInstance().receive('registerLeavingRoom', (data) => {
            var playerRLR = this.getAllPlayersById(data.r.id);
            if (data.r.status) {
                playerRLR.showRegisterQuit();
            } else {
                playerRLR.hideRegisterQuit();
            }
        });

        RouletteSignalRClient.getInstance().receive('rejectBetting', (data) => {
            let rejectedPlayers = data.map((id: string) => this.getPlayerById(id).lblNickname.string).join(", ");
            this.showToast(`Reject betting from ${rejectedPlayers}`);
        });

        RouletteSignalRClient.getInstance().receive('clearBetting', (data) => {
            var playerCBId = data.r.id;
            var playerCB = this.getPlayerById(playerCBId);
            this.chipContainer.children.filter(child => child.name.includes(playerCBId)).forEach(chip => {
                this.moveChipToPlayer(chip, this.mePlayer);
            });

            playerCB.setCoin(data.r.balance);
        });

        RouletteSignalRClient.getInstance().receive('confirmBetting', (data) => {
            var playerCB = this.getAllPlayersById(data.r.id);
            playerCB.showReady();
        });

        RouletteSignalRClient.getInstance().receive('playerOtherDevice', (data) => {
            this.showToast(RouletteSignalRClient.getErrMsg(data));
        });

        RouletteSignalRClient.getInstance().receive('connectionChanged', (data) => {
            var playerCC = this.getPlayerById(data.r.id);
            this.showToast(`${playerCC.lblNickname.string} ${data.status ? "connected" : "disconnected"}`);

            if (data.status) {
                playerCC.leave();
            }
        });

        RouletteSignalRClient.getInstance().receive('playerBet', (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            var player = this.getPlayerById(data.r.id);
            player.setCoin(data.r.balance);
            this.placeBet(player, data.r.amount, data.r.gate);
        })
    }

    getChipByAmount(amount: number) {
        return this.chipNodes.children.find((_chip, index) => {
            return this.amounts[index] === amount / this.minBet;
        }).getChildByName("chip");
    }

    joinRoom(data: any) {
        this.roomId = data.Id;
        this.sessionId = data.SessionId;
        this.minBet = data.Value;
        this.roomDetail.getChildByName('RoomTable').getComponent(Label).string = (data.Currency == 0 ? App.instance.getTextLang('tb113') : App.instance.getTextLang('tb112')) + `: ${data.Id}`;
        this.roomDetail.getChildByName('RoomValue').getComponent(Label).string = App.instance.getTextLang('iap38') + ': ' + Utils.formatMoney(data.Value) + ' ' + (data.Currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo');
        this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${data.SessionId}`;
        this.tableSpr.spriteFrame = this.listTableSpr[data.Currency];
        if ([500, 5000, 50000].includes(this.minBet)) {
            this.amounts = this.amounts_2;
        } else {
            this.amounts = this.amounts_1;
        }
        this.chipNodes.children.forEach((button, index) => {
            const text = button.getChildByName("text");
            text.getComponent(Label).string = Utils.formatMoney(this.amounts[index] * this.minBet, true);
            if (index == 0) {
                button.getChildByName("chip").emit(Node.EventType.TOUCH_END);
            }
        });

        var players = data.Players;
        if (players && players.length > 0) {
            for (var i = 0; i < players.length; i++) {
                if (players[i].AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    this.randomFreePlayer().set(players[i]);
                } else {
                    this.mePlayer.set(players[i])
                }
            }
        }

        this.updateStatistic();
        this.checkJackpot();

        if (data.Session) {
            this.handleSession(data.Session);
        }
    }

    handleSession(session: any) {
        this.unscheduleAllCallbacks();
        this.labelDealerNotify.string = 'ROULETTE';
        var dealerNormal = this.dealerNode.getChildByName('NORMAL');
        var dealerBET = this.dealerNode.getChildByName('BET');
        dealerBET.active = false;
        dealerNormal.active = false;
        if (session.Phrase == PHASE.NONE) {
            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();
            return;
        }

        if (session.Timeout) {
            this.updateTimeout(session.Timeout);
        }

        if (session.Phrase == PHASE.BETTING) {
            this.winGates = [];
            this.flagLog++;
            this.betLogs = this.betLogs.filter(log => log.flag == this.flagLog - 1);
            this.betAmountLogs = [];
            this.allBetAmountLogs = [];

            const currency = this.currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo';
            const messages = [
                App.instance.getTextLang('ca157'),
                App.instance.getTextLang('ca150') + ` ${Utils.formatNumber(this.minBet)} ${currency} ` + App.instance.getTextLang('ca151'),
                App.instance.getTextLang('ca165')
            ];

            let indexMsg = 0;

            this.schedule(() => {
                const parentNode = this.labelDealerNotify.node.parent;
                const opacityComp = parentNode.getComponent(UIOpacity) || parentNode.addComponent(UIOpacity);

                tween(opacityComp)
                    .to(0.5, { opacity: 0 })
                    .call(() => {
                        this.labelDealerNotify.string = messages[indexMsg];
                        indexMsg++;
                        if (indexMsg >= messages.length) indexMsg = 0;
                    })
                    .to(0.2, { opacity: 255 })
                    .start();
            }, 2.5);

            dealerBET.active = true;
            dealerBET.getComponent(Animation).play();
            this.jackpotNode.active = false;
            this.spinNode.active = false;
            this.chipContainer.removeAllChildren();
            [this.mePlayer, ...this.players].forEach(player => {
                player.boxWin.active = false;
                player.isWin = false;
                player.stopJackpot();
                player.hideReady();
                player.hideRegisterQuit();
                player.hideWinAnimation();
            });
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = this.betLogs.length == 0;
            this.nodeDisableBetX2.active = this.betLogs.length == 0;
            this.nodeDisableFinishBet.active = true;
            const isVip = CasinoLobby.instance.isTableVip;
            this.totalBet = 0;
            this.labelTotalBet.string = App.instance.getTextLang('ca164') + ` ${this.totalBet}`;

            this.betPositions.children.forEach((node: Node) => {
                var hover = isVip ? node.getChildByName("hover") : node.getChildByName("hoverNormal");
                if (hover) {
                    hover.active = false;
                    Tween.stopAllByTarget(hover.getComponent(UIOpacity));
                }
            });
            this.spinNode.getComponent(RouletteSpin).resultNode.active = false;
        }

        if (session.Phrase == PHASE.RESULT) {
            [this.mePlayer, ...this.players].forEach(player => {
                player.hideReady();
            });
            this.labelDealerNotify.string = App.instance.getTextLang('me18');

            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = true;
            this.spinNode.active = true;
            this.spinNode.getComponent(RouletteSpin).startSpin(session.Result.Value);
            const isVip = CasinoLobby.instance.isTableVip;
            this.scheduleOnce(() => {
                this.winGates = session.Result.WinGates;
                this.showResultPrize(session.Prizes);
                if (session.JackpotPrizes) {
                    this.showResultJackpotPrize(session.JackpotPrizes);
                }
                session.Result.WinGates.forEach((gate: number) => {
                   var node = this.betPositions.getChildByName(gate.toString());
                    var hover = isVip ? node.getChildByName("hover") : node.getChildByName("hoverNormal");
                    if (hover) {
                        hover.active = true;
                        tween(hover.getComponent(UIOpacity))
                            .repeatForever(
                                tween()
                                    .to(0.5, { opacity: 50 })
                                    .to(0.5, { opacity: 255 })
                            )
                            .start();
                    }
                });
                this.updateStatistic();
                this.checkJackpot();
            }, 10);
        }
    }

    updateTimeout(timeout: number) {
        const totalTime = timeout;
        let elapsed = 0;

        this.schedule(() => {
            elapsed += 0.05;
            const percent = Math.min(elapsed / totalTime, 1);
            this.progressSprite.fillRange = percent;
            this.progressSprite.spriteFrame = percent < 0.5 ? this.progressSpriteGreen : (percent < 0.8 ? this.progressSpriteYellow : this.progressSpriteRed);
            this.progressSprite.node.setScale(v3(-1, 1, 1));
        }, 0.05);

        this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
        this.schedule(() => {
            if (timeout < 0) {
                this.unscheduleAllCallbacks();
                return;
            }
            this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
            timeout--;
        }, 1);
    }

    showResultPrize(prizes: any) {
        const uniquePlayers = new Map<string, CasinoPlayer>();

        prizes.forEach((item: any) => {
            const player = this.getAllPlayersById(item.AccountId);
            if (!uniquePlayers.has(player.id)) {
                uniquePlayers.set(player.id, player);
            }
        });

        uniquePlayers.forEach(player => {
            player.isWin = true;
            player.boxWin.active = true;
            player.boxWin.getChildByName('win').active = true;
            player.boxWin.getChildByName('lose').active = false;
            player.showWinAnimation();
            this.chipContainer.children.filter(child => child.name.includes(player.id)).forEach(chip => {
                var gate = parseInt(chip.name.replace(player.id + "__", ""));
                if (this.winGates.includes(gate)) {
                    this.moveChipToPlayer(chip, player);
                } else {
                    this.moveChipToDealer(chip);
                }
            });
        });

        const playerPrizeIds = prizes.map((item: any) => item.AccountId);
        [this.mePlayer, ...this.players]
            .filter(player => player.id !== "" && !playerPrizeIds.includes(player.id))
            .forEach(player => {
            var isPlayerBet = false;
            this.chipContainer.children.filter(child => child.name.includes(player.id)).forEach(chip => {
                isPlayerBet = true;
                this.moveChipToDealer(chip);
            });

            this.scheduleOnce(() => {
                if (isPlayerBet) {
                    player.boxWin.active = true;
                    player.boxWin.getChildByName('win').active = false;
                    player.boxWin.getChildByName('lose').active = true;
                }
            }, 0.5);
        });
    }

    randomFreePlayer() {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == "") {
                return this.players[i];
            }
        }

        return null;
    }

    getPlayerById(id: string) {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == id) {
                return this.players[i];
            }
        }

        return null;
    }

    getAllPlayersById(id: string) {
        return [this.mePlayer, ...this.players].find(player => player.id === id);
    }

    toggleMenu() {
        this.boxSettingContainer.active = !this.boxSettingContainer.active;
    }

    clearBetting() {
        if (this.nodeDisableClearBet.active) {
            return;
        }

        RouletteSignalRClient.getInstance().send('ClearBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            this.chipContainer.children.filter(child => child.name.includes(this.mePlayer.id)).forEach(chip => {
                this.moveChipToPlayer(chip, this.mePlayer);
            });
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = true;
            this.betLogs = [];
            this.mePlayer.setCoin(data.r.balance);
        });
    }

    finishBetting() {
        if (this.nodeDisableFinishBet.active) {
            return;
        }

        RouletteSignalRClient.getInstance().send('FinishBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            this.mePlayer.showReady();
        });
    }

    x2Betting() {
        if (this.nodeDisableBetX2.active) {
            return;
        }

        this.actBetFromBetLogs();
        setTimeout(() => {
            this.actBetFromBetLogs();
        }, 500);
    }

    reLastBet() {
        if (this.nodeDisableRebet.active) {
            return;
        }

        this.actBetFromBetLogs();
    }

    actBetFromBetLogs() {
        this.betLogs.forEach(log => {
            if (log.flag == this.flagLog - 1) {
                this.actBet(log.amount, log.gate);
            }
        });
    }

    actBet(amount: number, gate: number) {
        RouletteSignalRClient.getInstance().send('Bet', [amount, gate], (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            this.nodeDisableClearBet.active = false;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = false;

            this.placeBet(this.mePlayer, data.r.amount, data.r.gate);
            this.mePlayer.setCoin(data.r.balance);

            this.betLogs.push({amount: data.r.amount, gate: data.r.gate, flag: this.flagLog});
        });
    }

    updateStatistic() {
        this.listCauMini.removeAllChildren();
        RouletteSignalRClient.getInstance().send('GetGameHistory', [this.roomId], (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            this.drawCau(data.r);
        });
    }

    sendMessage() {
        this.hiddenBoxChat();
        var content = this.editBoxChat.string;
        if (content == "") {
            return;
        }

        RouletteSignalRClient.getInstance().send('SendMessage', [content], (_data) => {});
    }

    sendEmotion(event: Event, data: string) {
        this.hiddenBoxChat();
        RouletteSignalRClient.getInstance().send('SendMessage', [data], (_data) => {});
    }

    closePlay() {
        RouletteSignalRClient.getInstance().send('ExitRoom', [], (data) => {
            if (data.c == 0) {
                RouletteSignalRClient.getInstance().dontReceive();
                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                this.node.destroy();
                return;
            }

            if (data.r.status) {
                this.mePlayer.showRegisterQuit();
                this.showToast(App.instance.getTextLang('me8'));
            } else {
                this.mePlayer.hideRegisterQuit();
                this.showToast(App.instance.getTextLang('me9'));
            }
        })
    }

    placeBet(player: CasinoPlayer, amount: number, position: number) {
        let chip = instantiate(this.getChipByAmount(amount));
        chip.setScale(0.5, 0.5, 0.5);
        this.chipContainer.addChild(chip);

        let startPos2D = player.avatarNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
        let endPos2D = this.betPositions.getChildByName(position.toString()).getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);

        let startPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(startPos2D.x, startPos2D.y, 0));
        let endPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(endPos2D.x, endPos2D.y, 0));

        const tolerance = 10;
        const existingChips = this.chipContainer.children.filter(child =>
            Vec3.distance(child.getPosition(), endPos) <= tolerance
        );

        if (existingChips.length > 0) {
            let offsetX = 10 * (existingChips.length % 5);
            let offsetY = -10 * Math.floor(existingChips.length / 5);
            endPos.x += offsetX;
            endPos.y += offsetY;
        }

        chip.position = startPos;
        chip.name = `${player.id}__${position}`;

        tween(chip)
            .to(0.5, { position: endPos }, { easing: "sineOut" })
            .call(() => {
                this.totalBet += amount;
                this.labelTotalBet.string = App.instance.getTextLang('ca164') + ' ' + Utils.formatNumber(this.totalBet);
                if (player.id === this.mePlayer.id) {
                    var amountGate = this.betAmountLogs.find(item => item.gate === position);
                    if (amountGate) {
                        amountGate.amount += amount;
                    } else {
                        this.betAmountLogs.push({gate: position, amount: amount});
                    }
                }

                var allAmountGate = this.allBetAmountLogs.find(item => item.gate === position);
                if (allAmountGate) {
                    allAmountGate.amount += amount;
                } else {
                    this.allBetAmountLogs.push({gate: position, amount: amount});
                }
            })
            .start();
    }

    showToast(msg: string) {
        this.lblToast.string = msg;
        this.lblToast.node.active = true;

        setTimeout(() => {
            if (this.lblToast.node) {
                this.lblToast.node.active = false;
            }
        }, 2000);
    }

    showBoxSoiCau() {
        this.boxSoiCau.active = true;
    }

    hiddenBoxSoiCau(){
        this.boxSoiCau.active = false;
    }

    chatInGame: ChatInGame = null;

    showBoxChat() {
        App.instance.inactivityTimer = 0;
        if (this.chatInGame == null) {
            BundleControl.loadPrefabPopup("prefabs/ChatInGame", (prefab: any) => {
                this.chatInGame = instantiate(prefab).getComponent("ChatInGame");
                this.node.addChild(this.chatInGame.node);
                this.chatInGame.show(Configs.InGameIds.Roullete);
            });
        } else {
            this.chatInGame.show(Configs.InGameIds.Roullete);
        }
    }

    hiddenBoxChat(){
        this.boxChat.active = false;
    }

    actShowPopupRank() {
        let popupRank = instantiate(this.popupRank);
        this.popupContainer.addChild(popupRank);
        // @ts-ignore
        popupRank.getComponent("Casino.PopupRank").showDetail(this.currency, Configs.InGameIds.Roullete);
    }

    actShowPopupJackpot() {
        let popupJackpot = instantiate(this.popupJackpot);
        this.popupContainer.addChild(popupJackpot);
        // @ts-ignore
        popupJackpot.getComponent("Roulette.PopupJackpot").show();
    }

    drawCau(data: number[]) {
        this.listCauMini.removeAllChildren();
        this.listCauNumber.removeAllChildren();

        const parseData = data.slice(-10);

        parseData.forEach((item, index) => {
            const mini = instantiate(this.itemCauMini);
            const number = instantiate(this.itemCauNumber);
            var spriteIndex : number;
            if (item == 0) {
                spriteIndex = 0;
            } else if ([ 1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36 ].includes(item)) {
                spriteIndex = 1;
            } else {
                spriteIndex = 2;
            }
            const sprite = this.listCauNumberSpr[spriteIndex];

            mini.getComponent(Sprite).spriteFrame = sprite;
            number.getComponent(Sprite).spriteFrame = sprite;

            const label = number.getComponentInChildren(Label);
            label.string = item < 10 ? `0${item}` : item.toString();

            this.listCauMini.addChild(mini);
            this.listCauNumber.addChild(number);

            if (index === parseData.length - 1) {
                tween(number)
                    .repeatForever(
                        tween()
                            .to(0.5, { position: v3(0, 10, 0) })
                            .to(0.5, { position: v3(0, 0, 0) })
                    )
                    .start();
            }
        });
    }

    checkJackpot() {
        RouletteSignalRClient.getInstance().send('GetJackpot', [this.currency, this.roomValue], (data) => {
            if (data.c < 0) {
                this.showToast(RouletteSignalRClient.getErrMsg(data.c));
                return;
            }

            const jackpot = data.r;
            this.lblJackpotFund.string = jackpot.jackpotFund.toLocaleString("vi-VN");
            this.lblJackpotRate.string = (jackpot.rate / 10) + "%";
            this.lblJackpotValue.string = jackpot.jackpotValue.toLocaleString("vi-VN");
        });
    }

    showResultJackpotPrize(prizes: any) {
        this.jackpotNode.active = true;
        prizes.Details.forEach((detail: any) => {
            [this.mePlayer, ...this.players].find(player => player.id.includes(detail.AccountID))?.activeJackpot();
        });
    }

    private moveChipToPlayer(chip: Node, player: CasinoPlayer, moveToDealerFirst: boolean = false) {
        let tw = tween(chip);

        if (moveToDealerFirst) {
            tw = tw.to(0.5, { position: this.dealerNode.position });
        }

        tw.to(0.5, { position: player.node.position })
            .call(() => chip.destroy())
            .start();
    }

    private moveChipToDealer(chip: Node) {
        tween(chip)
            .to(0.5, { position: this.dealerNode.position })
            .call(() => chip.destroy())
            .start();
    }

    showGuide() {
        var table = this.guideBG.children[0];
        table.children.forEach(child => {
            if (child.getChildByName("hover") == null || child.getChildByName("hoverNormal") == null) return;
            child.getChildByName("hover").active = this.currency == 1;
            child.getChildByName("hoverNormal").active = this.currency == 0;
        });
        this.guideBG.active = true;
        this.guide.active = true;
    }

    hideGuide() {
        this.guideBG.active = false;
        this.guide.active = false;
    }
}

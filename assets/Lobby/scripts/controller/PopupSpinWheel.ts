import { _decorator,EditBox,instantiate,Label,misc,Node, Sprite, tween, Vec3 } from "cc";
import Dialog from "../common/Dialog";
import Configs from "../common/Config";
import Http from "../common/Http";
import App from "../common/App";
import { LanguageManager } from "../common/language/Language.LanguageManager";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import { BroadcastReceiver } from "../common/BroadcastListener";



const { ccclass, property, } = _decorator;
interface SpinResult {
    currencyID: number;
    spinID: number;
    positionPrize: number;
    positionFree: number;
    positionCoin: number;
    totalPrizeValue: number;
    totalCoinValue: number;
    turnBalance: number;
    responseStatus: number;
    gamePrizeID: number;
    createdTime: string;
}

@ccclass
export default class PopupSpinWheel extends Dialog {



    @property(EditBox)
    edbCode: EditBox = null;
    @property(Node)
    wheel3: Node = null;
    @property(Node)
    wheel1: Node = null;


        @property(Node)
    nodeChuyenGame: Node = null;


    @property(Label)
    lblDoneNumber: Label = null;

    @property(Label)
    lbltoGame: Label = null;

    private circleRadius: number = 307 * 2 / 3;
    TYPE_VIP: number = 1;
    TYPE_OFTEN: number = 0;
    LIBNUMBER: string = "0";
    private isSpinning = false;


    @property(Node)
    rewardVn: Node = null;


    @property(Node)
    rewardEn: Node = null;

    @property(Node)
    nodeNotify: Node = null;

    @property(Label)
    lblXu: Label = null;
    @property(Label)
    lblPrice: Label = null;
     mapName = {
        "1": "100K",
        "2": "50K",
        "3": "20K",
        "4": "10K",
        "5": "5K",
        "6": "2K",
        "10": "1M",
        "11": "500K",
        "12": "200K",
        "18": "Trượt",
        "51": "Honda SH",
        "52": "iPhone",
        "111": "Thêm lượt",
        "112": "Trượt",
        "201": "2.000.000 Xu",
        "202": "300,000 Xu",
        "203": "3.000.000 Xu",
        "204": "500.000 Xu",
        "205": "1.000.000 Xu",
        "206": "100.000 Xu",
        "207": "5.000.000 Xu",
        "208": "200.000 Xu",
        "1152": "1 Free Lottery 2k",
        "1155": "1 Free Lottery 5k",
        "1191": "Trading Pro - 1 Ticket 2K",
        "1192": "Trading Pro - 2 Ticket 2K",
        "1193": "Trading Pro - 3 Ticket 2K",
        "1211": "1 Free Keno",
        "1212": "2 Free Keno",
        "1213": "3 Free Keno",
        "2012": "Vương quốc 88",
        "2015": "5 Vương quốc 88",
        "2032": "Sấm Truyền",
        "2034": "4 Sấm Truyền",
        "2052": "Gái Nhảy",
        "2054": "4 Gái Nhảy",
        "2072": "Thủy Cung",
        "2075": "5 Thủy Cung",
        "2112": "Rừng Vàng",
        "2115": "5 Rừng Vàng",
        "2134": "Thần tài",
        "2152": "2 Vũ Trường",
        "2154": "4 Vũ Trường",
        "4011": "Poker Tour",
        "4013": "3 Poker Tour",
        "4031": "TLMN Tour",
        "4033": "3 TLMN Tour",
        "4051": "Sâm lốc Tour",
        "4071": "OTT Tour",
        "4073": "3 OTT Tour",
        "5012": "Sport - 2 - Vé miễn phí - 2K",
        "5015": "Sport - 5 - Vé miễn phí - 5K",
        "5033": "US Power Ball",
        "5043": "US Mega Millions",
        "5091": "Fantasy Sport - 1 - Vé miễn phí - 2K",
        "6015": "Cá Kiếm",
        "6035": "Long Vương",
        "6055": "Cá Mập",
        "6075": "Xuất Kích",
        "6095": "Phi Đội - 5000 - Vàng",
        "11512": "2 Free Lottery 1k",
        "11513": "3 Free Lottery 1k",
        "11522": "2 Free Lottery 2k",
        "11523": "3 Free Lottery 2k",
        "21310": "10 Thần tài"
      };

       mapNameGameReal = {
        "403": "na38",
        "603": "na20",
        "401": "na37",
        "405": "na39",
        "407": "na40",
         "203": "na9",
           "607": "na36",
          "205": "na11"
      };
      

    start() {
        this.nodeChuyenGame.active = false;
        this.getPrizeData();
        this.lblDoneNumber.string = this.LIBNUMBER;
    }

    show() {
        super.show();
        // this.edbCode.string = "";
    }

    mapWheel = null;
    private async getPrizeData() {
      
        App.instance.showLoading(true);
        
            // giải thưởng
            Http.get(Configs.App.DOMAIN_CONFIG['VQMM_GetRewardsConfigUrl'], {}, (status, res) => {
                App.instance.showLoading(false);
                if (status !== 200 || res.c !== 0) {
                  
                    this.isSpinning = true;
                    App.instance.alertDialog.showMsg(App.instance.getTextLang("error_fetch_prize_data"));
                    return;
                }
                this.mapWheel = res.d;
                this.mapPrizeData(res.d);
            });
        
    }

    private mapPrizeData(prizes: { position: number, symbolID: number }[]) {
        if (!prizes || prizes.length === 0) return;

        prizes.sort((a, b) => a.position - b.position);

        prizes.forEach((prize, index) => {
            const imageNode = this.wheel3.children[this.wheel3.children.length - 1 -index]; // Lấy Node ảnh tại vị trí tương ứng
            if (!imageNode) return;
            if(LanguageManager.instance.locale == "vi"){
                const sprite = this.rewardVn.children.find(img => img.name === `lucky-wheel-symbol-${prize.symbolID}-vi`);
                if (sprite) {
                    const spriteComponent = imageNode.getComponent(Sprite);
                    if (spriteComponent) {
                        spriteComponent.spriteFrame = sprite.getComponent(Sprite).spriteFrame; // Gán ảnh vào vòng quay
                    }
                }
            }else{
                const sprite = this.rewardEn.children.find(img => img.name === `lucky-wheel-symbol-${prize.symbolID}`);
                if (sprite) {
                    const spriteComponent = imageNode.getComponent(Sprite);
                    if (spriteComponent) {
                        spriteComponent.spriteFrame = sprite.getComponent(Sprite).spriteFrame; // Gán ảnh vào vòng quay
                    }
                }
            }
          
          
        });

        // Sắp xếp lại ảnh trên vòng quay
        this.arrangeImagesInCircle();
         this.getDataListRotation();
    }
    private arrangeImagesInCircle() {
        const numberOfImages = this.wheel3.children.length; // Số lượng ảnh con
        const angleStep = 360 / numberOfImages;

        for (let i = 0; i < numberOfImages; i++) {
            const imageNode = this.wheel3.children[i]; // Lấy ảnh con thứ i

            // Tính toán góc hiện tại
            const angle = angleStep * i + 22.5+90;

            // Tính toán vị trí dựa trên góc và bán kính
            const radians = misc.degreesToRadians(angle);
            const x = this.circleRadius * Math.cos(radians);
            const y = this.circleRadius * Math.sin(radians);
            imageNode.setPosition(x, y);

            // Xoay ảnh sao cho vector BA hướng về tâm
            imageNode.angle = angle - 90; // +90 để điểm A hướng về tâm
        }
    }
    actRotate() {

    //    if( Configs.Login.SecurityStatus == 0){
    //          App.instance.alertDialog.showMsg(App.instance.getTextLang("me-3003"));
    //             return;
    //    }
        if (this.isSpinning) {
            // App.instance.alertDialog.showMsg("Đang quay, vui lòng chờ...");
            return;
        }

        //Kiểm tra thành công mới chạy
        let currentTurns = parseInt(this.lblDoneNumber.string);
        if (currentTurns <= 0) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_no_spin"));
            return;
        }

        this.isSpinning = true;

        //Gọi đến api spin
         App.instance.showLoading(true);
        this.postRotation();

    }

    clickHistory(){
        let cb = (prefab) => {
            let popup = instantiate(prefab)
              .getComponent("PopupHistoryWheel");
            popup.node.parent = App.instance.node;
            popup.setData(this);
            popup.show();
          };
          BundleControl.loadPrefabPopup("prefabs/PopupHistoryWheel", cb);
    }
    clickTutorial(){
        let cb = (prefab) => {
            let popup = instantiate(prefab)
              .getComponent("PopupTutorialWheel");
            popup.node.parent = App.instance.node;
           
            popup.show();
          };
          BundleControl.loadPrefabPopup("prefabs/PopupTutorialWheel", cb);
    }

    private getDataListRotation() {
        Http.get(Configs.App.DOMAIN_CONFIG['VQMM_GetTurnUrl'], { type: 0 }, (status, res) => {
            try {
                // App.instance.alertDialog.showMsg(status);
                if (status !== 200 || res.c !== 0) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang("ca-10000"));
                    return;
                }
                if (status === 200 && res.c == 0) {
                    if (+res?.d > 0) {
                        this.lblDoneNumber.string = res?.d.toString();
                    } else {
                        this.lblDoneNumber.string = "0";
                    }
                }
                this.isSpinning = false;
                App.instance.showLoading(false);
            } catch (error) {
                App.instance.showLoading(false);
                App.instance.alertDialog.showMsg(App.instance.getTextLang("ca-10000"));
            }
        });
        // this.dismiss();

    }

    private postRotation() {
        const CurrencyID = Configs.Login.CurrencyID;
        let data = {
            "CurrencyID": CurrencyID,
            "type": this.TYPE_OFTEN,
        };
        Http.post(Configs.App.DOMAIN_CONFIG['VQMM_GetSpinUrl'], data, (status, res) => {
            // App.instance.alertDialog.showMsg(res?.c);

            if (status !== 200 || res?.c !== 0) {
                // switch (res?.c) {
                //     case -51:
                //         App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_insufficient_balance"));
                //         break;
                //     case -600:
                //         App.instance.alertDialog.showMsg(App.instance.getTextLang("ca-10003"));
                //         break;

                //     case -99:
                //         App.instance.alertDialog.showMsg(App.instance.getTextLang("me-99"));
                //         break;

                //     case -62:
                //         App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_unknown_error"));
                //         break;
                //     default:
                //         App.instance.alertDialog.showMsg(App.instance.getTextLang("ca-10004"));
                //         break;
                // }

                // App.instance.showLoading(false);
                // this.isSpinning = false;
                return;
            }

            App.instance.showLoading(false);
            this.runWheel(res?.d)
        });
      //  App.instance.showLoading(false);
       // this.runWheel(null);
    }

    closePopupp(){
        this.unschedule(this.countDownTick);
           this.nodeNotify.active = false;
         this.isSpinning = false;
    }
    gameID = 0;
    private runWheel(spinData: SpinResult) {
         var rotateToIdx3 =  spinData?.positionPrize-1;
        var rotateToIdx1 =   spinData?.positionCoin-1;
        this.wheel1.eulerAngles = new Vec3(0, 0, 0);
        this.wheel3.eulerAngles = new Vec3(0, 0, 0);
      
          var arrIndexNew =  [6,5,4,3,2,1,0,7];
        // "201": "2.000.000 Xu",6
        // "202": "300,000 Xu",5
        // "203": "3.000.000 Xu",4
        // "204": "500.000 Xu",3
        // "205": "1.000.000 Xu",2
        // "206": "100.000 Xu", 1
        // "207": "5.000.000 Xu", 0
        // "208": "200.000 Xu",7

       this.gameID = spinData?.gamePrizeID;
       

       this.nodeChuyenGame.active =  this.gameID == 18 ?false:true;
    
        //Vòng quay nhỏ
const targetAngleZ = -(360 * 4 + (360 - 360 / 8 * arrIndexNew[rotateToIdx1]) - 360 / 8 / 2);

// Lấy góc hiện tại
const currentEuler = this.wheel1.eulerAngles;

// Tạo Vec3 mới với góc Z mới, giữ nguyên X và Y
const targetEuler = new Vec3(currentEuler.x, currentEuler.y, targetAngleZ);

// Tween xoay bằng eulerAngles trong 3 giây
tween(this.wheel1)
    .to(3, { eulerAngles: targetEuler }, { easing: 'sineInOut' }) // dùng easing bằng string
    .start();
        var thiz = this;

        thiz.lblXu.string = thiz.mapName["20"+spinData?.positionCoin];
        const found = this.mapWheel.find(item => item.position === spinData?.positionPrize);
        if(found){
            
            thiz.lblPrice.string  =   App.instance.getTextLang("vqmm_rs_"+found.symbolID);
        }
        const angleZ = 360 * 4 + 360 / 8 * rotateToIdx3 + 360 / 8 / 2;

// Lưu euler góc hiện tại
const currentEuler3 = this.wheel3.eulerAngles;

// Tạo euler target mới
const targetEuler3 = new Vec3(currentEuler3.x, currentEuler3.y, angleZ);

// Dừng tween cũ nếu có
tween(this.wheel3).stop();

// Bắt đầu tween mới (thay cho runAction)
tween(this.wheel3)
    .delay(0.25)
    .to(4, { eulerAngles: targetEuler3 }, { easing: 'sineInOut' })
    .delay(1)
    .call(() => {
        thiz.lblDoneNumber.string = spinData?.turnBalance.toString();
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        thiz.nodeNotify.active = true;

        if (thiz.gameID !== 18) {
            thiz.startCountDown();
        }
    })
    .start();
    }
    countDownTime = 10;
startCountDown() {
    this.countDownTime = 10;

    // Hiển thị giá trị ban đầu
    this.updateCountDownLabel();

    // Dừng bất kỳ schedule nào đang chạy trước đó (nếu có)
    this.unschedule(this.countDownTick);

    // Bắt đầu đếm ngược mỗi giây
    this.schedule(this.countDownTick, 1, this.countDownTime - 1, 0);
}

countDownTick() {
    this.countDownTime--;

    this.updateCountDownLabel();

    if (this.countDownTime <= 0) {
        this.unschedule(this.countDownTick);
        this.showCountdownFinished();
    }
}

updateCountDownLabel() {
    if (this.lbltoGame) {
        this.lbltoGame.string = LanguageManager.instance.getString(this.mapNameGameReal[this.gameID.toString()
        ]) + ": " + this.countDownTime.toString() + "s";

    }
}

showCountdownFinished() {
    this.node.active = false;
    App.instance.gotoGameFromVqmn(this.gameID);
}


    // private showPrizeImage(imageFrame: SpriteFrame) {
    //     let prizePopup = new Node();
    //     let sprite = prizePopup.addComponent(Sprite);
    //     sprite.spriteFrame = imageFrame;

    //     prizePopup.parent = this.node; 
    //     prizePopup.setPosition(0, 0); 
    //     prizePopup.setContentSize(200, 200); 
    // }

}

import {
    _decorator,
    Component,
    instantiate,
    log,
    Node,
    view,
    ResolutionPolicy,
    sys,
    UIOpacity,
    Label,
    tween,
    easing,
    Sprite<PERSON><PERSON>e,
    Sprite,
    Vec3,
    <PERSON><PERSON><PERSON>,
    director
} from "cc";

import BundleControl from "../../../Loading/scripts/BundleControl";
import App from "../common/App";
import Http from "../common/Http";
import { Utils } from "db://assets/Lobby/scripts/common/Utils";
import { SPUtils } from "db://assets/Lobby/scripts/common/SPUtils";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Config from "db://assets/Lobby/scripts/common/Config";
import { BroadcastReceiver } from "db://assets/Lobby/scripts/common/BroadcastListener";
import eventBus from "db://assets/Lobby/scripts/common/EventBus";
import { Global } from "db://assets/Lobby/scripts/common/Global";
import { PopupProfile } from "db://assets/Lobby/scripts/controller/lobby/PopupProfile";
import { PopupRegister } from "db://assets/Lobby/scripts/controller/lobby/PopupRegister";
import { PopupUpdateNickname } from "db://assets/Lobby/scripts/controller/lobby/PopupUpdateNickname";
import LobbyLoginByOTP from "db://assets/Lobby/scripts/controller/lobby/Lobby.LoginByOTP";
import PopupDownload from "db://assets/Lobby/scripts/controller/lobby/PopupDownload";
import { PopupSupport } from "db://assets/Lobby/scripts/controller/lobby/PopupSupport";
import PopupEvent from "db://assets/Lobby/scripts/controller/lobby/PopupEvent";
import PopupGiftCode from "db://assets/Lobby/scripts/controller/lobby/PopupGiftCode";
import PopupForgetPassword from "db://assets/Lobby/scripts/controller/lobby/Lobby.PopupForgetPassword";
import PopupSecurity from "./lobby/security/Lobby.PopupSecurity";
import { PopupTournament } from "db://assets/Lobby/scripts/controller/lobby/PopupTournament";
import PopupEventX2 from "db://assets/Lobby/scripts/controller/lobby/PopupEventX2";
import CardGameSignalRClient from "../common/networks/CardGameSignalRClient";
import BannerView from "db://assets/Lobby/scripts/common/BannerView";
import SignalRClient from "db://assets/Lobby/scripts/common/networks/Network.SignalRClient";
import PopupCashout from "db://assets/Lobby/scripts/controller/PopupCashout";

declare const forge: any;
declare global {
    interface Window {
       // forge: any;
        photon: any;
        md5: any;
        signalR: any;
    }
}

const { ccclass, property } = _decorator;

@ccclass("LobbyController")
export class LobbyController extends Component {
    @property(Node)
    nodeWheel: Node = null;
    @property(Node)
    nodeRoom: Node = null;
    @property(Label)
    lblNickname: Label = null;
    @property(Sprite)
    sprAvatar: Sprite = null;
    @property(Label)
    lblGoldBalance: Label = null;
    @property(Label)
    lblCoinBalance: Label = null;

    @property({ type: [Node] })
    hoverCategory: Node[] = [];
    @property(EditBox)
    userName: EditBox = null;
    @property(EditBox)
    password: EditBox = null;
    @property(Node)
    panelLoggedOut: Node = null;
    @property(Node)
    panelLoggedIn: Node = null;
    @property(Node)
    panelMenu: Node = null;

    @property(Node)
    jackpotx6: Node = null;
    @property(Node)
    jackpotX6List: Node = null;
    @property(Node)
    jackpotX6Content: Node = null;

    @property(SpriteFrame)
    logo_fortune: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_kingdom: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_ocean: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_oracle: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_dancingNight: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_dancing: SpriteFrame | null = null;
    @property(SpriteFrame)
    logo_forest: SpriteFrame | null = null;

    @property(SpriteFrame)
    icon_x2: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x3: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x4: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x5: SpriteFrame | null = null;
    @property(SpriteFrame)
    icon_x6: SpriteFrame | null = null;

    @property(Node)
    containerLoginBox: Node = null;
    @property(Node)
    bgLoginBox: Node = null;
    @property(Node)
    LoginBox: Node = null;
    @property(EditBox)
    userNameInBox: EditBox = null;
    @property(EditBox)
    passwordInBox: EditBox = null;
    @property(Node)
    nodeIconFloatButton: Node = null;

    @property(Label)
    lblVersion: Label = null;
    @property(Node)
    notifyBarNode: Node = null;
    @property(Node)
    listGameCenterNode: Node = null;

    private pingpongID: number = null;
    private isVisibleJackPotX6: boolean = false;


    protected onLoad(): void {
        this.scheduleOnce(()=>{
            console.log(Configs.App.DOMAIN_CONFIG);
        },10)
        Global.LobbyController = this;

        this.fixMultiScreen();
        this.fetchAndDecryptMessage();
        this.lblVersion.string = `Version: ${Configs.App.G88_VERSION}`;

        this.hoverCategory.forEach((node) => {
            node.on(Node.EventType.MOUSE_ENTER, () => this.onHoverEnter(node), this);
            node.on(Node.EventType.MOUSE_LEAVE, () => this.onHoverLeave(node), this);
        });
    }

    private _wasActive = false;
    update(_dt: number) {
        const hasRoomChild = App.instance.nodeRoom.children.length > 0;
        const hasBigGameChild = App.instance.bigGameNode.children.length > 0;
        const shouldHide = hasRoomChild || hasBigGameChild;

        if (shouldHide !== this._wasActive) {
            this.notifyBarNode.active = !shouldHide;
            this.listGameCenterNode.active = !shouldHide;
            this._wasActive = shouldHide;
        }
    }

    protected start(): void {
        BroadcastReceiver.register(
            BroadcastReceiver.USER_UPDATE_COIN,
            () => {
                if (Configs.Login.IsLogin == false) return;
                Http.get(Configs.App.DOMAIN_CONFIG['GetAllBalance'], {}, (status, json) => {
                    if (status === 200) {
                        Configs.Login.GoldBalance = json['d'][0]['goldBalance'];
                        Configs.Login.CoinBalance = json['d'][0]['coinBalance'];

                        this.lblCoinBalance.string = Utils.formatNumber(Configs.Login.CoinBalance);
                        this.lblGoldBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
                    }
                });
            },
            this
        );

        BroadcastReceiver.register(
            BroadcastReceiver.USER_LOGGED_IN,
            () => {
                this.lblNickname.string = Configs.Login.Nickname;
                this.sprAvatar.spriteFrame = App.instance.getAvatarSpriteFrame(
                    Configs.Login.Avatar
                );

                this.panelLoggedOut.active = false;
                this.panelLoggedIn.active = true;

                this.lblCoinBalance.string = Utils.formatNumber(Configs.Login.CoinBalance);
                this.lblGoldBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);

                Configs.Login.IsLogin = true;
                eventBus.emit('LOGIN_SUCCESS');
                this.LoginBox.active = false;
                this.nodeIconFloatButton.active = true;

                App.instance.actVQMM();
                App.instance.openGame(Configs.InGameIds.TaiXiuMini);
                Http.get(Configs.App.DOMAIN_CONFIG["VQMM_GetTurnUrl"], {type: 1}, (status, res) => {
                    if (status === 200 && res.c == 0 && res.d > 0) {
                        this.showVipWheel();
                    }
                }, false);
                Http.get(Configs.App.DOMAIN_CONFIG["GetBannerEvent"], {}, (status, res) => {
                    if (status === 200 && res.c == 0 && res.d.length > 0) {
                        this.showSlide(res.d);
                    }
                }, false);
                Http.get(Configs.App.DOMAIN_CONFIG["GetPopupContentUrl"], {}, (status, res) => {
                    if (status === 200 && res.c == 0 && res.d.length > 0) {
                        this.showAnnouncement(res.d);
                    }
                }, false);

                if (Utils.getStorageKey("last_open_game_id") !== null) {
                    const gameId = parseInt(Utils.getStorageKey("last_open_game_id"));
                    if (gameId > 0) {
                        App.instance.openGame(gameId);
                    }
                }
            },
            this
        );

        BroadcastReceiver.register(
            BroadcastReceiver.USER_INFO_REFRESH,
            (data: any) => {
                if (data) {
                    Config.Login.set(data);
                } else {
                    Http.get(Configs.App.DOMAIN_CONFIG['GetAccountInfoUrl'], {}, (status, res) => {
                        if (status === 200) {
                            Config.Login.set(res.d);
                            this.sprAvatar.spriteFrame = App.instance.getAvatarSpriteFrame(
                                Configs.Login.Avatar
                            );
                            this.lblCoinBalance.string = Utils.formatNumber(Configs.Login.CoinBalance);
                            this.lblGoldBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
                        }
                    });
                }
            },
            this
        );

        BroadcastReceiver.register(
            BroadcastReceiver.USER_LOGOUT,
            (_data: any) => {
                if (this.pingpongID) {
                    clearInterval(this.pingpongID);
                }

                SignalRClient.closeAll();
                if (director.getScene().name !== "Lobby") {
                    // App.instance.loadScene("Lobby");
                } else {
                    this.panelLoggedOut.active = true;
                    this.panelLoggedIn.active = false;
                    this.userName.string = "";
                    this.password.string = "";
                    this.userNameInBox.string = "";
                    this.passwordInBox.string = "";
                }
                SPUtils.setUserName("");
                SPUtils.setUserPass("");
                eventBus.emit('LOGOUT');
                Configs.Login.clear();
                this.nodeIconFloatButton.active = false;
                // App.instance.buttonMiniGame?.actHidden();
                App.instance.bigGameNode?.removeAllChildren();
                App.instance.miniGameNode?.removeAllChildren();
                // this.nodeRoom?.removeAllChildren();
            },
            this
        );
    }

    showVipWheel() {

        let cb = (prefab) => {
            let popupDaily = instantiate(prefab)
                .getComponent("PopupVipWheel");
            this.nodeWheel.addChild(popupDaily.node);

        };
        BundleControl.loadPrefabPopup("prefabs/PopupVipWheel", cb);
    }

    fixMultiScreen() {
        if (
            view.getViewportRect().width / view.getViewportRect().height >
            1920 / 1080
        ) {
            view.setDesignResolutionSize(1920, 1080, ResolutionPolicy.FIXED_HEIGHT);
        } else {
            view.setDesignResolutionSize(1920, 1080, ResolutionPolicy.FIXED_WIDTH);
        }
    }

    fetchAndDecryptMessage() {
        try {
            const key = Utils.isBrowser() ? Config.App.G88_KEY_DECRYPT_CONFIG : Config.App.G88_KEY_DECRYPT_CONFIG_APP;
            const nonSecretPayloadLength = 0;

            Http.get(Config.App.G88_DOMAIN_GET_CONFIG, {}, (status, res) => {
                if (status != 200) {
                    this.fetchAndDecryptMessage();

                    return;
                }

                const encryptedMessage = res.replace(/[^A-Za-z0-9+/=]/g, '');
                const decodedKey = forge.util.decode64(key);
                const cipherText = forge.util.decode64(encryptedMessage);
                let offset = 0;
                offset += nonSecretPayloadLength;
                const nonce = cipherText.slice(offset, offset + 16);
                offset += 16;
                const authTag = cipherText.slice(cipherText.length - 16);
                const encryptedMessagePart = cipherText.slice(offset, cipherText.length - 16);
                const decipher = forge.cipher.createDecipher('AES-GCM', decodedKey);
                decipher.start({
                    iv: nonce,
                    tagLength: 128,
                    tag: forge.util.createBuffer(authTag)
                });
                decipher.update(forge.util.createBuffer(encryptedMessagePart));
                const success = decipher.finish();
                if (!success) {
                    throw new Error('Decryption failed');
                }

                const data = JSON.parse(decipher.output.toString());
                log(data)
                Configs.App.DOMAIN_CONFIG = data.DomainConfig;
                Configs.App.G88_CONFIG = data;
                Config.App.GAME_AVAILABLE_IDS = data.GamesAvaiable;
                BannerView.instance.showDetail(data.Banners);

                SPUtils.setRSAPublicKey(data.RsaPublicKey);
                Utils.setStorageValue('client-token', data.Token);

                if (SPUtils.getUserName() != "" && SPUtils.getUserPass() != "") {
                    this.actLogin(SPUtils.getUserName(), SPUtils.getUserPass());
                }
            }, false);

        } catch (error) {
            if (error instanceof Error) {
                log(`Error: ${error}`);
            } else {
                log('An unknown error occurred');
            }
        }
    }

    public actLogin(username = null, password = null): void {
        if (Configs.App.DOMAIN_CONFIG.length === 0) return;

        if (username == null || password == null) {
            if (this.LoginBox.active) {
                username = this.userNameInBox.string.trim();
                password = this.passwordInBox.string.trim();
            } else {
                username = this.userName.string.trim();
                password = this.password.string.trim();
            }
        }

        if (username.length == 0) {
            App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_username_not_blank")
            );
            return;
        }

        if (password.length == 0) {
            App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_password_not_blank")
            );
            return;
        }

        const data = {
            Username: username,
            Password: password,
            Timestamp: Utils.getTicks(),
            Md5Password: window.md5(password)
        }

        const payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        };

        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['LoginUrl'], payload, (_status, res) => {
            if (this.pingpongID) {
                clearInterval(this.pingpongID);
            }
            this.pingpongID = setInterval(() => {
                this.pingpong();
            }, 60000);

            App.instance.showLoading(false);
            switch (parseInt(res["c"])) {
                case 0:
                    Configs.Login.IsLogin = true;
                    SPUtils.setUserName(username);
                    SPUtils.setUserPass(password);

                    Configs.Login.AccessToken = res["m"];
                    Configs.Login.SessionKey = res["d"]["sessionToken"];

                    BroadcastReceiver.send(BroadcastReceiver.USER_INFO_REFRESH, res['d']);
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGGED_IN);
                    break;
                case 2:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupLoginByOTP", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(LobbyLoginByOTP);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                case 3:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupUpdateNickName", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(PopupUpdateNickname);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                default:
                    break;
            }
        });
    }

    public actRegister(): void {
        if (Configs.App.DOMAIN_CONFIG.length === 0) return;

        BundleControl.loadPrefabPopup("prefabs/PopupRegister", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupRegister);
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    public actLostPassword(): void {
        if (Configs.App.DOMAIN_CONFIG.length === 0) return;

        BundleControl.loadPrefabPopup("prefabs/PopupForgetPassword", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupForgetPassword);
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    public actLogout(): void {
        App.instance.confirmDialog.showMsg(
            App.instance.getTextLang("txt_ask_logout"),
            (isConfirm) => {
                if (isConfirm) {
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
                }
            }
        );
    }

    private pingpong() {
        const accessToken = Configs.Login.AccessToken;
        if (accessToken == "") {
            return;
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CheckAuthenUrl'], {}, (_status, res) => {
            switch (res['c']) {
                case 0:
                    if (res['m'] != null) {
                        Configs.Login.AccessToken = res['m'];
                    }
                    break;
                case 2:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupLoginByOTP", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(LobbyLoginByOTP);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                case 3:
                    Configs.Login.Username = res.p[0];
                    BundleControl.loadPrefabPopup("prefabs/PopupUpdateNickName", (prefab: any) => {
                        let popup = instantiate(prefab).getComponent(PopupUpdateNickname);
                        App.instance.popupNode.addChild(popup.node);
                        popup.show();
                    });
                    break;
                default:
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
            }
        })
    }

    onHoverEnter(node: Node) {
        tween(node)
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1.1) })
            .start();
    }

    onHoverLeave(node: Node) {
        tween(node)
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
    }

    toggleMenu() {
        this.panelMenu.active = !this.panelMenu.active;
    }

    openPopupProfile(even: Event, customEventData: string) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        const index = parseInt(customEventData);
        BundleControl.loadPrefabPopup("prefabs/PopupProfile", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupProfile);
            App.instance.popupNode.addChild(popup.node);
            popup.show();
            popup.setTabDefault(index);
        });
    }

    actDownloadGameTipZo() {
        // if (!Configs.Login.IsLogin) {
        //     this.actShowLoginForm();
        //     return;
        // }
        BundleControl.loadPrefabPopup("prefabs/PopupDownload", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupDownload);
            if (!popup) {
                log("PopupDownload component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actSPGameTipZo() {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupSupport", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupSupport);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actMissonGameTipZo(even, data) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupEvent", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupEvent);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actOpenGifCode(even, data) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupGiftCode", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupGiftCode);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actTournament(even, data) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupTournament", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupTournament);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actEventX2(even: Event, customEventData: string) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        const index = parseInt(customEventData);
        BundleControl.loadPrefabPopup("prefabs/PopupX2", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupEventX2);
            if (!popup) {
                log("PopupSupport component not found on prefab.");
                return;
            }
            App.instance.tipzoJackpotEventX2X6Node.addChild(popup.node);
            popup.show();
            popup.showTab(index);
        });
    }

    actPopupCashInCashOut(even: Event, customEventData: string) {
        if (!Configs.Login.IsLogin) {
            this.actShowLoginForm();
            return;
        }
        let cb = (prefab) => {
            let actCashGameTipZo = instantiate(prefab).getComponent(PopupCashout);

            App.instance.popupNode.addChild(actCashGameTipZo.node);

            if (customEventData === 'withdraw_card') {
                actCashGameTipZo.showWithdrawCard();
            } else {
                actCashGameTipZo.showTab(parseInt(customEventData));
            }
        };
        BundleControl.loadPrefabPopup("prefabs/PopupCashinCashout", cb);
    }


    actToggleTargetX6() {
        this.isVisibleJackPotX6 = !this.isVisibleJackPotX6;
        this.jackpotx6.active = this.isVisibleJackPotX6;

        const logoMap = {
            213: this.logo_fortune,
            201: this.logo_kingdom,
            207: this.logo_ocean,
            203: this.logo_oracle,
            215: this.logo_dancingNight,
            205: this.logo_dancing,
            211: this.logo_forest,
        };

        if (this.isVisibleJackPotX6) {
            this.jackpotX6List.removeAllChildren();
            Http.get(Configs.App.DOMAIN_CONFIG['GetListJackpot'], { CurrencyID: Configs.Login.CurrencyID }, (status, res) => {
                if (status == 200) {
                    res.d.forEach((item, index) => {
                        if (item.multiplier > 0) {
                            let itemJackpot = instantiate(this.jackpotX6Content);
                            let nameLabel = itemJackpot.getChildByName("center").getChildByName("header").getComponent(Label);
                            switch (item.gameID) {
                                case 213:
                                    nameLabel.string = App.instance.getTextLang("tx_than_tai");
                                    break;
                                case 201:
                                    nameLabel.string = App.instance.getTextLang("tx_vuong_quoc");
                                    break;
                                case 207:
                                    nameLabel.string = App.instance.getTextLang("tx_thuy_cung");
                                    break;
                                case 203:
                                    nameLabel.string = App.instance.getTextLang("tx_sam_truyen");
                                    break;
                                case 215:
                                    nameLabel.string = App.instance.getTextLang("tx_vu_truong");
                                    break;
                                case 205:
                                    nameLabel.string = App.instance.getTextLang("tx_gai_nhay");
                                    break;
                                case 211:
                                    nameLabel.string = App.instance.getTextLang("tx_rung_vang");
                                    break;
                            }
                            itemJackpot.getChildByName("center").getChildByName("room").getChildByName("value").getComponent(Label).string = item.roomID == 1 ? "100" : (item.roomID == 2 ? "1.000" : "10.000");
                            itemJackpot.getChildByName("center").getChildByName("content").getChildByName("X").getComponent(Label).string = "X" + item.multiplier;
                            itemJackpot.getChildByName("center").getChildByName("content").getChildByName("nextJackpot").getComponent(Label).string = item.nextJackpot;
                            const logoNode = itemJackpot.getChildByName("logo");
                            const iconNode = itemJackpot.getChildByName("icon");
                            const spriteIcon = iconNode.getComponent(Sprite);

                            switch (item.multiplier) {
                                case 2:
                                    spriteIcon.spriteFrame = this.icon_x2;
                                    break;
                                case 3:
                                    spriteIcon.spriteFrame = this.icon_x3;
                                    break;
                                case 4:
                                    spriteIcon.spriteFrame = this.icon_x4;
                                    break;
                                case 5:
                                    spriteIcon.spriteFrame = this.icon_x5;
                                    break;
                                case 6:
                                    spriteIcon.spriteFrame = this.icon_x6;
                                    break;
                            }


                            const sprite = logoNode.getComponent(Sprite);
                            if (sprite && logoMap[item.gameID]) {
                                sprite.spriteFrame = logoMap[item.gameID];
                            }

                            itemJackpot.on(Node.EventType.TOUCH_END, (event: any) => {
                                // event.stopPropagation();
                                if (!Configs.Login.IsLogin) {
                                    this.actShowLoginForm();
                                    return;
                                }
                            });

                            this.jackpotX6List.addChild(itemJackpot);
                        }
                    });
                }
            });
        }
    }

    actTeleTipZo() {
        sys.openURL("https://t.me/TIPZO_CSKH_BOT");
    }

    actFbTipZo() {
        sys.openURL("http://tiny.cc/ple7001");
    }

    actSecurity() {
        if (!Configs.Login.IsLogin) {
            // this.actShowLoginForm();
            return;
        }
        BundleControl.loadPrefabPopup("prefabs/PopupSecurity", (prefab: any) => {
            let popup = instantiate(prefab).getComponent(PopupSecurity);
            if (!popup) {
                log("PopupSecurity component not found on prefab.");
                return;
            }
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actCheckLogined() {
        if (!Configs.Login.IsLogin) {
            this.LoginBox.active = true;
            return;
        }
    }

    actShowLoginForm() {
        this.LoginBox.active = true;

        let editsOutside = this.node.parent.getComponentsInChildren(EditBox);
        for (let edit of editsOutside) {
            edit.tabIndex = -1;
        }

        let editsInside = this.containerLoginBox.getComponentsInChildren(EditBox);
        for (let i = 0; i < editsInside.length; i++) {
            editsInside[i].tabIndex = i + 1; // Đảm bảo thứ tự focus đúng
        }

        if (editsInside.length > 0) {
            editsInside[0].focus();
        }
    }


    actCloseLoginForm() {
        if (!this.bgLoginBox || !this.containerLoginBox) return;

        const uiOpacityLoginBox = this.bgLoginBox.getComponent(UIOpacity);
        tween(uiOpacityLoginBox)
            .to(0.2, { opacity: 0 }) // fadeOut trong 0.2s
            .start();

        // Reset giá trị ban đầu
        this.containerLoginBox.getComponent(UIOpacity)!.opacity = 255;
        this.containerLoginBox.setScale(1, 1, 1); // scale = 1

        const uiOpacity = this.containerLoginBox.getComponent(UIOpacity);
        if (uiOpacity) {
            tween(uiOpacity)
                .to(0.3, { opacity: 150 }, { easing: easing.backIn })
                .start();
        }

        tween(this.containerLoginBox)
            .to(0.3, { scale: new Vec3(0.8, 0.8, 1) }, { easing: easing.backIn })
            .call(() => {
                this._onDismissed();
            })
            .start();
    }

    actGameTaiXiuMD5Live() {
        App.instance.openGame(Configs.InGameIds.TaiXiuMD5Live);
    }

    actGameTaiXiuLive() {
        App.instance.openGame(Configs.InGameIds.TaiXiuLive);
    }

    actGameBlackJack() {
        App.instance.openGame(Configs.InGameIds.BlackJack);
    }

    actGameBaccarat() {
        App.instance.openGame(Configs.InGameIds.Baccarat);
    }

    actGameRongHo() {}

    actGameRoulette() {}

    actGameSicbo() {
        App.instance.openGame(Configs.InGameIds.Sicbo);
    }

    actGameXocDia() {}

    actGameXuatKich() {
        App.instance.openGame(Configs.InGameIds.XuatKich);
    }

    actGameLongVuong() {
        App.instance.openGame(Configs.InGameIds.LongVuong);
    }

    actGamePhiDoi() {
        App.instance.openGame(Configs.InGameIds.PhiDoi);
    }

    actGameCaMap() {
        App.instance.openGame(Configs.InGameIds.CaMap);
    }

    actGameCaKiem() {
        App.instance.openGame(Configs.InGameIds.CaKiem);
    }

    actOpenVirtualSports() {
        App.instance.openGame(Configs.InGameIds.VirtualSports);
    }

    actGoToCatte() {
        App.instance.openGame(Configs.InGameIds.Catte)
    }

    actGoToTLMN() {
        App.instance.openGame(Configs.InGameIds.TLMN);
    }

    actGoToMauBinh() {
        App.instance.openGame(Configs.InGameIds.MauBinh);
    }

    actGoToPoker() {
        App.instance.openGame(Configs.InGameIds.Poker);
    }

    actGoToSamLoc() {
        App.instance.openGame(Configs.InGameIds.SamLoc);
    }

    actGoToBaCay() {
        App.instance.openGame(Configs.InGameIds.BaCay);
    }

    _onDismissed() {
        if (!this.containerLoginBox || !this.LoginBox || !this.node.parent) return;

        const edits = this.containerLoginBox.getComponentsInChildren(EditBox);
        for (let edit of edits) {
            edit.tabIndex = -1;
        }

        this.bgLoginBox.getComponent(UIOpacity)!.opacity = 210;
        this.containerLoginBox.getComponent(UIOpacity)!.opacity = 255;
        this.containerLoginBox.setScale(1, 1, 1);

        const editsOutside = this.node.parent.getComponentsInChildren(EditBox);
        for (let i = 0; i < editsOutside.length; i++) {
            editsOutside[i].tabIndex = i + 1;
        }

        if (this.userNameInBox) this.userNameInBox.string = '';
        if (this.passwordInBox) this.passwordInBox.string = '';

        this.LoginBox.active = false;
    }

    showAnnouncement(data: any) {
        data.forEach((item: any) => {
            BundleControl.loadPrefabPopup("prefabs/PopupAnnouncement", (prefab: any) => {
                let popup = instantiate(prefab).getComponent("PopupAnnouncement");
                App.instance.popupNode.addChild(popup.node);
                popup.showDetail(item['content']);
            });
        });
    }

    showSlide(data: any) {
        BundleControl.loadPrefabPopup("prefabs/PopupSlide", (prefab: any) => {
            let popup = instantiate(prefab).getComponent("PopupSlide");
            App.instance.popupNode.addChild(popup.node);
            popup.showDetail(data);
        });
    }
}

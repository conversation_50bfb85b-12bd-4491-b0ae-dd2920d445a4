// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html
import { _decorator,EditBox,instantiate,Label,log,misc,Node, Sprite, tween, Vec3 } from "cc";
import Dialog from "../common/Dialog";
import App from "../common/App";

const {ccclass, property} =_decorator;

@ccclass
export default class PopupTutorialWheel   extends Dialog {

    @property(Node)
       itemClone:Node;
       @property(Node)
       parentScrollView:Node;
    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}
    isEmtyData:boolean = false;
    page:number = 1;
    start () {
        this.getInboxs();
    }
   
   

    mapName = {
        "na8": "2.000",
        "na34": "1.000",
        "na11": "2.500",
        "na44": "2.500",
        "na10": "2.000",
        "na9": "2.500",
        "na33": "2.000",
        "st06": "2.000",
        "US Mega Millions": "1.000",
        "US Power Ball": "1.000",
        "na41": "1.000",
        "spr117": "1.000",
        "na28": "2.000"
        
        // ,"Keno": "1.000"
       
      };

 getInboxs(){
    for (let index = 0; index < Object.keys(this.mapName).length; index++) {
        var itemNode =instantiate(this.itemClone);
        itemNode.active = true;
        this.parentScrollView.addChild(itemNode);
        itemNode.children[0].active = index%2==0;
        itemNode.children[1].getComponent(Label).string = (index+1).toString();
        itemNode.children[2].getComponent(Label).string = App.instance.getTextLang(Object.keys(this.mapName)[index]);
        itemNode.children[3].getComponent(Label).string =     this.mapName[Object.keys(this.mapName)[index]]; 
       
    }
    }
    scrollEvent(sender: any, event) {
        var thiz = this;
   
        switch(event) {
          
            case 1: 
           log("Scroll to Bottom"); 
              
            if(!this.isEmtyData){
                
              
            }
               
               break;
           
            case 6: 
           log("Bounce bottom"); 
               break;
        
               break;
        }
    }

    // update (dt) {}
}

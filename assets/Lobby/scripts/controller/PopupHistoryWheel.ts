// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { _decorator,instantiate,Label,log,Node } from "cc";
import Dialog from "../common/Dialog";
import Http from "../common/Http";
import App from "../common/App";
import Configs from "../common/Config";
import PopupSpinWheel from "./PopupSpinWheel";
import { Utils } from "../common/Utils";



const {ccclass, property} = _decorator;

@ccclass
export default class PopupHistoryWheel   extends Dialog {

    @property(Node)
       itemClone:Node;
       @property(Node)
       parentScrollView:Node;
    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}
    isEmtyData:boolean = false;

    page:number = 1;

    @property(Node)
    nodeVip:Node;
    @property(Node)
       itemVip:Node;
    start () {
        this.getInboxs();
        Http.get( Configs.App.DOMAIN_CONFIG["GetAccountTicket"], {"currencyID":Configs.Login.CurrencyID}, (status, json) => {
            App.instance.showLoading(false);
            if (status === 200) {
                 log(JSON.stringify(json));
               
               
            }  });
    }
   
    popupSpinWheel:PopupSpinWheel;
    type:number = 0;
    setData(popupSpinWheel,type=0){
        this.type = type;
        this.popupSpinWheel = popupSpinWheel;
        this.nodeVip.active = type==0?false:true;
    }

    prizes = [
        [
          { "id": 1, "prize": 100000 },
          { "id": 2, "prize": 200000 },
          { "id": 3, "prize": 300000 },
          { "id": 4, "prize": 400000 },
          { "id": 5, "prize": 500000 },
          { "id": 6, "prize": 1000000 },
          { "id": 7, "prize": 2000000 },
          { "id": 8, "prize": 3000000 },
          { "id": 9, "prize": 4000000 },
          { "id": 90, "prize": 4000000 },
          { "id": 10, "prize": 5000000 },
          { "id": 11, "prize": ******** },
          { "id": 12, "prize": "Mercedes GLC 300" },
          { "id": 13, "prize": "SH 150I" },
          { "id": 14, "prize": "Iphone" },
          { "id": 30, "prize": "+3 {0}" }
        ],
        [
            { "id": 6, "prize": 100 },
            { "id": 2, "prize": 2 },
            { "id": 5, "prize": 20 },
            { "id": 3, "prize": 5 },
        { "id": 4, "prize": 10 },
        { "id": 1, "prize": 1 }
        ]
      ];
 getInboxs(){
        App.instance.showLoading(true);
        Http.get( Configs.App.DOMAIN_CONFIG["VQMM_GetHistoryUrl"], {"type":this.type,"Page":this.page,"PageSize":10}, (status, json) => {
            App.instance.showLoading(false);
            if (status === 200) {
                // log(JSON.stringify(json));
                this.isEmtyData = false;
                var results = json['d'];
                if(results.length==0 ){
                    this.isEmtyData = true;
                    return;
                }else{
                    this.page++;
                }
               
                for (let index = 0; index < results.length; index++) {
                    if(this.type==0){
                        var itemNode = instantiate(this.itemClone);
                        itemNode.active = true;


                        
                        this.parentScrollView.addChild(itemNode);
                        itemNode.children[0].getComponent(Label).string = results[index]['spinID'];
                        itemNode.children[1].getComponent(Label).string = Utils.formatDatetime88( results[index]['createdTime']);
                        itemNode.children[2].getComponent(Label).string =     this.popupSpinWheel.mapName["20"+results[index].positionCoin]; 
                        // itemNode.children[3].getComponent(Label).string =  this.popupSpinWheel.mapName[results[index].positionPrize]; 
                        itemNode.children[3].getComponent(Label).string = App.instance.getTextLang("vqmm_rs_"+results[index].positionPrize);
                        // if(results[index]['totalPrizeValue']>0){
                        //     itemNode.children[4].getComponent(Label).string = results[index]['totalPrizeValue'] + " vàng";
                        // }
                        itemNode.children[4].getComponent(Label).string = "";
                       
                    }else{
                        var itemNode = instantiate(this.itemVip);
                        itemNode.active = true;
                        this.parentScrollView.addChild(itemNode);
                        itemNode.children[0].getComponent(Label).string = results[index]['spinID'];
                        itemNode.children[1].getComponent(Label).string = Utils.formatDatetime88( results[index]['createdTime']);
                        var rotateToIdx1 = this.prizes[0].findIndex(item => item.id === results[index].prizeID); 
                        itemNode.children[2].getComponent(Label).string =     this.prizes[0][rotateToIdx1].prize.toString(); 
                       // var rotateToIdx2 = this.prizes[0].findIndex(item => item.id === results[index].prizeID); 
                      
                        itemNode.children[3].getComponent(Label).string = results[index].multiplier.toString();
                        itemNode.children[4].getComponent(Label).string = Utils.formatNumber( results[index]['prizeValue']);
                        // if(results[index]['totalPrizeValue']>0){
                        //     itemNode.children[4].getComponent(Label).string = results[index]['totalPrizeValue'] ;
                        // }
                        // if(results[index]['turnBalance']>0){
                        //     itemNode.children[4].getComponent(Label).string = results[index]['turnBalance'] + " vé chơi";
                        // }
                    }
                   
                 
                }
               
            }  });
    }
    scrollEvent(sender: any, event) {
        var thiz = this;
   
        switch(event) {
          
            case 1: 
            log("Scroll to Bottom"); 
              
            if(!this.isEmtyData){
                this.getInboxs();
              
            }
               
               break;
           
            case 6: 
            log("Bounce bottom"); 
               break;
        
               break;
        }
    }

    // update (dt) {}
}

import { _decorator, Component, Node, EditBox, Label } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import { Global } from "db://assets/Lobby/scripts/common/Global";
import { BroadcastReceiver } from "db://assets/Lobby/scripts/common/BroadcastListener";

const {ccclass, property} = _decorator;

@ccclass("Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Sms")
export class SmsSecureForm extends Component {
    @property(EditBox)
    edbOtpSMS: EditBox = null;
    @property(EditBox)
    edbOtpTelesafe: EditBox = null;
    @property(Label)
    lblMobile: Label = null;
    @property(Label)
    lblTelesafe: Label = null;
    @property(Node)
    viewVerifySMS: Node = null;
    @property(Node)
    viewVerifyTelesafe: Node = null;

    protected onEnable() {
        var needVerifiedTelesafe = [3, 6].indexOf(Configs.Login.SecurityStatus) !== -1;
        this.viewVerifyTelesafe.active = needVerifiedTelesafe;
        this.viewVerifySMS.active = !needVerifiedTelesafe;
        this.edbOtpTelesafe.string = "";
        this.edbOtpSMS.string = "";
    }

    private verifyTelesafe() {
        const OTP = this.edbOtpTelesafe.string.trim();
        if (OTP.length === 0) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_otp_blank'));
            return;
        }
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['VerifyTeleSafe'], { OTP }, (_status, res) => {
            this.edbOtpTelesafe.string = "";
            this.edbOtpSMS.string = "";
            if (res.c < 0) {
                App.instance.showLoading(false);
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            App.instance.showLoading(false);
            this.viewVerifySMS.active = true;
            this.viewVerifyTelesafe.active = false;
        }, false);
    }

    private verifySMS() {
        const OTP = this.edbOtpSMS.string.trim();
        if (OTP.length === 0) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_otp_blank'));
            return;
        }
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['ConfirmMobile'], { OTP }, (_status, res) => {
            if (res.c < 0 && res.c !== -1023) {
                App.instance.showLoading(false);
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            App.instance.showLoading(false);
            App.instance.alertDialog.showMsg(App.instance.getTextLang('me-1023'));
            this.scheduleOnce(() => {
                Global.PopupSecurity.dismiss();
                Global.PopupProfile?.dismiss();
                BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
            }, 1);
        }, false);
    }

    private getOTPVerifySMS() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["GetSmsOtp"];
        const payload = {
            "CurrencyID": Configs.Login.CurrencyID,
            "ServiceID": Configs.SMSService.CONFIRM_PHONE,
            "Username": Configs.Login.Username
        }
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status == 200) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('se76'));
            }
        })
    }

    private getOTPVerifyTelesafe() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["GetTeleSafeOtp"];
        const payload = {
            "IsVerify": 0,
        }
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status == 200) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('se82'));
            }
        })
    }
}
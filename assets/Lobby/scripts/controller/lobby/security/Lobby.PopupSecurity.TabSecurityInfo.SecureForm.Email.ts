import { _decorator, Component, Node, EditBox, Label } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import { Global } from "db://assets/Lobby/scripts/common/Global";
import { BroadcastReceiver } from "db://assets/Lobby/scripts/common/BroadcastListener";

const { ccclass, property } = _decorator;

@ccclass("Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Email")
export class EmailSecureForm extends Component {
    @property(EditBox)
    edbOtp: EditBox = null;

    @property(Node)
    btn: Node = null;

    @property(Label)
    lblMail: Label;

    @property(Node)
    toastNode: Node = null;

    protected onEnable() {
        this.toastNode.active = false;
        this.edbOtp.string = "";
    }

    protected onLoad() {
        this.btn.on('click', () => {
            App.instance.showLoading(true);
            let otp = this.edbOtp.string.trim();
            Http.post(Configs.App.DOMAIN_CONFIG['ConfirmEmail'], {
                "OTP": otp,
            }, (_status, res) => {
                if (res.c < 0 && res.c !== -1023) {
                    App.instance.showLoading(false);
                    this.showToast(App.instance.getTextLang(`me${res.c}`));
                    return;
                }

                App.instance.showLoading(false);
                this.showToast(App.instance.getTextLang('me-1023'));
                this.scheduleOnce(() => {
                    Global.PopupSecurity.dismiss();
                    Global.PopupProfile?.dismiss();
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
                }, 1);
            }, false)
        })
    }

    showToast(message: string) {
        this.toastNode.getComponent(Label).string = message;
        this.toastNode.active = true;
        this.scheduleOnce(() => {
            this.toastNode.active = false;
        }, 3);
    }
}
import { _decorator, Component, Node, Label, Button, EditBox, Color, RichText } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import {Global} from "db://assets/Lobby/scripts/common/Global";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";

const {ccclass, property} = _decorator;

@ccclass("CancelServiceForm")
export class CancelServiceForm {
    @property(Button)
    btnGetOtp: Button = null;
    @property(EditBox)
    edbOtp: EditBox = null;
}

@ccclass("ChangePhoneForm")
export class ChangePhoneForm {
    @property(Button)
    btnGetOtp: Button = null;
    @property(EditBox)
    edbPhoneNumber: EditBox = null;
    @property(EditBox)
    edbOtp: EditBox = null;
}

@ccclass("CancelTelesafeForm")
export class CancelTelesafeForm {
    @property(Button)
    btnGetOtp: Button = null;

    @property(EditBox)
    edbOtp: EditBox = null;
}

@ccclass
export class TabSafePlus extends Component {
    @property(Label)
    infoLblUsername: Label = null;
    @property(Label)
    infoLblMobile: Label = null;
    @property(Label)
    infoLblTelesafe: Label = null;
    @property(Node)
    formCancelSMS: Node = null;
    @property(Node)
    formChangeCMS: Node = null;
    @property(Node)
    formCancelTelesafe: Node = null;
    @property(CancelServiceForm)
    cancelServiceForm: CancelServiceForm;
    @property(ChangePhoneForm)
    changePhoneForm: ChangePhoneForm;
    @property(CancelTelesafeForm)
    cancelTelesafeForm: CancelTelesafeForm;
    @property(Node)
    secureOTPContainer: Node = null;
    @property(Node)
    telesafeContainer: Node = null;
    @property(Node)
    smsContainer: Node = null;
    @property(Node)
    toastNode: Node = null;

    private _changeFormShowed: boolean = false;
    private _cancelFormShowed: boolean = false;
    private _cancelTelesafeFormShowed: boolean = false;

    protected onLoad() {
        this.toastNode.active = false;
        this.infoLblUsername.string = Configs.Login.Username;
        this.infoLblMobile.string = Configs.Login.MobilePhone?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);
        this.infoLblTelesafe.string = Configs.Login.TeleSafe?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);

        this.cancelServiceForm.btnGetOtp.node.on('click', () => {
            App.instance.showLoading(true);
            const url = Configs.App.DOMAIN_CONFIG["GetSmsOtp"];
            const payload = {
                "CurrencyID": Configs.Login.CurrencyID,
                "ServiceID": Configs.SMSService.CONFIRM_PHONE,
                "Username": Configs.Login.Username
            }
            Http.post(url, payload, (status, res) => {
                App.instance.showLoading(false);
                if (status === 200) {
                    this.showToast(App.instance.getTextLang('se76'));
                }
            })
        })

        this.changePhoneForm.btnGetOtp.node.on('click', () => {
            App.instance.showLoading(true);
            const url = Configs.App.DOMAIN_CONFIG["GetSmsOtp"];
            const payload = {
                "CurrencyID": Configs.Login.CurrencyID,
                "ServiceID": Configs.SMSService.CHANGE_PHONE,
                "Username": Configs.Login.Username
            }
            Http.post(url, payload, (status, res) => {
                App.instance.showLoading(false);
                if (status === 200) {
                    this.showToast(App.instance.getTextLang('se76'));
                }
            })
        })

        this.cancelTelesafeForm.btnGetOtp.node.on('click', () => {
            App.instance.showLoading(true);
            Http.post(Configs.App.DOMAIN_CONFIG["GetTeleSafeOtp"], {
                "Username": Configs.Login.Username,
                "IsVerify": 0
            }, (status, res) => {
                App.instance.showLoading(false);
                if (status === 200) {
                    this.showToast(App.instance.getTextLang('se82'));
                }
            })
        })
    }

    protected onEnable() {
        this.checkStatusSecureOTP();
        this.telesafeContainer.active = [3, 5, 6, 7].indexOf(Configs.Login.SecurityStatus) !== -1;
        this.smsContainer.active = [1, 4, 5, 7].indexOf(Configs.Login.SecurityStatus) !== -1;
        this.onClickButtonBack();
    }

    private checkStatusSecureOTP() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetSMSInfo'], {}, (_status, res) => {
            const isSecure = res.d.some((s: any) => s.serviceID == 1);
            this.secureOTPContainer.getChildByName('checkmark').active = isSecure;
            this.secureOTPContainer.getChildByName('text').getComponent(Label).color = isSecure ? Color.WHITE.fromHEX('#2CFB86') : Color.WHITE;
        });
    }

    private showFormCancelSMS() {
        this.formCancelSMS.active = true;
        this.formChangeCMS.active = false;
        this.formCancelTelesafe.active = false;
        this._cancelFormShowed = true;
        this._changeFormShowed = false;
        this._cancelTelesafeFormShowed = false;
    }

    private showFormChangeCMS() {
        this.formCancelSMS.active = false;
        this.formChangeCMS.active = true;
        this.formCancelTelesafe.active = false;
        this._cancelFormShowed = false;
        this._changeFormShowed = true;
        this._cancelTelesafeFormShowed = false;
    }

    private showFormCancelTelesafe() {
        this.formCancelSMS.active = false;
        this.formChangeCMS.active = false;
        this.formCancelTelesafe.active = true;
        this._cancelFormShowed = false;
        this._changeFormShowed = false;
        this._cancelTelesafeFormShowed = true;
    }

    private onClickButtonBack() {
        this.formCancelSMS.active = false;
        this.formChangeCMS.active = false;
        this.formCancelTelesafe.active = false;
        this.cancelServiceForm.edbOtp.string = "";
        this.changePhoneForm.edbOtp.string = "";
        this.changePhoneForm.edbPhoneNumber.string = "";
        this.cancelTelesafeForm.edbOtp.string = "";
    }

    private onClickContinueButton() {
        if (this._cancelFormShowed) {
            this.confirmCancelService();
        }
        if (this._changeFormShowed) {
            this.confirmChangePhone();
        }
        if (this._cancelTelesafeFormShowed) {
            this.confirmCancelTelesafe();
        }
    }

    private confirmCancelService() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG['ConfirmCancelMobile'];
        const payload = {
            "OTP": this.cancelServiceForm.edbOtp.string.trim()
        };
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.showToast(App.instance.getTextLang('me-1023'));
                this.scheduleOnce(() => {
                    Global.PopupSecurity.dismiss();
                    Global.PopupProfile?.dismiss();
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
                }, 1);
            }
        })
    }

    private confirmChangePhone() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG['ChangeMobile'];
        const payload = {
            "OldMobile": Configs.Login.MobilePhone,
            "NewMobile": this.changePhoneForm.edbPhoneNumber.string.trim(),
            "OTP": this.changePhoneForm.edbOtp.string.trim(),
        };
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.showToast(App.instance.getTextLang('me-1023'));
                this.scheduleOnce(() => {
                    Global.PopupSecurity.dismiss();
                    Global.PopupProfile?.dismiss();
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
                }, 1);
            }
        })
    }

    private confirmCancelTelesafe() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG['ConfirmCancelTeleSafe'];
        const payload = {
            "OTP": this.cancelTelesafeForm.edbOtp.string.trim()
        };
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.showToast(App.instance.getTextLang('me-1023'));
                this.scheduleOnce(() => {
                    Global.PopupSecurity.dismiss();
                    Global.PopupProfile?.dismiss();
                    BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
                }, 1);
            }
        })
    }

    showToast(message: string) {
        this.toastNode.getComponent(Label).string = message;
        this.toastNode.active = true;
        this.scheduleOnce(() => {
            this.toastNode.active = false;
        }, 3);
    }
}
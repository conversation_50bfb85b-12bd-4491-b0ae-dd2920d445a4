import { _decorator, Component, Node, EditBox, Label, RichText } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import DropDown from "db://assets/Loading/common/DropDown/Script/DropDown";

const { ccclass, property } = _decorator;

@ccclass
export class TabSecureLogin extends Component {
    @property(Node)
    enableSecure: Node = null;
    @property(Node)
    disableSecure: Node = null;
    @property(Node)
    requireAuth: Node = null;
    @property(EditBox)
    edbMinAmount: EditBox = null;
    @property(EditBox)
    edbOtp: EditBox = null;
    @property(Node)
    statusSecureNode: Node = null;
    @property(DropDown)
    selector: DropDown = null;
    @property(RichText)
    lblOtpRequire: RichText = null;
    @property(Node)
    toastNode: Node = null;

    protected onLoad() {
        this.disableSecure.active = true;
        this.requireAuth.active = false;
        this.statusSecureNode.active = false;
        this.toastNode.active = false;

        this.selector.setCallBack((index: number) => {
            switch (index) {
                case 0:
                    this.lblOtpRequire.string = App.instance.getTextLang('se26');
                    break;
                case 1:
                    this.lblOtpRequire.string = App.instance.getTextLang('txt_otp_code_note');
                    break;
            }
        })
        this.lblOtpRequire.string = App.instance.getTextLang('se26');
    }

    registerSecureLogin() {
        if ([1, 3, 4, 5, 6, 7].indexOf(Configs.Login.SecurityStatus) === -1) {
            this.requireAuth.active = true;
            this.scheduleOnce(() => {
                this.requireAuth.active = false;
            }, 3);
        } else {
            App.instance.showLoading(true);
            Http.post(Configs.App.DOMAIN_CONFIG['SMSPlusCreateService'], {MinAmount: 0}, (status, _res) => {
                App.instance.showLoading(false);
                if (status === 200) {
                    this.enableSecure.active = false;
                    this.disableSecure.active = false;
                    this.statusSecureNode.active = true;
                    this.statusSecureNode.getComponentInChildren(Label).string = App.instance.getTextLang('se34');
                }
            })
        }
    }

    protected onEnable() {
        this.updateStatus();
    }

    private updateStatus() {
        this.edbMinAmount.string = "0";
        this.edbOtp.string = "";
        Http.get(Configs.App.DOMAIN_CONFIG['GetSMSInfo'], {}, (_status, res) => {
            const isSecure = res.d.some((s: any) => s.serviceID == 1);
            if (isSecure) {
                var secureInfo = res.d.find((s: any) => s.serviceID == 1);
                this.edbMinAmount.string = secureInfo.minAmount.toLocaleString("vi-VN");
            }
            this.enableSecure.active = isSecure;
            this.disableSecure.active = !isSecure;
            this.statusSecureNode.active = false;
        });
    }

    private getOtp() {
        App.instance.showLoading(true);
        let index = this.selector.selectedIndex;
        switch (index) {
            case 0:
                Http.post(Configs.App.DOMAIN_CONFIG["GetSmsOtp"], {
                    "CurrencyID": Configs.Login.CurrencyID,
                    "ServiceID": Configs.SMSService.ACTIVE_LOGIN_OTP,
                    "Username": Configs.Login.Username
                }, (status, res) => {
                    App.instance.showLoading(false);
                    if (status === 200) {
                        this.showToast(App.instance.getTextLang('se76'));
                    }
                })
                break;
            case 1:
                Http.post(Configs.App.DOMAIN_CONFIG["GetTeleSafeOtp"], {
                    "Username": Configs.Login.Username,
                    "IsVerify": 0
                }, (status, res) => {
                    App.instance.showLoading(false);
                    if (status === 200) {
                        this.showToast(App.instance.getTextLang('se82'));
                    }
                })
                break;
        }
    }

    updateMinAmount() {
        const minAmount = parseInt(this.edbMinAmount.string);
        if (isNaN(minAmount) || minAmount < 0) {
            this.showToast(App.instance.getTextLang('me-60'));
            return;
        }

        const OTP = this.edbOtp.string.trim();
        if (OTP.length === 0) {
            this.showToast(App.instance.getTextLang('txt_otp_blank'));
            return;
        }

        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['SMSPlusUpdateService'], { MinAmount: minAmount, OTP }, (status, _res) => {
            App.instance.showLoading(false);
            this.updateStatus();
            if (status === 200) {
                this.showToast(App.instance.getTextLang('txt_success'));
            }
        });
    }

    cancelSecureLogin() {
        const OTP = this.edbOtp.string.trim();
        if (OTP.length === 0) {
            this.showToast(App.instance.getTextLang('txt_otp_blank'));
            return;
        }

        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['SMSPlusCancelService'], { OTPType: this.selector.selectedIndex === 0 ? 1 : 3, OTP }, (status, _res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.enableSecure.active = false;
                this.disableSecure.active = false
                this.statusSecureNode.active = true;
                this.statusSecureNode.getComponentInChildren(Label).string = App.instance.getTextLang('se35');
            }
        });
    }
    
    showToast(message: string) {
        this.toastNode.getComponent(Label).string = message;
        this.toastNode.active = true;
        this.scheduleOnce(() => {
            this.toastNode.active = false;
        }, 3);
    }
}
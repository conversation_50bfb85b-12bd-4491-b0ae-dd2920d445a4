import { _decorator, Component, Node, EditBox, Label } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import { Global } from "db://assets/Lobby/scripts/common/Global";
import { BroadcastReceiver } from "db://assets/Lobby/scripts/common/BroadcastListener";

const {ccclass, property} = _decorator;

@ccclass("Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Telesafe")
export class TelesafeSecureForm extends Component {
    @property(EditBox)
    edbOtpSMS: EditBox = null;
    @property(EditBox)
    edbOtpTelesafe: EditBox = null;
    @property(Label)
    lblTelesafe: Label;
    @property(Label)
    lblSMS: Label;
    @property(Node)
    viewVerifySMS: Node = null;
    @property(Node)
    viewVerifyTelesafe: Node = null;
    @property(Node)
    toastNode: Node = null;

    protected onEnable() {
        this.toastNode.active = false;
        var needVerifiedSMS = [1, 4].indexOf(Configs.Login.SecurityStatus) !== -1;
        this.viewVerifySMS.active = needVerifiedSMS;
        this.viewVerifyTelesafe.active = !needVerifiedSMS;
        this.edbOtpTelesafe.string = "";
        this.edbOtpSMS.string = "";
    }

    private verifyTelesafe() {
        const OTP = this.edbOtpTelesafe.string.trim();
        if (OTP.length === 0) {
            this.showToast(App.instance.getTextLang('txt_otp_blank'));
            return;
        }
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['ConfirmTeleSafe'], { OTP }, (_status, res) => {
            this.edbOtpTelesafe.string = "";
            this.edbOtpSMS.string = "";
            if (res.c < 0 && res.c !== -1023) {
                App.instance.showLoading(false);
                this.showToast(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            App.instance.showLoading(false);
            this.showToast(App.instance.getTextLang('me-1023'));
            this.scheduleOnce(() => {
                Global.PopupSecurity.dismiss();
                Global.PopupProfile?.dismiss();
                BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
            }, 1);
        }, false);
    }

    private verifySMS() {
        const OTP = this.edbOtpSMS.string.trim();
        if (OTP.length === 0) {
            this.showToast(App.instance.getTextLang('txt_otp_blank'));
            return;
        }
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['VerifySmsOtp'], { OTP }, (_status, res) => {
            this.edbOtpSMS.string = "";
            if (res.c < 0) {
                App.instance.showLoading(false);
                this.showToast(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            App.instance.showLoading(false);
            this.viewVerifySMS.active = false;
            this.viewVerifyTelesafe.active = true;
        });
    }

    private getOTPVerifySMS() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["GetSmsOtp"];
        const payload = {
            "CurrencyID": Configs.Login.CurrencyID,
            "ServiceID": Configs.SMSService.VERIFY_PHONE,
            "Username": Configs.Login.Username
        }
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status == 200) {
                this.showToast(App.instance.getTextLang('se76'));
            }
        })
    }

    private getOTPVerifyTelesafe() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["GetTeleSafeOtp"];
        const payload = {
            "IsVerify": 1,
        }
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status == 200) {
                this.showToast(App.instance.getTextLang('se82'));
            }
        })
    }

    showToast(message: string) {
        this.toastNode.getComponent(Label).string = message;
        this.toastNode.active = true;
        this.scheduleOnce(() => {
            this.toastNode.active = false;
        }, 3);
    }
}
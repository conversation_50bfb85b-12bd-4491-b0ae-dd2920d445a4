import { _decorator, Component, Node, Label, Toggle, EditBox } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import { EmailSecureForm } from "./Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Email";
import { SmsSecureForm } from "./Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Sms";
import { Global } from "db://assets/Lobby/scripts/common/Global";
import {TelesafeSecureForm} from "./Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Telesafe";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import { AlertDialog_v2 } from '../../../common/AlertDialog_v2';

const { ccclass, property } = _decorator;

@ccclass
export class TabSecurityInfoSecureForm extends Component {
    @property(Node)
    getOtpSecureView: Node = null;

    @property(Label)
    lblAccount: Label = null;

    @property(Label)
    lblEmail: Label = null;

    @property(Label)
    lblMobile: Label = null;

    @property(Label)
    lblTelesafe: Label = null;

    @property(Label)
    lblNickname: Label = null;

    @property(Toggle)
    changeEmailToggle: Toggle = null;

    @property(Toggle)
    changeSmsToggle: Toggle = null;

    @property(Node)
    editEmail: Node = null;

    @property(Node)
    buttonEmail: Node = null;

    @property(Node)
    buttonActiveEmail: Node = null;

    @property(Node)
    buttonSaveEmail: Node = null;

    @property(Node)
    buttonChangeEmail: Node = null;

    @property(Node)
    verifiedEmail: Node = null;

    @property(Node)
    editMobile: Node = null;

    @property(Node)
    buttonMobile: Node = null;

    @property(Node)
    buttonActiveMobile: Node = null;

    @property(Node)
    buttonSaveMobile: Node = null;

    @property(Node)
    buttonChangeMobile: Node = null;

    @property(Node)
    verifiedMobile: Node = null;

    @property(Node)
    editTelesafe: Node = null;

    @property(Node)
    buttonTelesafe: Node = null;

    @property(Node)
    buttonActiveTelesafe: Node = null;

    @property(Node)
    buttonSaveTelesafe: Node = null;

    @property(Node)
    buttonChangeTelesafe: Node = null;

    @property(Node)
    verifiedTelesafe: Node = null;

    @property(EmailSecureForm)
    emailSecureForm: EmailSecureForm = null;

    @property(SmsSecureForm)
    smsSecureForm: SmsSecureForm = null;

    @property(TelesafeSecureForm)
    telesafeSecureForm: TelesafeSecureForm = null;

    @property(Node)
    btnCapNhat: Node = null;

    @property(Node)
    toastNode: Node = null;

    private _openChangeEmail: boolean = false;
    private _openChangeMobile: boolean = false;
    private _openChangeTelesafe: boolean = false;

    protected onLoad() {
        this.toastNode.active = false;

        this.buttonChangeEmail.on('toggle', () => {
            this._openChangeEmail = !this._openChangeEmail;
            this.updateUI();
        })

        this.buttonChangeMobile.on('toggle', () => {
            this._openChangeMobile = !this._openChangeMobile;
            this.updateUI();
        })

        this.buttonChangeTelesafe.on('toggle', () => {
            this._openChangeTelesafe = !this._openChangeTelesafe;
            this.updateUI();
        })

        this.updateVerifiedUI();
        this.updateUI();
        this.emailSecureForm.lblMail.string = Configs.Login.Mail;
        this.smsSecureForm.lblMobile.string = Configs.Login.MobilePhone?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);
        this.telesafeSecureForm.lblSMS.string = Configs.Login.MobilePhone?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);
        this.telesafeSecureForm.lblTelesafe.string = Configs.Login.TeleSafe?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);
        this.smsSecureForm.lblTelesafe.string = Configs.Login.TeleSafe?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);
    }

    protected start() {
        this.lblAccount.string = Configs.Login.Username;
        this.lblEmail.string = Configs.Login.Mail;
        this.lblMobile.string = Configs.Login.MobilePhone?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);
        this.lblTelesafe.string = Configs.Login.TeleSafe?.replace(/^(.{3})(.*)(.{3})$/, (_, a, b, c) => a + '*'.repeat(b.length) + c);
        this.lblNickname.string = Configs.Login.Nickname;

        this.getOtpSecureView.getChildByName("account").getComponentInChildren(Label).string =
            Configs.Login.Username;
        this.getOtpSecureView.getChildByName("nickname").getComponentInChildren(Label).string =
            Configs.Login.Nickname;

        const isEmptyMail = Configs.Login.Mail === "";
        const isEmptyMobile = Configs.Login.MobilePhone === "";
        const isEmptyTelesafe = Configs.Login.TeleSafe === "";

        this.editEmail.active = isEmptyMail;
        this.buttonEmail.active = !isEmptyMail;
        this.editMobile.active = isEmptyMobile;
        this.buttonMobile.active = !isEmptyMobile;
        this.editTelesafe.active = isEmptyTelesafe;
        this.buttonTelesafe.active = !isEmptyTelesafe;
    }

    /// 0: Chưa confirm gì
    /// 1:Confirm Mobile
    /// 2:Confirm Email
    /// 3:Confirm Telesafe
    /// 4: Confirm Email và Mobile
    /// 5: confirm Telesafe và Mobile
    /// 6: confirm Telesafe và Email
    /// 7: confirm Telesafe Email và Mobile
    updateVerifiedUI() {
        const status = Configs.Login.SecurityStatus;
        let verifiedEmail = [2, 4, 6, 7].indexOf(status) !== -1;
        let verifiedMobile = [1, 4, 5, 7].indexOf(status) !== -1;
        let verifiedTelesafe = [3, 5, 6, 7].indexOf(status) !== -1;

        this.verifiedEmail.active = verifiedEmail;
        this.verifiedMobile.active = verifiedMobile;
        this.verifiedTelesafe.active = verifiedTelesafe;

        // in active button save and changed if verified
        this.buttonActiveEmail.active = !verifiedEmail;
        this.buttonActiveMobile.active = !verifiedMobile;
        this.buttonActiveTelesafe.active = !verifiedTelesafe;

        this.buttonChangeEmail.active = !verifiedEmail;
        this.buttonChangeMobile.active = !verifiedMobile;
        this.buttonChangeTelesafe.active = !verifiedTelesafe;
    }


    showOTPEmail() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["SendMailConfirm"];
        Http.post(url, {}, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.node.active = false;
                this.getOtpSecureView.active = true;
                this.smsSecureForm.node.active = false;
                this.emailSecureForm.node.active = true;
                this.telesafeSecureForm.node.active = false;
            }
        })
    }

    showOTPSMS() {
        this.node.active = false;
        this.getOtpSecureView.active = true;
        this.smsSecureForm.node.active = true;
        this.emailSecureForm.node.active = false;
        this.telesafeSecureForm.node.active = false;
    }

    showOTPTelesafe() {
        this.node.active = false;
        this.getOtpSecureView.active = true;
        this.smsSecureForm.node.active = false;
        this.emailSecureForm.node.active = false;
        this.telesafeSecureForm.node.active = true;
    }

    private saveEmailButton() {
        let mobileEdb = this.editEmail.getComponent(EditBox).string;
        this.updateAccountInfo({
            Email: mobileEdb.trim(),
        })
    }

    private saveSmsButton() {
        let newPhone = this.editMobile.getComponent(EditBox).string;
        if (newPhone.trim() === "") {
            this.showToast(App.instance.getTextLang("txt_phone_number_incorrect"));
            return;
        }

        if (newPhone === Configs.Login.MobilePhone) {
            this.showToast(App.instance.getTextLang("txt_phone_number_incorrect2"));
            return;
        }
        this.updateAccountInfo({
            Mobile: newPhone.trim()
        })
    }

    private saveTelesafeButton() {
        let teleSafeEdb = this.editTelesafe.getComponent(EditBox).string;
        if (teleSafeEdb.trim() === "") {
            this.showToast(App.instance.getTextLang("txt_phone_number_incorrect"));
            return;
        }

        if (teleSafeEdb === Configs.Login.TeleSafe) {
            this.showToast(App.instance.getTextLang("txt_phone_number_incorrect2"));
            return;
        }
        this.updateAccountInfo({
            TeleSafe: teleSafeEdb.trim()
        })
    }

    backToSecureForm() {
        this.node.active = true;
        this.getOtpSecureView.active = false;
        
    }

    private updateAccountInfo(payload: {}) {
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['UpdateAccountUrl'], payload, (status, res) => {
            if (res.c < 0 && res.c !== -1023) {
                App.instance.showLoading(false);
                this.showToast(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            App.instance.showLoading(false);
            AlertDialog_v2.create().then(dialog => {
                dialog.showDialog(App.instance.getTextLang('me-1023'), 1);
            })
            this.scheduleOnce(() => {
                Global.PopupSecurity.dismiss();
                Global.PopupProfile?.dismiss();
                BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
            }, 1);
        }, false)
    }

    updateSecurityInfo() {
        let emailEdb = this.editEmail.getComponent(EditBox).string;
        let mobileEdb = this.editMobile.getComponent(EditBox).string;
        let teleSafeEdb = this.editTelesafe.getComponent(EditBox).string;
        this.updateAccountInfo({
            Email: emailEdb.trim(),
            Mobile: mobileEdb.trim(),
            TeleSafe: teleSafeEdb.trim(),
        })
    }

    updateButtonUpdate() {
        this.btnCapNhat.active = !(this._openChangeEmail || this._openChangeMobile || this._openChangeTelesafe);
        if(Configs.Login.SecurityStatus === 7){
            this.btnCapNhat.active = false;
        }
    }

    updateUI() {
        const cancel: string = App.instance.getTextLang("txt_cancel");
        const change: string = App.instance.getTextLang("se17");

        if (!this.verifiedEmail.active) {
            this.buttonActiveEmail.active = !this._openChangeEmail;
            this.buttonSaveEmail.active = this._openChangeEmail;
            this.buttonChangeEmail.getComponentInChildren(Label).string = this._openChangeEmail ? cancel : change;
        }

        if (!this.verifiedMobile.active) {
            this.buttonActiveMobile.active = !this._openChangeMobile;
            this.buttonSaveMobile.active = this._openChangeMobile;
            this.buttonChangeMobile.getComponentInChildren(Label).string = this._openChangeMobile ? cancel : change;
        }

        if (!this.verifiedTelesafe.active) {
            this.buttonActiveTelesafe.active = !this._openChangeTelesafe;
            this.buttonSaveTelesafe.active = this._openChangeTelesafe;
            this.buttonChangeTelesafe.getComponentInChildren(Label).string = this._openChangeTelesafe ? cancel : change;
        }

        this.editEmail.getComponent(EditBox).string = "";
        this.editMobile.getComponent(EditBox).string = "";
        this.editTelesafe.getComponent(EditBox).string = "";

        this.updateButtonUpdate();
    }

    showToast(message: string) {
        this.toastNode.getComponent(Label).string = message;
        this.toastNode.active = true;
        this.scheduleOnce(() => {
            this.toastNode.active = false;
        }, 3);
    }
}
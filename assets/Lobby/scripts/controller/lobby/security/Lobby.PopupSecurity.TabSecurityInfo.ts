import { _decorator, Component, Label, EditBox, Node, RichText } from "cc";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import { TabSecurityInfoSecureForm } from "./Lobby.PopupSecurity.TabSecurityInfo.SecureForm";
import { Global } from "db://assets/Lobby/scripts/common/Global";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";

const { ccclass, property } = _decorator;

@ccclass
export class TabSecurityInfo extends Component {
    @property(Label)
    infoLblUsername: Label = null;

    @property(EditBox)
    updateEdbEmail: EditBox = null;

    // @property(Node)
    // warnEdbEmail: Node = null;

    @property(EditBox)
    updateEdbPhoneNumber: EditBox = null;

    // @property(Node)
    // warnEdbPhoneNumber: Node = null;

    @property(EditBox)
    updateEdbTeleSafe: EditBox = null;

    // @property(Node)
    // warnEdbTeleSafe: Node = null;

    @property(Node)
    updateButton: Node = null;

    @property(TabSecurityInfoSecureForm)
    secureForm: TabSecurityInfoSecureForm = null;

    @property(Node)
    editForm: Node = null;

    @property(RichText)
    secureNote: RichText = null;

    @property(Node)
    toastNode: Node = null;

    onLoad(){
        this.toastNode.active = false;
    }

    protected onEnable() {
        this.infoLblUsername.string = Configs.Login.Username;
        this.node.children.forEach(child => child.active = false);
        const isSecure = Configs.Login.SecurityStatus !== 0;
        this.editForm.active = !isSecure;
        this.secureForm.node.active = isSecure;
        if (isSecure) {
            this.secureForm.backToSecureForm();
        }

        if (Configs.Login.Mail == "" && Configs.Login.MobilePhone == "" && Configs.Login.TeleSafe == "") {
            this.editForm.active = true;
            this.secureForm.node.active = false;
        } else {
            this.editForm.active = false;
            this.secureForm.node.active = true;
        }
    }

    protected start() {
        const mobilePhone = Configs.Login.MobilePhone || "99999999"
        this.secureNote.string = App.instance.getTextLang("txt_describe_secure").replace('{0}', mobilePhone);

        if (Configs.Login.Mail == "" && Configs.Login.MobilePhone == "" && Configs.Login.TeleSafe == "") {
            this.editForm.active = true;
            this.secureForm.node.active = false;
        } else {
            this.editForm.active = false;
            this.secureForm.node.active = true;
        }
    }


    private onUpdatePressed() {
        let email = this.updateEdbEmail.string.trim();
        let mobile = this.updateEdbPhoneNumber.string.trim();
        let teleSafe = this.updateEdbTeleSafe.string.trim();
        let params: any = {};

        if (email.length == 0 && mobile.length == 0 && teleSafe.length == 0) {
            // App.instance.alertDialog.showMsg(App.instance.getTextLang('me-999009'));
            this.showToast(App.instance.getTextLang('me-999009'));
            return;
        }

        App.instance.showLoading(true);

        if (email.toString() !== "") {
            params.Email = email;
        }

        if (mobile.toString() !== "") {
            params.Mobile = mobile;
        }

        if (teleSafe.toString() !== "") {
            params.TeleSafe = teleSafe;
        }

        const url = Configs.App.DOMAIN_CONFIG["UpdateAccountUrl"];
        Http.post(url, params, (_status, res) => {
            if (res.c < 0 && res.c !== -1023) {
                App.instance.showLoading(false);
                // App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                this.showToast(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            App.instance.showLoading(false);
            // App.instance.alertDialog.showMsg(App.instance.getTextLang('me-1023'));
            this.showToast(App.instance.getTextLang('me-1023'));
            this.scheduleOnce(() => {
                Global.PopupSecurity.dismiss();
                Global.PopupProfile?.dismiss();
                BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
            }, 1);
        }, false)
    }

    showToast(message: string) {
        this.toastNode.getComponent(Label).string = message;
        this.toastNode.active = true;
        this.scheduleOnce(() => {
            this.toastNode.active = false;
        }, 3);
    }
}
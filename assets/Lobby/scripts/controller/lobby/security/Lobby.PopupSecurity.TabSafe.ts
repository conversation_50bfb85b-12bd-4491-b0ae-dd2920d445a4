import { _decorator, Component, Node, Label, ToggleContainer, EditBox, RichText, Color } from 'cc';
import DropDown from "db://assets/Loading/common/DropDown/Script/DropDown";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";

const {ccclass, property} = _decorator;

@ccclass
export class TabSafe extends Component {
    @property(Label)
    infoLblUsername: Label = null;

    @property(ToggleContainer)
    action: ToggleContainer = null;

    @property([Node])
    contents: Node[] = [];

    @property(EditBox)
    edbLockBalance: EditBox = null;

    @property(EditBox)
    edbUnlockBalance: EditBox = null;

    @property(Label)
    lblUnlockBalance: Label = null;

    @property(Label)
    lblLockBalance: Label = null;

    @property(DropDown)
    selector: DropDown = null;

    @property(EditBox)
    edbOTP: EditBox = null;

    @property(RichText)
    lblOtpRequire: RichText = null;

    @property(Label)
    lblAccounce: Label = null;

    @property(Node)
    toastNode: Node = null;

    tabSelectedIndex: number = 0;

    private _unlockBalance: number;
    public get unlockBalance(): number {
        return this._unlockBalance;
    }
    public set unlockBalance(value: number) {
        this._unlockBalance = value;
        this.lblUnlockBalance.string = Utils.formatNumber(value);
    }

    private _lockBalance: number;
    public get lockBalance(): number {
        return this._lockBalance;
    }
    public set lockBalance(value: number) {
        this._lockBalance = value;
        this.lblLockBalance.string = Utils.formatNumber(value);
    }

    protected onLoad() {
        this.edbLockBalance.node.on("editing-did-ended", () => {
            let number = Utils.stringToInt(this.edbLockBalance.string);
            this.edbLockBalance.string = Utils.formatNumber(number);
        })

        this.edbUnlockBalance.node.on("editing-did-ended", () => {
            let number = Utils.stringToInt(this.edbUnlockBalance.string);
            this.edbUnlockBalance.string = Utils.formatNumber(number);
        })

        for (let i = 0; i < this.action.toggleItems.length; i++) {
            this.action.toggleItems[i].node.on("toggle", () => {
                this.tabSelectedIndex = i;
                this.onTabChanged();
            });
        }
        this.onTabChanged();

        this.selector.setCallBack((index: number) => {
            switch (index) {
                case 0:
                    this.lblOtpRequire.string = App.instance.getTextLang('se26');
                    break;
                case 1:
                    this.lblOtpRequire.string = App.instance.getTextLang('se81');
                    break;
            }
        })
        this.lblOtpRequire.string = App.instance.getTextLang('se26');

        this.onTabChanged();
        this.infoLblUsername.string = Configs.Login.Username;
    }

    protected start() {
        App.instance.showLoading(true);
    }

    protected onEnable() {
        this.toastNode.active = false;
        this.updateStockAccount();
        this.lblAccounce.string = "";
    }

    updateStockAccount() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetStockAccount'], {CurrencyID: Configs.Login.CurrencyID}, (status, res) => {
            App.instance.showLoading(false);
            if (res.c >= 0) {
                this.unlockBalance = res["d"]["goldBalance"];
                this.lockBalance = res["d"]["stockBalance"];
                this.edbLockBalance.string = "";
                this.edbUnlockBalance.string = "";
                this.edbOTP.string = "";
            }
        })
    }

    private onTabChanged() {
        this.contents.forEach((item, index) => {
            this.contents[index].active = index === this.tabSelectedIndex;
        })
        for (let i = 0; i < this.action.toggleItems.length; i++) {
            this.action.toggleItems[i].node.getComponentInChildren(Label).color = i === this.tabSelectedIndex ? Color.BLACK.fromHEX("#4ef1ac") : Color.WHITE;
        }
        this.lblAccounce.string = "";
        switch (this.tabSelectedIndex) {
            case 0:
                this.edbLockBalance.string = "";
                break;
            case 1:
                this.edbUnlockBalance.string = "";
                this.edbOTP.string = "";
                break;
        }
    }

    private lockBalancePressed() {
        let coin = Utils.stringToInt(this.edbLockBalance.string);
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG["LockBalance"], {
            "CurrencyID": Configs.Login.CurrencyID,
            "Amount": coin
        }, (status, res) => {
            App.instance.showLoading(false);
            if (res.c >= 0) {
                this.unlockBalance = res["d"]["balance"];
                this.lockBalance = res["d"]["stock"];
                this.edbLockBalance.string = "";
                this.lblAccounce.string = App.instance.getTextLang("se49");
                this.scheduleOnce(() => {
                    this.lblAccounce.string = "";
                }, 3);
            }
        })
    }

    /** Registered in editor */
    private sendOtp() {
        App.instance.showLoading(true);
        let index = this.selector.selectedIndex;
        switch (index) {
            case 0:
                Http.post(Configs.App.DOMAIN_CONFIG["GetSmsOtp"], {
                    "CurrencyID": Configs.Login.CurrencyID,
                    "ServiceID": Configs.SMSService.SAFE_BALANCE,
                    "Username": Configs.Login.Username
                }, (status, res) => {
                    App.instance.showLoading(false);
                    if (status === 200) {
                        this.showToast(App.instance.getTextLang('se76'));
                    }
                })
                break;
            case 1:
                Http.post(Configs.App.DOMAIN_CONFIG["GetTeleSafeOtp"], {
                    "Username": Configs.Login.Username,
                    "IsVerify": 0
                }, (status, res) => {
                    App.instance.showLoading(false);
                    if (status === 200) {
                        this.showToast(App.instance.getTextLang('se82'));
                    }
                })
                break;
        }
    }

    private unlockBalancePressed() {
        const OTP = this.edbOTP.string.trim();
        if (OTP.length === 0) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_otp_blank'));
            return;
        }

        App.instance.showLoading(true);
        let coin = Utils.stringToInt(this.edbUnlockBalance.string);
        Http.post(Configs.App.DOMAIN_CONFIG["UnLockBalance"], {
            "CurrencyID": Configs.Login.CurrencyID,
            "Amount": coin
        }, (_status, res) => {
            App.instance.showLoading(false);
            if (res.c >= 0) {
                Http.post(Configs.App.DOMAIN_CONFIG["ConfirmUnLockBalance"], {
                    "OTPType": this.selector.selectedIndex === 0 ? 1 : 3,
                    OTP
                }, (_status, res) => {
                    App.instance.showLoading(false);
                    if (res.c >= 0) {
                        this.updateStockAccount();
                        this.lblAccounce.string = App.instance.getTextLang("se50");
                        this.scheduleOnce(() => {
                            this.lblAccounce.string = "";
                        }, 3);
                    }
                })
            }
        })
    }

}
import { _decorator, Component, Node, instantiate, Toggle, Label, Color, EditBox, RichText } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import DropDown from "db://assets/Loading/common/DropDown/Script/DropDown";

const { ccclass, property } = _decorator;

class ItemGameDataComponent extends Component {
    @property
    gameData: ItemGameData = null; // Store the game data here
}

@ccclass
export class TabManageGame extends Component {
    @property(Node)
    gameTable: Node = null;

    @property(Node)
    itemGameTemplate: Node = null;

    @property(DropDown)
    selector: DropDown = null;

    @property(EditBox)
    edbOtp: EditBox = null;

    @property(RichText)
    lblOtpRequire: RichText = null;

    selectedBlockGameID: number[] = [];

    onLoad() {
        this.selector.setCallBack((index: number) => {
            switch (index) {
                case 0:
                    this.lblOtpRequire.string = App.instance.getTextLang('se26');
                    break;
                case 1:
                    this.lblOtpRequire.string = App.instance.getTextLang('se81');
                    break;
            }
        })
        this.lblOtpRequire.string = App.instance.getTextLang('se26');
    }

    protected onEnable() {
        this.initialize();
    }

    public initialize() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["GetListGame"];
        Http.get(url, {}, (status, res) => {
            if (status === 200) {
                App.instance.showLoading(false);
                this.updateGameTable(res.d);
            }
        })
    }

    private updateGameTable(gameList: ItemGameData[]) {
        this.selectedBlockGameID = [];
        this.gameTable.removeAllChildren();

        for (let data of gameList) {
            let spawner = instantiate(this.itemGameTemplate);

            spawner.addComponent(ItemGameDataComponent).gameData = data;

            spawner.getChildByName("Toggle").on("toggle", () => {
                let isChecked = spawner.getComponentInChildren(Toggle).isChecked;
                if (isChecked) {
                    spawner.getComponentInChildren(Label).color = Color.BLACK.fromHEX("#4ef1ac");
                    const index = this.selectedBlockGameID.indexOf(data.gameID);
                    if (index !== -1) {
                        this.selectedBlockGameID.splice(index, 1);
                    }
                } else {
                    spawner.getComponentInChildren(Label).color = Color.WHITE;
                    if (this.selectedBlockGameID.indexOf(data.gameID) === -1) {
                        this.selectedBlockGameID.push(data.gameID);
                    }
                }
            })
            
            const gameName = Configs.InGameIds.getGameName(data.gameID);
            spawner.getComponentInChildren(Label).string = App.instance.getTextLang(gameName);
            spawner.getComponentInChildren(Toggle).isChecked = data.enable;
            spawner.getComponentInChildren(Label).color = data.enable ? Color.BLACK.fromHEX("#4ef1ac") : Color.WHITE;
            if (!data.enable) {
                if (this.selectedBlockGameID.indexOf(data.gameID) === -1) {
                    this.selectedBlockGameID.push(data.gameID);
                }
            }
            spawner.active = true;
            this.gameTable.addChild(spawner)
        }
    }

    private sendOtp() {
        App.instance.showLoading(true);
        let index = this.selector.selectedIndex;
        switch (index) {
            case 0:
                Http.post(Configs.App.DOMAIN_CONFIG["GetSmsOtp"], {
                    "CurrencyID": Configs.Login.CurrencyID,
                    "ServiceID": Configs.SMSService.MANAGE_GAME,
                    "Username": Configs.Login.Username
                }, (status, res) => {
                    App.instance.showLoading(false);
                    if (status === 200) {
                        App.instance.alertDialog.showMsg(App.instance.getTextLang('se76'));
                    }
                })
                break;
            case 1:
                Http.post(Configs.App.DOMAIN_CONFIG["GetTeleSafeOtp"], {
                    "Username": Configs.Login.Username,
                    "IsVerify": 0
                }, (status, res) => {
                    App.instance.showLoading(false);
                    if (status === 200) {
                        App.instance.alertDialog.showMsg(App.instance.getTextLang('se82'));
                    }
                })
                break;
        }
    }

    private confirmBlockGamePressed() {
        let otp = this.edbOtp.string.trim();
        let index = this.selector.selectedIndex;
        let otpType: eOtpType = index === 0 ? eOtpType.SmsOtp : eOtpType.TeleSafeOtp;
        let listGameBlocked = this.selectedBlockGameID.join(",");

        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["ConfirmBlockGame"];
        Http.post(url, { "OTP": otp, "OtpType": otpType, "GameIDs": listGameBlocked }, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.edbOtp.string = "";
                this.initialize();
                App.instance.alertDialog.showMsg(App.instance.getTextLang("se51"));
            }
        })
    }
}

export interface ItemGameData {
    gameID: number;
    type: number;
    name: string;
    enable: boolean;
}

export enum eOtpType {
    SmsOtp = 1,
    AppOtp,
    TeleSafeOtp,
}
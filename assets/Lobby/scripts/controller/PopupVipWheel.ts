import { _decorator, Component, Label,    Node, log, Sprite, misc, instantiate, tween, Vec3 } from "cc";
import Http from "../common/Http";
import Configs from "../common/Config";
import { LanguageManager } from "../common/language/Language.LanguageManager";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import App from "../common/App";
import { BroadcastReceiver } from "../common/BroadcastListener";


const { ccclass, property } = _decorator;

@ccclass
export default class PopupVipWheel extends Component {


    @property(Node)
    wheel3: Node = null;
    @property(Node)
    wheel1: Node = null;
    
    @property(Label)
    lblDoneNumber: Label = null;


    private isSpinning = false;
    prizes = [
        [
          { "id": 1, "prize": 100000 },
          { "id": 2, "prize": 200000 },
          { "id": 3, "prize": 300000 },
          { "id": 4, "prize": 400000 },
          { "id": 5, "prize": 500000 },
          { "id": 6, "prize": 1000000 },
          { "id": 7, "prize": 2000000 },
          { "id": 8, "prize": 3000000 },
          { "id": 9, "prize": 4000000 },
          { "id": 90, "prize": 4000000 },
          { "id": 10, "prize": 5000000 },
          { "id": 11, "prize": 10000000 },
          { "id": 12, "prize": "Mercedes GLC 300" },
          { "id": 13, "prize": "SH 150I" },
          { "id": 14, "prize": "Iphone" },
          { "id": 30, "prize": "+3 {0}" }
        ],
        [
            { "id": 6, "prize": 100 },
            { "id": 2, "prize": 2 },
            { "id": 5, "prize": 20 },
            { "id": 3, "prize": 5 },
        { "id": 4, "prize": 10 },
        { "id": 1, "prize": 1 }
        ]
      ];
      @property(Node)
      rewardNode: Node = null;
    start() {

        const item = this.prizes[0].find(p => p.id === 1);
        log(item.prize);
        Http.get(Configs.App.DOMAIN_CONFIG['VQMM_GetTurnUrl'], { type: 1 }, (status, res) => {
            try {
                log(JSON.stringify(res));
                // App.instance.alertDialog.showMsg(status);
               
                if (status === 200 && res.c == 0) {
                    if (+res?.d > 0) {
                        this.lblDoneNumber.string = res?.d.toString();
                    } else {
                        this.lblDoneNumber.string = "0";
                    }
                   
                    const arr =  res.m.split(";").map(Number);
                    this.mapPrizeData(arr);
                    this.arrID = arr;
                    // Find the position of the number 3
                   
                }
                this.isSpinning = false;
                App.instance.showLoading(false);
            } catch (error) {
                // App.instance.showLoading(false);
                // App.instance.alertDialog.showMsg(App.instance.getTextLang("ca-10000"));
            }
        });
    }

    private mapPrizeData(data) {
       
        log(data);
        // prizes.sort((a, b) => a.position - b.position);

        for (let index = 0; index < data.length; index++) {
            const imageNode = this.wheel3.children[this.wheel3.children.length-1-index ]; // Lấy Node ảnh tại vị trí tương ứng
            if (!imageNode) return;

            const spriteComponent = imageNode.getComponent(Sprite);
            
            var sprite = this.rewardNode.children.find(img => img.name === `lucky-vip-${data[index]}`);
            if(LanguageManager.instance.locale == "vi" && (data[index]==12 || data[index] ==30)){
                 sprite = this.rewardNode.children.find(img => img.name === `lucky-vip-${data[index]}-vi`);
            }
            if (sprite) {
                const spriteComponent = imageNode.getComponent(Sprite);
                if (spriteComponent) {
                    spriteComponent.spriteFrame = sprite.getComponent(Sprite).spriteFrame; // Gán ảnh vào vòng quay
                }
            }
            
        }

        
        // Sắp xếp lại ảnh trên vòng quay
        this.arrangeImagesInCircle();
    }
    private circleRadius: number = 260;
    private arrangeImagesInCircle() {
        const numberOfImages = this.wheel3.children.length; // Số lượng ảnh con
        const angleStep = 360 / numberOfImages;

        for (let i = 0; i < numberOfImages; i++) {
            const imageNode = this.wheel3.children[i]; // Lấy ảnh con thứ i

            // Tính toán góc hiện tại
            const angle = angleStep * i + 15+90;

            // Tính toán vị trí dựa trên góc và bán kính
            const radians = misc.degreesToRadians(angle);
            const x = this.circleRadius * Math.cos(radians);
            const y = this.circleRadius * Math.sin(radians);
            imageNode.setPosition(x, y);

            // Xoay ảnh sao cho vector BA hướng về tâm
            imageNode.angle = angle - 90; // +90 để điểm A hướng về tâm
        }
    }
   

    dismiss(){
        this.node.destroy();
    }
    arrID:number[] = [];
    @property(Node)
    nodeNotify: Node = null;
    @property(Label)
    lblNamereward: Label = null;
    @property(Sprite)
    imgWin: Sprite = null;

clickHistory(){
        let cb = (prefab) => {
            let popup = instantiate(prefab)
              .getComponent("PopupHistoryWheel");
            popup.node.parent = App.instance.node;
            popup.setData(this,1);
            popup.show();
          };
          BundleControl.loadPrefabPopup("prefabs/PopupHistoryWheel", cb);
    }
    clickTutorial(){
        // let cb = (prefab) => {
        //     let popup = cc
        //       .instantiate(prefab)
        //       .getComponent("PopupTutorialWheel");
        //     popup.node.parent = App.instance.node;
           
        //     popup.show();
        //   };
        //   BundleControl.loadPrefabPopup("PrefabPopup/PopupTutorialWheel", cb);
    }

    actRotate() {
        
        if(this.lblDoneNumber.string == "0"){
            App.instance.alertDialog.showMsg(App.instance.getTextLang("vqmm6"));
            return;
        }
        

        if (this.isSpinning) {
            // App.instance.alertDialog.showMsg("Đang quay, vui lòng chờ...");
            return;
        }
        this.isSpinning = true;
     

         const CurrencyID = Configs.Login.CurrencyID;
                let data = {
                    "CurrencyID": CurrencyID,
                    "type": 1,
                };
                App.instance.showLoading(true);
                Http.post(Configs.App.DOMAIN_CONFIG['VQMM_GetSpinUrl'], data, (status, res) => {
                    // App.instance.alertDialog.showMsg(res?.c);
        
                    if (status !== 200 || res?.c !== 0) {
                     
                        return;
                    }
        
                    App.instance.showLoading(false);
                    // const item = this.prizes[0].find(p => p.id === res.d.item1ID);
                    // log(item.prize);
                    var iddd = res.d.item1ID;
        var sprite = this.rewardNode.children.find(img => img.name === `lucky-vip-${iddd}`);
        if(LanguageManager.instance.locale == "vi" && (iddd==12 || iddd==30)){
             sprite = this.rewardNode.children.find(img => img.name === `lucky-vip-${iddd}-vi`);
        }
        this.imgWin.spriteFrame = sprite.getComponent(Sprite).spriteFrame; // Gán ảnh vào vòng quay
        
        this.lblNamereward.string = App.instance.getTextLang("sl59")+ ": " +     this.prizes[0].find(p => p.id === iddd).prize;
                    var rotateToIdx3 = this.arrID.indexOf(iddd);
                    var rotateToIdx1 = this.prizes[1].findIndex(item => item.id === res.d.item2ID); 
                  const angle1 = -(360 * 4 + (360 - 360 / 6 * rotateToIdx1) - 360 / 6 / 2);
                    tween(this.wheel1)
                        .to(3, { eulerAngles: new Vec3(0, 0, angle1) }, { easing: 'sineInOut' })
                        .start();

                    // Wheel3: Sequence tween
                    const angle3 = 360 * 4 + 360 / 12 * rotateToIdx3 + 360 / 12 / 2;

                    tween(this.wheel3)
                        .delay(0.25)
                        .to(4, { eulerAngles: new Vec3(0, 0, -angle3) }, { easing: 'sineInOut' })  // âm nếu quay ngược
                        .delay(1)
                        .call(() => {
                            this.lblDoneNumber.string = res.d?.spinBalance.toString();
                            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                            this.nodeNotify.active = true;
                        })
                        .delay(4)
                        .call(() => {
                            this.nodeNotify.active = false;
                            this.isSpinning = false;
                        })
                        .start();   
                });

      
    }
}

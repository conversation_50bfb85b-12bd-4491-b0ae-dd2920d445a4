// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

// import Configs from "../MoveScript/Configs";
// import Http from "../MoveScript/Http";
// import Tween from "./Script/common/Tween";
// import UtilsNative from "../MoveScript/UtilsNative";
// import App from "./Script/common/App";
// import Utils from "./Script/common/Utils";
// import BroadcastReceiver from "./Script/common/BroadcastReceiver";
// import SPUtils from "./Script/common/SPUtils";

import { _decorator, Component, Node, Color, Label, Toggle, EditBox, Sprite, RichText, instantiate, UIOpacity, tween, Vec3, easing, sys, Tween, Button, ToggleContainer, SpriteFrame, UITransform } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import Http from "db://assets/Lobby/scripts/common/Http";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
const { ccclass, property } = _decorator;

// const {ccclass, property} = cc._decorator;
// var TW = cc.tween

@ccclass('PopupCashout')
export default class PopupCashout extends Component {
    @property(Node)
    bg: Node = null;
    @property(Node)
    container: Node = null;

    @property([Node])
    tabContents: Node[] = [];
    @property(Node)
    toggleGroup: Node = null;
    @property([Toggle])
    tabToggleMain: Toggle[] = [];

    @property(Node)
    containerMenuCardWithdraw: Node = null;
    @property(Node)
    containerStep1CardWithdraw: Node = null;
    @property(Node)
    containerStep2CardWithdraw: Node = null;
    @property(Node)
    containerStep3CardWithdraw: Node = null;
    @property(Node)
    withdrawCardTableList: Node = null;
    @property(Node)
    withdrawCardTableItem: Node = null;
    @property(Node)
    withdrawCardDropList: Node = null;
    @property(Node)
    withdrawCardDropListItem: Node = null;
    @property(Node)
    withdrawCardDropListCost: Node = null;
    @property(Node)
    withdrawCardDropListCostItem: Node = null;
    @property(Label)
    withdrawCardDetailLabel: Label = null;
    @property(Label)
    withdrawCardCostLabel: Label = null;
    @property(EditBox)
    withdrawEdbQualityContent: EditBox = null;
    @property(Label)
    withdrawCardTotalLabel: Label = null;
    @property(Label)
    withdrawCardOTPOptionLabel: Label = null;
    @property(Toggle)
    withdrawCardOTPOptionToggle: Toggle = null;

    @property(EditBox)
    withdrawCardEdbOTP: EditBox = null;

    @property(Node)
    withdrawCardListStep3: Node = null;

    @property(Node)
    withdrawCardListStep3Item: Node = null;

    @property(Node)
    withdrawCardContainerOTPStep2: Node = null;

    private selectedProductID: string = "";
    private selectedPromotion: number = 0;


    @property(Node)
    containerStep1BankVNWithdraw: Node = null;
    @property(Node)
    containerStep2BankVNWithdraw: Node = null;
    @property(Node)
    containerStep3BankVNWithdraw: Node = null;
    @property(Label)
    withdrawBankVNBalance: Label = null;
    @property(Label)
    withdrawBankVNValuePlaceholder: Label = null;
    @property(Label)
    withdrawBankVNBalanceToday: Label = null;
    @property(Node)
    withdrawBankVNListItems: Node = null;
    @property(Node)
    withdrawBankVNListItemTemplate: Node = null;
    @property(Label)
    withdrawBankVNSelectedBankLabel: Label = null;
    @property(Toggle)
    withdrawBankVNSelectedBankToggle: Toggle = null;
    @property(EditBox)
    withdrawBankVNEdbValue: EditBox = null;
    @property(EditBox)
    withdrawBankVNEdbNumberBank: EditBox = null;
    @property(EditBox)
    withdrawBankVNEdbAccountBank: EditBox = null;
    @property(Label)
    withdrawBankVNStep2BankName: Label = null;
    @property(Label)
    withdrawBankVNStep2BankAccount: Label = null;
    @property(Label)
    withdrawBankVNStep2BankNumber: Label = null;
    @property(Label)
    withdrawBankVNStep2BankAmout: Label = null;
    @property(Label)
    withdrawBankVNStep2BankValue: Label = null;
    @property(Node)
    withdrawBankVNStep2OTPContainer: Node = null;
    @property(EditBox)
    withdrawBankVNEdbStep2OTP: EditBox = null;
    @property(Toggle)
    withdrawBankVNOTPOptionToggle: Toggle = null;
    @property(Label)
    withdrawBankVNOTPOptionLabel: Label = null;
    @property(Label)
    withdrawBankVNStep3TransactionIDLabel: Label = null;

    @property(Node)
    containerStep1CryptoWithdraw!: Node;
    @property(Node)
    containerStep2CryptoWithdraw!: Node;
    @property(Node)
    containerStep3CryptoWithdraw!: Node;
    @property(Label)
    withdrawCryptoStep1LabelCrypto!: Label;
    @property(Label)
    withdrawCryptoStep2LabelCrypto!: Label;
    @property(Label)
    withdrawCryptoStep3LabelCrypto!: Label;
    @property(Label)
    withdrawCryptoStep1PlaceholderWalletNumber!: Label;
    @property(Label)
    withdrawCryptoStep1PlaceholderAmount!: Label;
    @property(Label)
    withdrawCryptoStep1LabelToday!: Label;
    @property(EditBox)
    withdrawCryptoStep1EdbWalletNumber!: EditBox;
    @property(EditBox)
    withdrawCryptoStep1EdbAmount!: EditBox;
    @property(Label)
    withdrawCryptoStep2ValueAddress!: Label;
    @property(Label)
    withdrawCryptoStep2ValueAmount!: Label;
    @property(Label)
    withdrawCryptoStep2ValueAmount2!: Label;
    @property(Node)
    withdrawCryptoStep2OTPContainer!: Node;
    @property(Toggle)
    withdrawCryptoOTPOptionToggle!: Toggle;
    @property(Label)
    withdrawCryptoOTPOptionLabel!: Label;
    @property(Label)
    withdrawCryptoStep3TransactionIDLabel!: Label;
    @property([Toggle])
    tabToggle: Toggle[] = [];

    // --- EWallet Withdraw ---
    @property(Node)
    containerStep1EWalletWithdraw!: Node;
    @property(Node)
    containerStep2EWalletWithdraw!: Node;
    @property(Node)
    containerStep3EWalletWithdraw!: Node;
    @property(Node)
    eWalletCotainer!: Node;
    @property([Toggle])
    tabToggleEWallet: Toggle[] = [];
    @property([Node])
    spriteActiveEWallet: Node[] = [];
    @property([Node])
    spriteNotActiveEWallet: Node[] = [];
    @property([Node])
    spriteLgEWallet: Node[] = [];
    @property([Node])
    spriteLabelEWallet: Node[] = [];
    @property(Label)
    withdrawEWalletAddressPlaceholder!: Label;
    @property(Label)
    withdrawEWalletValuePlaceholder!: Label;
    @property(Label)
    withdrawEWalletBalanceToday!: Label;
    @property(EditBox)
    withdrawEWalletStep1EdbAddress!: EditBox;
    @property(EditBox)
    withdrawEWalletStep1EdbAmount!: EditBox;
    @property(Label)
    withdrawEWalletStep2Address!: Label;
    @property(Label)
    withdrawEWalletStep2Amount!: Label;
    @property(Label)
    withdrawEWalletStep2Amount2!: Label;
    @property(EditBox)
    withdrawEWalletEdbStep2OTP!: EditBox;
    @property(Toggle)
    withdrawEWalletOTPOptionToggle!: Toggle;
    @property(Label)
    withdrawEWalletOTPOptionLabel!: Label;
    @property(Label)
    withdrawEWalletStep3TransactionIDLabel!: Label;

    // --- EWalletVN ---
    @property(Node)
    containerStep1EWalletVNWithdraw!: Node;
    @property(Node)
    containerStep2EWalletVNWithdraw!: Node;
    @property(Node)
    containerStep3EWalletVNWithdraw!: Node;
    @property(Node)
    eWalletCotainerVN!: Node;
    @property([Toggle])
    tabToggleEWalletVN: Toggle[] = [];
    @property([Node])
    spriteActiveEWalletVN: Node[] = [];
    @property([Node])
    spriteNotActiveEWalletVN: Node[] = [];
    @property([Node])
    spriteLgEWalletVN: Node[] = [];
    @property([Node])
    spriteLabelEWalletVN: Node[] = [];
    @property(Label)
    withdrawEWalletVNValuePlaceholder!: Label;
    @property(Label)
    withdrawEWalletVNBalanceToday!: Label;
    @property(EditBox)
    withdrawEWalletVNStep1EdbAddress!: EditBox;
    @property(EditBox)
    withdrawEWalletVNStep1EdbAmount!: EditBox;
    @property(Label)
    withdrawEWalletVNStep2Address!: Label;
    @property(Label)
    withdrawEWalletVNStep2Amount!: Label;
    @property(Label)
    withdrawEWalletVNStep2Amount2!: Label;
    @property(EditBox)
    withdrawEWalletVNEdbStep2OTP!: EditBox;
    @property(Toggle)
    withdrawEWalletVNOTPOptionToggle!: Toggle;
    @property(Label)
    withdrawEWalletVNOTPOptionLabel!: Label;
    @property(Label)
    withdrawEWalletVNStep3TransactionIDLabel!: Label;
    @property(Node)
    containerStep2EWalletVNWithdrawOTP!: Node;
    @property(Sprite)
    withDrawEWalletVNSpriteStep2!: Sprite;

    private withdrawBankVNSelectedBankCode: string = "";
    private productIDAndAmountMap: any[] = [];
    private withdrawBankVNMinToday: number = 0;
    private withdrawBankVNMaxToday: number = 0;
    private withdrawBankVNBalanceTodayValue: number = 0;
    private withdrawCryptoSelected: string = "";
    private withdrawCryptoSelectedWalletType: number = -1;
    private withdrawCryptoMinToday: number = 0;
    private withdrawCryptoMaxToday: number = 0;
    private withdrawCryptoRate: number = 0;
    private withdrawCryptoBalanceTodayValue: number = 0;
    private withdrawEWalletTypeSelected: number = -1;
    private withdrawEWalletMinToday: number = 0;
    private withdrawEWalletMaxToday: number = 0;
    private withdrawEWalletBalanceTodayValue: number = 0;
    private withdrawEWalletVNMinToday: number = 0;
    private withdrawEWalletVNMaxToday: number = 0;
    private withdrawEWalletVNBalanceTodayValue: number = 0;

    // Deposit
    @property(Node)
    containerMenuDeposit: Node = null!;
    @property(Node)
    containerDepositWithCard: Node = null!;
    @property(Node)
    containerDepositWithBank: Node = null!;
    @property(Node)
    containerDepositWithWalletWorld: Node = null!;
    @property(Node)
    containerDepositWithWalletVN: Node = null!;
    @property(Node)
    containerDepositWithCryptoUSDT: Node = null!;
    @property(Node)
    containerDepositWithCryptoUSDC: Node = null!;
    @property(Node)
    containerDepositWithCryptoBUST: Node = null!;

    // Withdraw
    @property(Node)
    containerWithDrawWithCard: Node = null!;
    @property(Node)
    containerWithDrawWithBank: Node = null!;
    @property(Node)
    containerWithDrawWithWalletWorld: Node = null!;
    @property(Node)
    containerWithDrawWithWalletVN: Node = null!;
    @property(Node)
    containerWithDrawWithCrypto: Node = null!;

    // Deposit Label
    @property(Label)
    labelNumberBank: Label = null!;
    @property(Label)
    labelContentBank: Label = null!;
    @property(Label)
    labelNumberWalletWorld: Label = null!;
    @property(Label)
    labelNumberWalletVN: Label = null!;
    @property(Label)
    labelCodeCrypto: Label = null!;
    @property(Label)
    labelCodeCryptoBUST: Label = null!;
    @property(Label)
    labelCodeCryptoUSDC: Label = null!;

    // Deposit wallet world
    @property([Toggle])
    tabToggleEWalletDepositWorld: Toggle[] = [];
    @property([Node])
    spriteActiveEWalletDepositWorld: Node[] = [];
    @property([Node])
    spriteNotActiveEWalletDepositWorld: Node[] = [];
    @property([Node])
    spriteLgEWalletDepositWorld: Node[] = [];
    @property([Node])
    spriteLabelEWalletDepositWorld: Node[] = [];

    // Deposit wallet VN
    @property([Toggle])
    tabToggleEWalletDepositVN: Toggle[] = [];
    @property([Node])
    spriteActiveEWalletDepositVN: Node[] = [];
    @property([Node])
    spriteNotActiveEWalletDepositVN: Node[] = [];
    @property([Node])
    spriteLabelEWalletDepositVN: Node[] = [];
    @property([Node])
    spriteLgEWalletDepositVN: Node[] = [];

    // Deposit Card
    @property(Node)
    depositCardTableList: Node = null!;
    @property(Node)
    depositCardTableItem: Node = null!;
    @property(Node)
    depositCardDropList: Node = null!;
    @property(Node)
    depositCardDropListItem: Node = null!;
    @property(Node)
    depositCardDropListCost: Node = null!;
    @property(Node)
    depositCardDropListCostItem: Node = null!;
    @property(Label)
    depositCardTelcoNameLabel: Label = null!;
    @property(Label)
    depositCardTypeLabel: Label = null!;
    @property(Label)
    depositCardCostLabel: Label = null!;
    @property(EditBox)
    depositCardEdbSeri: EditBox = null!;
    @property(EditBox)
    depositCardEdbCode: EditBox = null!;
    @property(Node)
    depositCardNotifiConfirm: Node = null!;
    @property(RichText)
    depositCardNotifiConfirmLabel: RichText = null!;

    // Deposit Bank VN
    @property(Node)
    depositBankVNStep1: Node = null!;
    @property(Node)
    depositBankVNStep2: Node = null!;
    @property(Label)
    depositBankVNWalletNameLabel: Label = null!;
    @property(Label)
    depositBankVNWalletNumberLabel: Label = null!;
    @property(Label)
    depositBankVNWalletAccountLabel: Label = null!;
    @property(Label)
    depositBankVNAmountLabel: Label = null!;
    @property(Label)
    depositBankVNCode: Label = null!;
    @property(Label)
    depositBankVNTime: Label = null!;
    @property(EditBox)
    edbDepositBankVNAmount: EditBox = null!;
    @property(Sprite)
    depositBankVNQR: Sprite = null!;
    depositBankVNCountdownFunc: Function;

    private depositBankVNAmount: number = 0;

    // Deposit EWallet
    private walletType: Record<number, string> = {
        1: "VN", // momo
        3: "VN", // zalo
        4: "VN", // ViettelPay
        5: "USDT",
        6: "VN", // VNPTPay
        7: "VN", // MobifoneMoney
        8: "BUST",
        9: "USDC",
        10: "World", // Paypal
        12: "World", // PerfectMoney
        13: "World", //WebMoney
        14: "World" // Payeer
    };
    private depositEWalletWalletType: number;

    // EWalletVN
    @property(Node) depositEWalletVNStep1: Node = null!;
    @property(Node) depositEWalletVNStep2: Node = null!;
    @property(Label) depositEWalletVNWalletNumberLabel: Label = null!;
    @property(Label) depositEWalletVNWalletAccountLabel: Label = null!;
    @property(Label) depositEWalletVNAmount: Label = null!;
    @property(Label) depositEWalletVNCode: Label = null!;
    @property(Label) depositEWalletVNTime: Label = null!;
    @property(Sprite) depositEWalletVNQR: Sprite = null!;
    depositEWalletVNCountdownFunc: Function;
    @property(EditBox) edbDepositEWalletVNAmount: EditBox = null!;

    // EWallet World
    @property(Node) depositEWalletWorldStep1: Node = null!;
    @property(Node) depositEWalletWorldStep2: Node = null!;
    @property(Label) depositEWalletWorldWalletNumberLabel: Label = null!;
    @property(Label) depositEWalletWorldWalletAccountLabel: Label = null!;
    @property(Label) depositEWalletWorldAmount: Label = null!;
    @property(Label) depositEWalletWorldCode: Label = null!;
    @property(Label) depositEWalletWorldTime: Label = null!;
    @property(EditBox) edbDepositEWalletWorldAmount: EditBox = null!;

    // Coin
    @property(Label) depositCoinUSDTWalletAddressLabel: Label = null!;
    @property(EditBox) depositCoinUSDTEdbAmount: EditBox = null!;
    @property(Label) depositCoinBUSTWalletAddressLabel: Label = null!;
    @property(EditBox) depositCoinBUSTEdbAmount: EditBox = null!;
    @property(Label) depositCoinUSDCWalletAddressLabel: Label = null!;
    @property(EditBox) depositCoinUSDCEdbAmount: EditBox = null!;
    private depositCoinWalletType: number;

    // Transfer
    @property(Node) step1ContainerTransfer: Node = null!;
    @property(Node) step2ContainerTransfer: Node = null!;
    @property(Node) step3ContainerTransfer: Node = null!;
    @property(Node) stepSuccessContainerTransfer: Node = null!;
    @property(EditBox) editBoxUserNameTransfer: EditBox = null!;
    @property(EditBox) editBoxUserNameAgainTransfer: EditBox = null!;
    @property(EditBox) editBoxReasonTransfer: EditBox = null!;
    @property(EditBox) editBoxCoinTransfer: EditBox = null!;
    @property(Label) textCoinTransferReceive: Label = null!;
    @property(Label) textCoinTransferExist: Label = null!;
    @property(Label) textCoinTransferReceiveStep3: Label = null!;
    @property(Label) textUserNameStep3: Label = null!;
    @property(Label) textCoinTransferExistStep3: Label = null!;
    @property(Node) step3OTPContainer: Node = null!;
    @property(Toggle) step3OTPToggle: Toggle = null!;
    @property(Label) step3OTPTypeLabel: Label = null!;
    @property(EditBox) step3OTPEdb: EditBox = null!;

    // Withdraw wallet world
    @property([Toggle]) tabToggleEWalletWithdrawWorld: Toggle[] = [];
    @property([Node]) spriteActiveEWalletWithdrawWorld: Node[] = [];
    @property([Node]) spriteNotActiveEWalletWithdrawWorld: Node[] = [];
    @property([Node]) spriteLgEWalletWithdrawWorld: Node[] = [];
    @property([Node]) spriteLabelEWalletWithdrawWorld: Node[] = [];
    @property(Node) withDrawEwalletWorldOTPContainerStep2: Node = null!;
    @property(Label) lblTextAddressWithdrawEWalletWorld: Label = null!;

    // Cash QR
    @property(Node) containerCashQR: Node = null!;
    @property(Node) step1ContainerCashQR: Node = null!;
    @property(Node) step2ContainerCashQR: Node = null!;
    @property([Toggle]) tabCashQR: Toggle[] = [];
    @property([Node]) spriteActiveTabCashQR: Node[] = [];
    @property([Node]) spriteNotActiveTabCashQR: Node[] = [];
    @property(EditBox) edbDepositCashQR: EditBox = null!;
    @property(Node) containerCashQREWalletVN: Node = null!;
    @property(Node) containerCashQREWalletWorld: Node = null!;
    @property(Node) placeHolderEdbAmount: Node = null!;
    @property(Label) lblTextUserNameCashQR: Label = null!;
    @property(Label) lblTimeWhenNotQR: Label = null!;
    @property(Label) lblTimeNoteWhenNotQR: Label = null!;

    @property(Node) step1ContainerCashQRUSD: Node = null!;
    @property(EditBox) edbDepositCashQRUSD: EditBox = null!;
    @property(Node) placeHolderEdbAmountUSD: Node = null!;
    @property(Label) lblTextUserNameCashQRUSD: Label = null!;
    @property(Label) lblTextNotifiStep1: Label = null!;
    @property(Label) lblTextNotifiStep1USD: Label = null!;
    @property(Node) containerQRCodeSprite: Node = null!;

    // Cash QR Bank
    @property(Node) boxDepositCashQRBankName: Node = null!;
    @property(Label) lblDepositCashQRBankName: Label = null!;
    @property(Label) lblDepositCashQRNumberSTK: Label = null!;
    @property(Label) lblDepositCashQRNameOwner: Label = null!;
    @property(Label) lblDepositCashQRAmount: Label = null!;
    @property(Label) lblDepositCashQRContent: Label = null!;
    @property(Sprite) depositSpriteCashQR: Sprite = null!;
    @property(Label) lblDepositCashQRTime: Label = null!;
    depositCashQRCountdownFunc: Function;
    private depositCashQRBankWalletType: number;

    // Cash QR EWallet VN
    @property([Toggle])
    tabToggleEWalletVNCashQR: Toggle[] = [];

    @property([Node])
    spriteActiveEWalletVNCashQR: Node[] = [];

    @property([Node])
    spriteNotActiveEWalletVNCashQR: Node[] = [];

    @property([Node])
    spriteLabelEWalletVNCashQR: Node[] = [];

    private depositEWalletWalletTypeCashQRVN: number;

    // Cash QR EWallet World
    @property([Toggle])
    tabToggleEWalletWorldCashQR: Toggle[] = [];

    @property([Node])
    spriteActiveEWalletWorldCashQR: Node[] = [];

    @property([Node])
    spriteNotActiveEWalletWorldCashQR: Node[] = [];

    @property([Node])
    spriteLabelEWalletWorldCashQR: Node[] = [];

    private depositEWalletWalletTypeCashQRWorld: number;

    // Bank VN New
    @property(Label)
    lblNameBankDeposit: Label = null;

    @property(Label)
    lblNumberBankDeposit: Label = null;

    @property(Label)
    lblNameAccountBank: Label = null;

    @property(Label)
    lblCodeContent: Label = null;

    @property(Label)
    lblTimeExpired: Label = null;

    @property(Node)
    btnCreateCodeContent: Node = null;

    @property(Node)
    nodeContentCodeBank: Node = null;

    private walletCodeDeposit: string;

    // EWalletVN New
    @property(Label)
    lblWalletAccountEWalletVNDeposit: Label = null;

    @property(Label)
    lblWalletAccountNameEWalletVNDeposit: Label = null;

    @property(Label)
    lblTimeExpiredEWalletVN: Label = null;

    @property(Node)
    btnCreateCodeContentEWalletVN: Node = null;

    @property(Label)
    lblContentEWalletVNDeposit: Label = null;

    @property(Label)
    keyWalletAccountEWalletVNDeposit: Label = null;

    @property(Node)
    nodeContentCodeEWalletVN: Node = null;

    @property(Label)
    lblContentNoteEWalletVN: Label = null;


    private lblWalletNameEWalletVNDeposit: string;
    private lblWalletCodeEWalletVNDeposit: string;


    // EWallet World New
    @property(Label)
    lblWalletAccountEWalletWorldDeposit: Label = null;
    @property(Label)
    lblWalletAccountNameEWalletWorldDeposit: Label = null;
    @property(Label)
    lblTimeExpiredEWalletWorld: Label = null;
    @property(Node)
    btnCreateCodeContentEWalletWorld: Node = null;
    @property(Label)
    lblContentEWalletWorldDeposit: Label = null;
    @property(Label)
    keyWalletAccountEWalletWorldDeposit: Label = null;
    @property(Node)
    nodeContentCodeEWalletWorld: Node = null;
    @property(Label)
    lblContentNoteEWalletWorld: Label = null;



    private lblWalletNameEWalletWorldDeposit: string;
    private lblWalletCodeEWalletWorldDeposit: string;



    // Bank New
    @property([Toggle])
    tabBank: Toggle[] = [];
    @property([Node])
    spriteActiveTabBank: Node[] = [];
    @property([Node])
    spriteNotActiveTabBank: Node[] = [];
    @property(Node)
    containerBankGianTiep: Node = null;
    @property(Node)
    containerBankTrucTiep: Node = null;

// Bank Gián Tiếp
    @property(Node)
    depositBankDropList: Node = null;
    @property(Node)
    depositBankDropListItem: Node = null;
    @property(Label)
    depositBankGianTiepLabel: Label = null;

    private selectedBankName: string = "";
    private bankCode: string = "";
    private rate: number = 1;
    private minAmountBank: number = 0;
    private maxAmountBank: number = 0;

    private isDepositDropdownInitialized: boolean = false;

    @property(EditBox)
    edbAmountBankGianTiep: EditBox = null;
    @property(Label)
    lblCoinReceive: Label = null;

    //Bank ThaiLand
    @property(Node)
    containerBankThaiLand: Node = null;
    @property(Node)
    depositBankDropListThaiLand: Node = null;
    @property(Node)
    depositBankDropListItemThaiLand: Node = null;
    @property(Label)
    depositBankGianTiepLabelThaiLand: Label = null;
    private selectedBankNameThaiLand: string = "";
    private bankCodeThaiLand: string = "";
    private rateThaiLand: number = 1;
    private minAmountBankThaiLand: number = 0;
    private maxAmountBankThaiLand: number = 0;

    private isDepositDropdownInitializedThaiLand: boolean = false;

    @property(EditBox)
    edbAmountBankGianTiepThaiLand: EditBox = null;
    @property(Label)
    lblCoinReceiveThaiLand: Label = null;
    private _internalToggleChange = false;






    onLoad() {
        this.editBoxCoinTransfer.node.on('editing-did-ended', this.formatNumber, this);
        this.edbAmountBankGianTiep.node.on('editing-did-ended', this.formatNumberBank, this);
        this.edbAmountBankGianTiepThaiLand.node.on('editing-did-ended', this.formatNumberBankThaiLand, this);

        this.edbDepositCashQR.node.on('editing-did-began', () => {
            if (this.placeHolderEdbAmount) {
                this.placeHolderEdbAmount.active = false;
            }
        });

        this.edbDepositCashQR.node.on('editing-did-ended', () => {
            if (this.placeHolderEdbAmount) {
                this.placeHolderEdbAmount.active = this.edbDepositCashQR.string.trim() === '';
            }
        });

        this.edbDepositCashQR.node.on('editing-did-ended', this.formatNumberInput, this);

        this.edbDepositCashQRUSD.node.on('editing-did-began', () => {
            if (this.placeHolderEdbAmountUSD) {
                this.placeHolderEdbAmountUSD.active = false;
            }

        });


        this.edbDepositCashQRUSD.node.on('editing-did-ended', () => {
            if (this.placeHolderEdbAmountUSD) {
                this.placeHolderEdbAmountUSD.active = this.edbDepositCashQRUSD.string.trim() === '';
            }
        });

        this.edbDepositCashQRUSD.node.on('editing-did-ended', this.formatNumberInputUSD, this);
    }

    start () {
        this.textCoinTransferExist.string = Configs.Login.GoldBalance.toLocaleString("vi-VN");
    }

    formatNumberInput() {
        let inputText = this.edbDepositCashQR.string.trim();
        let cleaned = inputText.replace(/[^0-9]/g, '');
        let number = parseInt(cleaned, 10);

        if (!isNaN(number)) {
            this.edbDepositCashQR.string = number.toLocaleString('vi-VN');
        } else {
            this.edbDepositCashQR.string = '';
        }
    }

    formatNumberInputUSD() {
        let inputText = this.edbDepositCashQRUSD.string.trim();
        let cleaned = inputText.replace(/[^0-9]/g, '');
        let number = parseInt(cleaned, 10);

        if (!isNaN(number)) {
            this.edbDepositCashQRUSD.string = number.toLocaleString('vi-VN');
        } else {
            this.edbDepositCashQRUSD.string = '';
        }
    }

    formatNumber() {
        let inputText = this.editBoxCoinTransfer.string.trim();
        let cleanedText = inputText.replace(/[^0-9]/g, '');
        let numberValue = parseInt(cleanedText, 10);

        if (!isNaN(numberValue)) {
            this.editBoxCoinTransfer.string = numberValue.toString();
            let receivedValue = Math.floor(numberValue * 0.9604);
            this.textCoinTransferReceive.string = receivedValue.toLocaleString("vi-VN");
        } else {
            this.editBoxCoinTransfer.string = "0";
            this.textCoinTransferReceive.string = "0";
        }
    }

    formatNumberBank() {
        let inputText = this.edbAmountBankGianTiep.string.trim();
        let cleanedText = inputText.replace(/[^0-9]/g, '');
        let numberValue = parseInt(cleanedText, 10);

        if (!isNaN(numberValue)) {
            this.edbAmountBankGianTiep.string = numberValue.toString();
            let receivedValue = Math.floor(numberValue * this.rate);
            this.lblCoinReceive.string = receivedValue.toLocaleString("vi-VN");
        } else {
            this.edbAmountBankGianTiep.string = "";
            this.lblCoinReceive.string = "0";
        }
    }

    formatNumberBankThaiLand() {
        let inputText = this.edbAmountBankGianTiepThaiLand.string.trim();
        let cleanedText = inputText.replace(/[^0-9]/g, '');
        let numberValue = parseInt(cleanedText, 10);

        if (!isNaN(numberValue)) {
            this.edbAmountBankGianTiepThaiLand.string = numberValue.toString();
            let receivedValue = Math.floor(numberValue * this.rateThaiLand);
            this.lblCoinReceiveThaiLand.string = receivedValue.toLocaleString("vi-VN");
        } else {
            this.edbAmountBankGianTiepThaiLand.string = "";
            this.lblCoinReceiveThaiLand.string = "0";
        }
    }

    actContinueTransfer() {
        if (this.editBoxUserNameTransfer.string.trim() === "" || this.editBoxUserNameAgainTransfer.string.trim() === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_please_enter_full_recipient_name"));
            return;
        }

        if (this.editBoxUserNameTransfer.string.trim() !== this.editBoxUserNameAgainTransfer.string.trim()) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_recipient_name_mismatch"));
            return;
        }

        if (this.editBoxCoinTransfer.string.trim() === "0" || this.editBoxCoinTransfer.string.trim() === "" ) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_please_enter_transfer_amount"));

            return;
        }

        const amountValue = parseInt(this.editBoxCoinTransfer.string);

        const data = {
            CurrencyID: 1,
            Username: this.editBoxUserNameTransfer.string.trim(),
            Amount: amountValue,
            Reason: this.editBoxReasonTransfer.string.trim(),
            Timestamp: Utils.getTicks()
        }

        const json = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        };

        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['TransferAccount'], json, (status, res) => {
            if (status === 200 && res.c === 0) {
                this.step3OTPContainer.active = !!res.p[0];

                this.textCoinTransferReceiveStep3.string = this.textCoinTransferReceive.string;
                this.textUserNameStep3.string = this.editBoxUserNameTransfer.string;
                this.textCoinTransferExistStep3.string = this.textCoinTransferExist.string;
                this.step1ContainerTransfer.active = false;
                this.step2ContainerTransfer.active = false;
                this.step3ContainerTransfer.active = true;
            }
            App.instance.showLoading(false);
        });
    }

    populateOTPStep3Transfer(event: any, data: string) {
        this.step3OTPToggle.isChecked = false;
        if (data == "sms") {
            this.step3OTPTypeLabel.string = 'SMS OTP';
            return;
        }

        if (data == "telesafe") {
            this.step3OTPTypeLabel.string = 'Telesafe OTP';
            return;
        }
    }

    backToStep2ContinerTransfer() {
        this.step1ContainerTransfer.active = false;
        this.step2ContainerTransfer.active = true;
        this.step3ContainerTransfer.active = false;
    }

    actConfirmTransfer() {
        const data = {
            OTPType: 1,
            OTP: "000000",
            Timestamp: Utils.getTicks()
        };

        if (this.step3OTPContainer.active) {
            data.OTP = this.step3OTPEdb.string;
            data.OTPType = this.step3OTPTypeLabel.string === "SMS OTP" ? 1 : 3;
        }

        const json = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        };

        App.instance.showLoading(true);
        Http.post(
            Configs.App.DOMAIN_CONFIG['TranferAccountConfirm'],
            json,
            (status, res) => {
                App.instance.showLoading(false);
                if (parseInt(res["c"]) === 0) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_transfer_success"));
                    BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                    this.step3ContainerTransfer.active = false;
                    this.stepSuccessContainerTransfer.active = true;
                } else {
                    App.instance.alertDialog.showMsg(res["r"]);
                }
            }
        );
    }

    showWithdrawCard() {
        this.showTab(2);
        this.hideContainerMenuWithdraw();
        this.containerMenuCardWithdraw.getComponentsInChildren(Toggle).forEach((toggle, i) => {
            toggle.isChecked = i === 0;
        });
    }

    onTabSelected(toggle: Toggle, index: string) {
        // toggle.isChecked = true;
        if (this._internalToggleChange) return;

        let tabIndex = parseInt(index);

        if (toggle.isChecked === false) return;

        this.showTab(tabIndex);


        this.eWalletCotainer.active = false;
        this.eWalletCotainerVN.active = false;
        this.containerDepositWithCard.active = false;
        this.containerDepositWithBank.active = false;
        this.containerDepositWithWalletWorld.active = false;
        this.containerDepositWithWalletVN.active = false;
        this.containerDepositWithCryptoUSDC.active = false;
        this.containerDepositWithCryptoUSDT.active = false;
        this.containerDepositWithCryptoBUST.active = false;
        this.step2ContainerTransfer.active = false;
        this.step3ContainerTransfer.active = false;
        this.stepSuccessContainerTransfer.active = false;
        this.containerCashQR.active = false;
        this.containerBankThaiLand.active = false;
    }

    showTab(index: number) {
        this._internalToggleChange = true;
        this.tabToggleMain.forEach((tab, i) => {
            tab.isChecked = i === index;
        });

        this.tabToggle.forEach((tab, i) => {
            tab.isChecked = i === index;

            const textNode = tab.node.getChildByName("text");
            if (textNode) {
                const label = textNode.getComponent(Label);
                if (label) {
                    label.color = (i === index)
                        ? Color.fromHEX(new Color(), "#FFE242")
                        : Color.WHITE;
                }
            }
        });

        this.tabContents.forEach((tab, i) => {
            tab.active = (i === index);
        });

        this._internalToggleChange = false;


        this.containerMenuDeposit.active = true;

        if (index === 2) {
            this.containerMenuCardWithdraw.active = true;
            this.containerMenuCardWithdraw.getComponentsInChildren(Toggle).forEach((toggle, i) => {
                toggle.isChecked = false;
            });
        }

        if (index === 1) {
            this.step1ContainerTransfer.active = true;
        }
    }

    submitWithdrawCardStep1() {
        let amount = parseInt(this.withdrawCardCostLabel.string.replace(/\./g, ""));
        let quality = parseInt(this.withdrawEdbQualityContent.string);

        if (isNaN(amount) || isNaN(quality) || amount === 0 || quality === 0) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
            return;
        }

        App.instance.showLoading(true);
        let data = {
            "CurrencyID": Configs.Login.CurrencyID,
            "ProductID": this.productIDAndAmountMap[amount].productID,
            "Quantity": parseInt(this.withdrawEdbQualityContent.string),
            "Timestamp": Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['BuyItemUrl'], payload, (status, response) => {
            App.instance.showLoading(false);
            if (status === 200 && response.c === 0) {

                if (response.p[0]) {
                    this.withdrawCardContainerOTPStep2.active = true;
                } else {
                    this.withdrawCardContainerOTPStep2.active = false;
                }

                this.containerStep2CardWithdraw.getChildByName('value1').getComponent(Label).string = this.withdrawCardDetailLabel.string;
                this.containerStep2CardWithdraw.getChildByName('value2').getComponent(Label).string = this.withdrawCardCostLabel.string;
                this.containerStep2CardWithdraw.getChildByName('value3').getComponent(Label).string = this.withdrawEdbQualityContent.string;
                this.containerStep2CardWithdraw.getChildByName('value4').getComponent(Label).string = this.withdrawCardTotalLabel.string;

                this.containerStep1CardWithdraw.active = false;
                this.containerStep2CardWithdraw.active = true;

                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            }
        });
    }

    backWithdrawCardStep1() {
        this.containerStep1CardWithdraw.active = true;
        this.containerStep2CardWithdraw.active = false;
        this.containerStep3CardWithdraw.active = false;
    }

    backWithdrawBankVNStep2() {
        this.containerStep1BankVNWithdraw.active = true;
        this.containerStep2BankVNWithdraw.active = false;
        this.containerStep3BankVNWithdraw.active = false;
    }

    backWithdrawCryptoStep2() {
        this.containerStep1CryptoWithdraw.active = true;
        this.containerStep2CryptoWithdraw.active = false;
        this.containerStep3CryptoWithdraw.active = false;
    }

    withdrawCardGetOTPStep2(event: any, value: any) {
        var data = value.split(":");
        var ServiceID = data[0];

        let label = this.withdrawCardOTPOptionLabel.string;
        if (data[1] == 'BankVN') {
            label = this.withdrawBankVNOTPOptionLabel.string;
        } else if (data[1] == 'Crypto') {
            label = this.withdrawCryptoOTPOptionLabel.string;
        } else if (data[1] == 'EWallet') {
            label = this.withdrawEWalletOTPOptionLabel.string;
        } else if (data[1] == 'EWalletVN') {
            label = this.withdrawEWalletVNOTPOptionLabel.string;
        } else if (data[1] == 'Transfer') {
            label = this.step3OTPTypeLabel.string;
        }

        if (label === "SMS OTP") {
            App.instance.showLoading(true);

            var payload = {
                ServiceID: parseInt(ServiceID),
                CurrencyID: Configs.Login.CurrencyID,
            };

            Http.post(Configs.App.DOMAIN_CONFIG['GetSmsOtp'], payload, (status, res) => {
                App.instance.showLoading(false);
                if (status === 200) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('sms_sent_check'));
                }
            });

            return;
        }

        if (label === "Telesafe OTP") {
            App.instance.showLoading(true);

            Http.post(Configs.App.DOMAIN_CONFIG['GetTeleSafeOtp'], { IsVerify: false }, (status, res) => {
                App.instance.showLoading(false);
                if (status === 200) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('sms_sent_check'));
                }
            });

            return;
        }
    }

    submitWithdrawCardStep2() {
        // var otpType = this.withdrawCardOTPOptionLabel.string === "SMS OTP" ? 1 : 3;

        let data = {
            OTPType: 1,
            OTP: "000000",
            Timestamp: Utils.getTicks()
        }

        if (this.withdrawCardContainerOTPStep2.active) {
            data.OTPType = this.withdrawCardOTPOptionLabel.string === "SMS OTP" ? 1 : 3;
            data.OTP = this.withdrawCardEdbOTP.string
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['ConfirmBuyItem'], payload, (status, response) => {
            if (status === 200) {
                this.showWithdrawCardStep3(response.d);
                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            }
        });
    }

    showWithdrawCardStep3(data: any[]) {
        this.withdrawCardListStep3.removeAllChildren();
        data.forEach(item => {
            let tableItem = instantiate(this.withdrawCardListStep3Item);
            tableItem.active = true;
            tableItem.getChildByName("1").getComponent(Label).string = '#' + item.productItemID;
            tableItem.getChildByName("2").getComponent(Label).string = item.value.toLocaleString("vi-VN");
            tableItem.getChildByName("3").getComponent(Label).string = item.serial;
            tableItem.getChildByName("4").getComponent(Label).string = item.code;

            this.withdrawCardListStep3.addChild(tableItem);
        });

        this.containerStep1CardWithdraw.active = false;
        this.containerStep2CardWithdraw.active = false;
        this.containerStep3CardWithdraw.active = true;
    }

    backWithdrawCardStep3() {
        this.containerStep1CardWithdraw.active = true;
        this.containerStep2CardWithdraw.active = false;
        this.containerStep3CardWithdraw.active = false;
        this.withdrawCardListStep3.removeAllChildren();
    }

    hideContainerMenuWithdraw() {
        this.containerMenuCardWithdraw.active = false;
    }

    showWithdrawCrypto(event: any, data: string) {
        this.containerStep1CryptoWithdraw.active = true;
        this.containerStep2CryptoWithdraw.active = false;
        this.containerStep3CryptoWithdraw.active = false;
        this.containerMenuCardWithdraw.active = false;
        this.withdrawCryptoSelected = data;

        this.withdrawCryptoStep1EdbWalletNumber.string = "";
        this.withdrawCryptoStep1EdbAmount.string = "";

        var txt_wallet_address = App.instance.getTextLang('txt_wallet_address');

        this.withdrawCryptoStep1LabelCrypto.string = txt_wallet_address + ' ' + data + " " + "(TRC20)";
        this.withdrawCryptoStep1PlaceholderWalletNumber.string = App.instance.getTextLang('txt_input_wallet_address') + ' ' + data + " " + "(TRC20)";
        this.withdrawCryptoStep2LabelCrypto.string = txt_wallet_address + ' ' + data + "(TRC20)";
        this.withdrawCryptoStep3LabelCrypto.string = App.instance.getTextLang('txt_trans_success') + ' ' + this.withdrawCryptoSelected + ' ' + App.instance.getTextLang('txt_success');

        if (this.withdrawCryptoSelected == "USDT") {
            this.withdrawCryptoSelectedWalletType = 5;
        } else if (this.withdrawCryptoSelected == "BUST") {
            this.withdrawCryptoSelectedWalletType = 8;
        } else if (this.withdrawCryptoSelected == "USDC") {
            this.withdrawCryptoSelectedWalletType = 9;
        } else {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            this.showTab(2);
            return;
        }

        Http.get(Configs.App.DOMAIN_CONFIG['ECoinGetInfoForCashOut'], { WalletType: this.withdrawCryptoSelectedWalletType, CurrencyID : Configs.Login.CurrencyID}, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.withdrawCryptoBalanceTodayValue = response.d.walletInfo.amountEnable;
                this.withdrawCryptoMinToday = response.d.walletInfo.minValue;
                this.withdrawCryptoMaxToday = response.d.walletInfo.maxValue;
                this.withdrawCryptoRate = response.d.config.rate;

                var form = App.instance.getTextLang('txt_from_value');
                var to = App.instance.getTextLang('txt_to_value');

                this.withdrawCryptoStep1PlaceholderAmount.string = `${form} ${this.withdrawCryptoMinToday.toLocaleString("vi-VN")} ${to} ${this.withdrawCryptoMaxToday.toLocaleString("vi-VN")}`;
                this.withdrawCryptoStep1LabelToday.string = this.withdrawCryptoBalanceTodayValue.toLocaleString("vi-VN");
            }
        });
    }

    actSubmitWithdrawCryptoStep1() {
        let address = this.withdrawCryptoStep1EdbWalletNumber.string;
        let amountStr = this.withdrawCryptoStep1EdbAmount.string;
        let amount = parseInt(amountStr);

        if (address === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('wallet_address_required'));
            return;
        }

        if (amountStr === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('amount_required'));
            return;
        }

        if (amount > this.withdrawCryptoMaxToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_less_than')} ${this.withdrawCryptoMaxToday.toLocaleString("vi-VN")}`);
            return;
        }

        if (amount < this.withdrawCryptoMinToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_greater_than')} ${this.withdrawCryptoMinToday.toLocaleString("vi-VN")}`);
            return;
        }

        if (isNaN(amount)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('value_invalid_amount'));
            return;
        }

        // if (address === "" || isNaN(amount) || amount < this.withdrawCryptoMinToday || amount > this.withdrawCryptoMaxToday) {
        //     App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
        //     return;
        // }

        let data = {
            CurrencyID: Configs.Login.CurrencyID,
            Address: address,
            CashoutValue: amount,
            WalletType: this.withdrawCryptoSelectedWalletType,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CashOutECoinCreateTrans'], payload, (status, response) => {
            if (status === 200 && response.c === 0) {
                if (response.p[0]) {
                    this.withdrawCryptoStep2OTPContainer.active = true;
                } else {
                    this.withdrawCryptoStep2OTPContainer.active = false;
                }
                this.withdrawCryptoStep2ValueAddress.string = address;
                this.withdrawCryptoStep2ValueAmount.string = amount.toLocaleString("VN");
                const value = parseFloat((amount / this.withdrawCryptoRate).toFixed(3));
                this.withdrawCryptoStep2ValueAmount2.string = value.toLocaleString("VN");
                // this.withdrawCryptoStep2OTPContainer.active = true;
                this.containerStep1CryptoWithdraw.active = false;
                this.containerStep2CryptoWithdraw.active = true;
            }
        });
    }

    actSubmitWithdrawCryptoStep2() {
        let data = {
            OTPType: 1,
            OTP: "000000",
            Timestamp: Utils.getTicks()
        }

        if (this.withdrawCryptoStep2OTPContainer.active) {
            data.OTPType = this.withdrawCardOTPOptionLabel.string === "SMS OTP" ? 1 : 3;
            data.OTP = this.withdrawCardEdbOTP.string;
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CashOutECoinCreateTransConfirm'], payload, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.containerStep2CryptoWithdraw.active = false;
                this.containerStep3CryptoWithdraw.active = true;

                this.withdrawCryptoStep3TransactionIDLabel.string = "TransactionID: " + response.d.transactionID;

                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            }
        });
    }

    showContainerWithdrawCard() {
        if (!Configs.Login.IsLogin) {
            return;
        }
        this.hideContainerMenuWithdraw();
        this.containerWithDrawWithCard.active = true;

        this.containerStep1CardWithdraw.active = true;
        this.containerStep2CardWithdraw.active = false;
        this.containerStep3CardWithdraw.active = false;


        Http.get(Configs.App.DOMAIN_CONFIG['GetListTelco'], {telcoType: 1}, (status, response) => {
            if (status === 200 && response.c === 0) {
                // this.populateDropdownWithdraw(response.d);
                // cc.log(response.d);
                const viettelItem = response.d.find(item =>
                    item.telcoCode.toLowerCase().includes("vtel")
                );

                if (viettelItem) {
                    this.withdrawCardDetailLabel.string = viettelItem.telcoName;
                    this.onDropdownItemSelectedWithdraw(viettelItem.telcoCode);
                }

            }
        });

        // Http.get(Configs.App.DOMAIN_CONFIG['GetProductList'], {
        //     TelcoName: "vtel",
        //     telcoType: 1
        // }, (status, response) => {
        //     if (status === 200 && response.c === 0) {
        //
        //         cc.log(response.d)
        //
        //         const viettel100k = response.d.find(item =>
        //             item.productCode.includes("VTEL100")
        //         );
        //
        //         this.populateDropdownItemCostWithdraw(viettel100k);
        //     }
        // });

    }

    getCardDataWithdraw() {
        if (!Configs.Login.IsLogin) {
            return;
        }

        Http.get(Configs.App.DOMAIN_CONFIG['GetListTelco'], {telcoType: 1}, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.populateDropdownWithdraw(response.d);
            }
        });
    }

    populateDropdownWithdraw(data: any[]) {
        this.withdrawCardDropList.removeAllChildren();
        this.withdrawCardCostLabel.string = App.instance.getTextLang('txt_select_card_cost');
        this.withdrawCardDropListCost.active = false;

        data.forEach(item => {
            let dropdownItem = instantiate(this.withdrawCardDropListItem);
            dropdownItem.active = true;
            dropdownItem.getChildByName("Label").getComponent(Label).string = item.telcoName;

            dropdownItem.on(Node.EventType.TOUCH_END, () => {
                this.onDropdownItemSelectedWithdraw(item.telcoCode);
                this.withdrawCardDropList.active = !this.withdrawCardDropList.active;
                this.withdrawCardDetailLabel.string = item.telcoName;
            });

            this.withdrawCardDropList.addChild(dropdownItem);
        });
    }

    onDropdownItemSelectedWithdraw(telcoCode: string) {
        Http.get(Configs.App.DOMAIN_CONFIG['GetProductList'], {
            TelcoName: telcoCode,
            telcoType: 1
        }, (status, response) => {
            if (status === 200 && response.c === 0) {
                // cc.log(response.d);
                this.populateDropdownItemCostWithdraw(response.d);
            }
        });
    }

    populateDropdownItemCostWithdraw(data: any[]) {
        this.withdrawCardDropListCost.removeAllChildren();
        this.withdrawCardCostLabel.string = App.instance.getTextLang('txt_select_card_cost');
        this.withdrawEdbQualityContent.string = "";
        this.withdrawCardDropList.active = false;
        this.productIDAndAmountMap = [];
        this.withdrawCardTableList.removeAllChildren();

        let autoFocusAmount = 100000;

        data.forEach(item => {
            let dropdownItem = instantiate(this.withdrawCardDropListCostItem);
            dropdownItem.active = true;
            dropdownItem.getChildByName("Label").getComponent(Label).string = item.amount.toLocaleString("vi-VN");

            dropdownItem.on(Node.EventType.TOUCH_END, () => {
                this.withdrawCardDropListCost.active = !this.withdrawCardDropListCost.active;
                this.withdrawCardCostLabel.string = item.amount.toLocaleString("vi-VN");

                this.selectedProductID = item.productID;
                this.selectedPromotion = item.promotion;
            });

            this.productIDAndAmountMap[item.amount] = {
                productID: item.productID,
                promotion: item.promotion
            };
            this.withdrawCardDropListCost.addChild(dropdownItem);

            let tableItem = instantiate(this.withdrawCardTableItem);
            tableItem.active = true;
            tableItem.getChildByName("MenhGia").getComponent(Label).string = item.amount.toLocaleString("vi-VN");
            tableItem.getChildByName("Tong").getComponent(Label).string = item.amount.toLocaleString("vi-VN") + "";
            this.withdrawCardTableList.addChild(tableItem);

            if (item.amount === autoFocusAmount) {
                this.withdrawCardCostLabel.string = item.amount.toLocaleString("vi-VN");
                this.selectedProductID = item.productID;
                this.selectedPromotion = item.promotion;
            }
        });
    }

    updateCardCostByQualityWithdraw(edbQuality: EditBox) {
        try {
            const q = parseInt(edbQuality.string);
            const c = parseInt(this.withdrawCardCostLabel.string.replace(/\./g, ""));

            const targetValue = q * c;
            const labelTarget = this.withdrawCardTotalLabel;


            let currentValue = parseInt(labelTarget.string.replace(/\./g, "")) || 0;
            tween({ value: currentValue })
                .to(0.1, { value: targetValue }, {
                    onUpdate: (val) => {
                        labelTarget.string = Utils.formatNumber(Math.round(val.value));
                    }
                })
                .start();
        } catch (error) {
            console.warn("updateCardCostByQualityWithdraw error:", error);
            return;
        }
    }

    getDataWithdrawBankVN() {
        this.containerStep1BankVNWithdraw.active = true;
        this.containerStep2BankVNWithdraw.active = false;
        this.containerStep3BankVNWithdraw.active = false;

        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 2 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === 2) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    this.getDataWithdrawBankVNAfterCheckGetButtons();
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                    this.showTab(2);
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                this.showTab(2);
            }
        }, false);
    }

    getDataWithdrawBankVNAfterCheckGetButtons() {
        this.withdrawBankVNListItems.removeAllChildren();
        this.withdrawBankVNEdbValue.string = "";
        this.withdrawBankVNEdbNumberBank.string = "";
        this.withdrawBankVNEdbAccountBank.string = "";
        this.withdrawBankVNSelectedBankCode = "";
        this.withdrawBankVNSelectedBankLabel.string = App.instance.getTextLang('iap250');

        App.instance.showLoading(true);

        Http.get(Configs.App.DOMAIN_CONFIG['WalletGetInfoForCashOut'], { WalletType: 2, CurrencyID : Configs.Login.CurrencyID}, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.withdrawBankVNBalanceTodayValue = response.d.walletInfo.amountEnable;
                this.withdrawBankVNMinToday = response.d.walletInfo.minValue;
                this.withdrawBankVNMaxToday = response.d.walletInfo.maxValue;

                var form = App.instance.getTextLang('txt_from_value');
                var to = App.instance.getTextLang('txt_to_value');

                this.withdrawBankVNBalance.string = Configs.Login.GoldBalance.toLocaleString("vi-VN");
                this.withdrawBankVNBalanceToday.string = this.withdrawBankVNBalanceTodayValue.toLocaleString("vi-VN");
                this.withdrawBankVNValuePlaceholder.string = `${form} ${this.withdrawBankVNMinToday.toLocaleString("vi-VN")} ${to} ${this.withdrawBankVNMaxToday.toLocaleString("vi-VN")}`;
            }
        });

        Http.get(Configs.App.DOMAIN_CONFIG['GetAccountBankinfoForCashout'], { CurrencyID: Configs.Login.CurrencyID, MoneyType: 'All' }, (status, response) => {
            App.instance.showLoading(false);
            if (status === 200 && response.c === 0) {
                App.instance.showLoading(false);
                response.d.banks.forEach((bank: { bankName: string; bankCode: string; }) => {
                    let item = instantiate(this.withdrawBankVNListItemTemplate);
                    item.parent = this.withdrawBankVNListItems;

                    let bankName = bank.bankName;

                    item.getChildByName("label").getComponent(Label).string = bankName;

                    item.on("click", () => {
                        this.withdrawBankVNSelectedBankCode = bank.bankCode;
                        this.withdrawBankVNSelectedBankLabel.string = bankName;
                        this.withdrawBankVNSelectedBankToggle.isChecked = false;
                    });
                });
            }
        });
    }
    hasVietnameseDiacritics(str: string): boolean {
        const normalized = str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
        return str !== normalized;
    }

    actSubmitWithdrawBankVNStep1() {
        var value = parseInt(this.withdrawBankVNEdbValue.string);
        var numberBank = this.withdrawBankVNEdbNumberBank.string;
        var accountBank = this.withdrawBankVNEdbAccountBank.string;


        if (numberBank === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('account_number_required'));
            return;
        }

        if (accountBank === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('account_name_required'));
            return;
        }

        if (this.hasVietnameseDiacritics(accountBank)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('account_name_no_accent'));
            return;
        }

        if (isNaN(value)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('value_invalid_amount'));
            return;
        }

        if (value > this.withdrawBankVNMaxToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang("amount_required_in_range")} ${Utils.formatNumber(this.withdrawBankVNMinToday)} ${App.instance.getTextLang("iap35")} ${Utils.formatNumber(this.withdrawBankVNMaxToday)}`);
            // App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_less_than')} ${this.withdrawBankVNMaxToday.toLocaleString("vi-VN")}`);
            return;
        }

        if (value < this.withdrawBankVNMinToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang("amount_required_in_range")} ${Utils.formatNumber(this.withdrawBankVNMinToday)} ${App.instance.getTextLang("iap35")} ${Utils.formatNumber(this.withdrawBankVNMaxToday)}`);
            // App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_greater_than')} ${this.withdrawBankVNMinToday.toLocaleString("vi-VN")}`);
            return;
        }


        App.instance.showLoading(true);

        let data = {
            WalletCode: this.withdrawBankVNSelectedBankCode,
            WalletAccount: accountBank,
            CashoutValue: value,
            WalletType: 2,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['BankCashOut'], payload, (status, response) => {
            App.instance.showLoading(false);
            if (status === 200 && response.c === 0) {
                if (response.p[0]) {
                    this.withdrawBankVNStep2OTPContainer.active = true;
                } else {
                    this.withdrawBankVNStep2OTPContainer.active = false;
                }

                this.withdrawBankVNStep2BankName.string = this.withdrawBankVNSelectedBankLabel.string;
                this.withdrawBankVNStep2BankAccount.string = accountBank;
                this.withdrawBankVNStep2BankNumber.string = numberBank;
                this.withdrawBankVNStep2BankAmout.string = value.toLocaleString("vi-VN");
                this.withdrawBankVNStep2BankValue.string = value.toLocaleString("vi-VN");
                this.withdrawBankVNEdbStep2OTP.string = "";

                this.containerStep1BankVNWithdraw.active = false;
                this.containerStep2BankVNWithdraw.active = true;
            }
        });
    }

    actSubmitWithdrawBankVNStep2() {
        let data = {
            OTPType: 1,
            OTP: "000000",
            Timestamp: Utils.getTicks()
        }

        if (this.withdrawBankVNStep2OTPContainer.active) {
            data.OTPType = this.withdrawBankVNOTPOptionLabel.string === "SMS OTP" ? 1 : 3;
            data.OTP = this.withdrawBankVNEdbStep2OTP.string
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['BankCashOutConfirm'], payload, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.containerStep2BankVNWithdraw.active = false;
                this.containerStep3BankVNWithdraw.active = true;
                this.withdrawBankVNStep3TransactionIDLabel.string = 'TransactionID: ' + response.d.transactionID;
                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            }
        });
    }

    populateOTPOptionWithdraw(option: any) {
        this.withdrawCardOTPOptionToggle.isChecked = false;
        if (option.target.name == "OptionSMS") {
            this.withdrawCardOTPOptionLabel.string = 'SMS OTP';
            return;
        }


        if (option.target.name == "OptionTelesafe") {
            this.withdrawCardOTPOptionLabel.string = 'Telesafe OTP';
            return;
        }
    }

    populateOTPOptionWithdrawBankVN(option: any) {
        this.withdrawBankVNOTPOptionToggle.isChecked = false;
        if (option.target.name == "OptionSMS") {
            this.withdrawBankVNOTPOptionLabel.string = 'SMS OTP';
            return;
        }


        if (option.target.name == "OptionTelesafe") {
            this.withdrawBankVNOTPOptionLabel.string = 'Telesafe OTP';
            return;
        }
    }

    populateOTPOptionWithdrawCrypto(event: any, data: string) {
        this.withdrawCryptoOTPOptionToggle.isChecked = false;
        if (data == "sms") {
            this.withdrawCryptoOTPOptionLabel.string = 'SMS OTP';
            return;
        }


        if (data == "telesafe") {
            this.withdrawCryptoOTPOptionLabel.string = 'Telesafe OTP';
            return;
        }
    }

    dismiss() {
        if (!this.bg) this.bg = this.node.getChildByName("Bg");
        if (!this.container) this.container = this.node.getChildByName("Container");

// Stop all actions: Không còn dùng runAction nên không cần stopAllActions
// Đảm bảo có UIOpacity
        const bgOpacity = this.bg.getComponent(UIOpacity) || this.bg.addComponent(UIOpacity);
        bgOpacity.opacity = 128;

        tween(bgOpacity)
            .to(0.2, { opacity: 0 })
            .start();

        const containerOpacity = this.container.getComponent(UIOpacity) || this.container.addComponent(UIOpacity);
        containerOpacity.opacity = 255;
        this.container.setScale(new Vec3(1, 1, 1));

        tween(this.container)
            .to(0.3, {
                scale: new Vec3(0.8, 0.8, 0.8),
            }, { easing: easing.backIn })
            .call(() => {
                containerOpacity.opacity = 150;
                this._onDismissed();  // Gọi hàm callback sau tween
            })
            .start();
    }

    _onDismissed() {
        var edits = this.node.getComponentsInChildren(EditBox);

        for (var i = 0; i < edits.length; i++) {
            edits[i].tabIndex = -1;
        }
        this.node.active = false;
    }

    showEWalletContainer(event: any, data: any) {
        this.hideContainerMenuWithdraw();
        this.eWalletCotainer.active = true;
        this.scheduleOnce(() => {
            this.showEWallet(0);
        }, 0.05);
    }

    selectedToggleEWallet(toggle: Toggle, index: string) {
        toggle.isChecked = true;
        let tabIndex = parseInt(index);
        this.showEWallet(tabIndex);
    }

    showEWallet(index: number): void {
        // EWallet not EWalletVN
        if (index < 4) {
            this.tabToggleEWalletWithdrawWorld.forEach((tab, i) => {
                tab.isChecked = i === index;
            })

            this.spriteActiveEWalletWithdrawWorld.forEach((tab, i) => {
                tab.active = (i === index);
            });

            this.spriteNotActiveEWalletWithdrawWorld.forEach((sprite, i) => {
                sprite.active = (i !== index);
            });

            this.spriteLgEWalletWithdrawWorld.forEach((tab, i) => {
                tab.active = (i === index);
            });

            this.spriteLabelEWalletWithdrawWorld.forEach((label, i) => {
                let uiOpacity = label.getComponent(UIOpacity);
                if (!uiOpacity) {
                    uiOpacity = label.addComponent(UIOpacity);
                }
                uiOpacity.opacity = i === index ? 255 : 180;
            });

            this.tabToggleEWalletVN.forEach((tab, i) => {
                tab.isChecked = i === index;
            })

            this.spriteActiveEWalletVN.forEach((tab, i) => {
                tab.active = (i === index);
            });

            this.spriteNotActiveEWalletVN.forEach((sprite, i) => {
                sprite.active = (i !== index);
            });

            this.spriteLabelEWalletVN.forEach((label, i) => {
                let uiOpacity = label.getComponent(UIOpacity);
                if (!uiOpacity) {
                    uiOpacity = label.addComponent(UIOpacity);
                }
                uiOpacity.opacity = i === index ? 255 : 180;
            });

            this.spriteLgEWalletVN.forEach((tab, i) => {
                tab.active = (i === index);
            });
        }

        if (index === 0) {
            this.lblTextAddressWithdrawEWalletWorld.string = App.instance.getTextLang("iap274") + " " + "Paypal";
            this.withdrawEWalletTypeSelected = 10;
        } else if (index === 1) {
            this.lblTextAddressWithdrawEWalletWorld.string = App.instance.getTextLang("iap274") + " " + "Payeer";
            this.withdrawEWalletTypeSelected = 12;
        } else if (index === 2) {
            this.lblTextAddressWithdrawEWalletWorld.string = App.instance.getTextLang("iap274") + " " + "Perfect Money";
            this.withdrawEWalletTypeSelected = 14;
        } else if (index === 3) {
            this.lblTextAddressWithdrawEWalletWorld.string = App.instance.getTextLang("iap274") + " " + "Web Money";
            this.withdrawEWalletTypeSelected = 13;
        } else if (index === 4) {
            this.withdrawEWalletTypeSelected = 1;
        } else if (index === 5) {
            this.withdrawEWalletTypeSelected = 3;
        } else if (index === 6) {
            this.withdrawEWalletTypeSelected = 4;
        }

        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 2 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === this.withdrawEWalletTypeSelected) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    if (index < 4) {
                        this.getDataWithdrawEWalletAfterCheckGetButtons();
                    } else {
                        this.getDataWithdrawEWalletVNAfterCheckGetButtons();
                    }
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                    this.showTab(2);
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                this.showTab(2);
            }
        }, false);

        this.withdrawEWalletVNStep1EdbAddress.string = "";
        this.withdrawEWalletVNStep1EdbAmount.string = "";
        this.withdrawEWalletVNStep2Address.string = "";
        this.withdrawEWalletVNStep2Amount.string = "";
        this.withdrawEWalletVNStep2Amount2.string = "";
        this.withdrawEWalletVNEdbStep2OTP.string = "";
        this.withdrawEWalletVNOTPOptionLabel.string = "SMS OTP";
        this.withdrawEWalletVNStep3TransactionIDLabel.string = "";

        this.containerStep1EWalletVNWithdraw.active = true;
        this.containerStep2EWalletVNWithdraw.active = false;
        this.containerStep3EWalletVNWithdraw.active = false;

        this.containerStep1EWalletWithdraw.active = true;
        this.containerStep2EWalletWithdraw.active = false;
        this.containerStep3EWalletWithdraw.active = false;

        this.withdrawEWalletStep1EdbAddress.string = "";
        this.withdrawEWalletStep1EdbAmount.string = "";
        this.withdrawEWalletStep2Address.string = "";
        this.withdrawEWalletStep2Amount.string = "";
        this.withdrawEWalletStep2Amount2.string = "";
        this.withdrawEWalletEdbStep2OTP.string = "";
        this.withdrawEWalletOTPOptionLabel.string = "SMS OTP";
        this.withdrawEWalletStep3TransactionIDLabel.string = "";
    }

    getDataWithdrawEWalletAfterCheckGetButtons() {
        Http.get(Configs.App.DOMAIN_CONFIG['WalletGetInfoForCashOut'], { WalletType: this.withdrawEWalletTypeSelected, CurrencyID : Configs.Login.CurrencyID}, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.withdrawEWalletBalanceTodayValue = response.d.walletInfo.amountEnable;
                this.withdrawEWalletMinToday = response.d.walletInfo.minValue;
                this.withdrawEWalletMaxToday = response.d.walletInfo.maxValue;

                var form = App.instance.getTextLang('txt_from_value');
                var to = App.instance.getTextLang('txt_to_value');

                if (this.withdrawEWalletTypeSelected == 10) {
                    this.withdrawEWalletAddressPlaceholder.string = App.instance.getTextLang('iap300');
                } else {
                    this.withdrawEWalletAddressPlaceholder.string = App.instance.getTextLang('iap301');
                }

                this.withdrawEWalletValuePlaceholder.string = `${form} ${this.withdrawEWalletMinToday.toLocaleString("vi-VN")} ${to} ${this.withdrawEWalletMaxToday.toLocaleString("vi-VN")}`;
                this.withdrawEWalletBalanceToday.string = this.withdrawEWalletBalanceTodayValue.toLocaleString("vi-VN");
            }
        });
    }

    getDataWithdrawEWalletVNAfterCheckGetButtons() {
        Http.get(Configs.App.DOMAIN_CONFIG['WalletGetInfoForCashOut'], { WalletType: this.withdrawEWalletTypeSelected, CurrencyID : Configs.Login.CurrencyID}, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.withdrawEWalletVNBalanceTodayValue = response.d.walletInfo.amountEnable;
                this.withdrawEWalletVNMinToday = response.d.walletInfo.minValue;
                this.withdrawEWalletVNMaxToday = response.d.walletInfo.maxValue;

                var form = App.instance.getTextLang('txt_from_value');
                var to = App.instance.getTextLang('txt_to_value');

                this.withdrawEWalletVNValuePlaceholder.string = `${form} ${this.withdrawEWalletVNMinToday.toLocaleString("vi-VN")} ${to} ${this.withdrawEWalletVNMaxToday.toLocaleString("vi-VN")}`;
                this.withdrawEWalletVNBalanceToday.string = this.withdrawEWalletVNBalanceTodayValue.toLocaleString("vi-VN");
            }
        });
    }

    actSubmitWithdrawEWalletStep1() {
        let address = this.withdrawEWalletStep1EdbAddress.string;
        let amountStr = this.withdrawEWalletStep1EdbAmount.string;
        let amount = parseInt(amountStr);

        if (address === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('wallet_address_required'));
            return;
        }

        switch (this.withdrawEWalletTypeSelected) {
            case 10:
                //Paypal
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(address)) {
                    App.instance.alertDialog.showMsg("Địa chỉ Paypal không hợp lệ!");
                    return;
                }
                break;
            case 12:
                //Payeer
                if (!/^P\d{6,10}$/.test(address)) {
                    App.instance.alertDialog.showMsg("Địa chỉ Payeer không hợp lệ!");
                    return;
                }
                break;
            case 14:
                //Perfect Money
                if (!/^[UEGB]\d{5,10}$/.test(address)) {
                    App.instance.alertDialog.showMsg("Địa chỉ Perfect Money không hợp lệ!");
                    return;
                }
                break;
            case 13:
                //WebMoney
                if (!/^[ZREUB]\d{12}$/.test(address)) {
                    App.instance.alertDialog.showMsg("Địa chỉ WebMoney không hợp lệ!");
                    return;
                }
                break;
            default:
                App.instance.alertDialog.showMsg("Loại ví không hợp lệ!");
                return;

        }



        // let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        // if (!emailRegex.test(address)) {
        //     App.instance.alertDialog.showMsg(App.instance.getTextLang('email_invalid_format'));
        //     return;
        // }

        if (amountStr === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('amount_required'));
            return;
        }

        if (amount > this.withdrawEWalletMaxToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_less_than')} ${this.withdrawEWalletMaxToday.toLocaleString("vi-VN")}`);
            return;
        }

        if (amount < this.withdrawEWalletMinToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_greater_than')} ${this.withdrawEWalletMinToday.toLocaleString("vi-VN")}`);
            return;
        }

        if (isNaN(amount)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('value_invalid_amount'));
            return;
        }

        let data = {
            WalletAccount: amount,
            WalletAccountName: address,
            WalletType: this.withdrawEWalletTypeSelected,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        let rateVNDToUSD = 0;

        const url = Configs.App.DOMAIN_CONFIG['WalletGetInfoForCashOut'];
        const params = { CurrencyID: 1, WalletType: this.withdrawEWalletTypeSelected };

        Http.get(url, params, (status, res) => {
            if (res.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }
            rateVNDToUSD = parseFloat(res.d.config.rate);

            Http.post(Configs.App.DOMAIN_CONFIG['CashOutWalletCreateTrans'], payload, (status, response) => {
                App.instance.showLoading(false);
                if (status === 200 && response.c === 0) {
                    if (response.p[0]) {
                        this.withDrawEwalletWorldOTPContainerStep2.active = true;
                    } else {
                        this.withDrawEwalletWorldOTPContainerStep2.active = false;
                    }

                    this.withdrawEWalletStep2Address.string = address;
                    let vndAmount = amount;
                    this.withdrawEWalletStep2Amount.string = vndAmount.toLocaleString("vi-VN");

                    let usdAmount = vndAmount / rateVNDToUSD;
                    this.withdrawEWalletStep2Amount2.string = `$${usdAmount.toFixed(2)}`;

                    this.containerStep1EWalletWithdraw.active = false;
                    this.containerStep2EWalletWithdraw.active = true;
                }
            });
        });


    }

    actSubmitWithdrawEWalletStep2() {
        let data = {
            OTPType: 1,
            OTP: "000000",
            Timestamp: Utils.getTicks()
        }

        if (this.withDrawEwalletWorldOTPContainerStep2.active) {
            data.OTPType = this.withdrawEWalletOTPOptionLabel.string === "SMS OTP" ? 1 : 3;
            data.OTP = this.withdrawEWalletEdbStep2OTP.string
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CashOutWalletCreateTransConfirm'], payload, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.containerStep2EWalletWithdraw.active = false;
                this.containerStep3EWalletWithdraw.active = true;

                this.withdrawEWalletStep3TransactionIDLabel.string = "TransactionID: " + response.d.transactionID;

                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            }
        });
    }

    populateOTPOptionWithdrawEWallet(event: any, data: string) {
        this.withdrawEWalletOTPOptionToggle.isChecked = false;
        if (data == "sms") {
            this.withdrawEWalletOTPOptionLabel.string = 'SMS OTP';
            return;
        }


        if (data == "telesafe") {
            this.withdrawEWalletOTPOptionLabel.string = 'Telesafe OTP';
            return;
        }
    }

    backWithdrawEWalletStep2() {
        this.containerStep1EWalletWithdraw.active = true;
        this.containerStep2EWalletWithdraw.active = false;
        this.containerStep3EWalletWithdraw.active = false;
    }

    actSubmitWithdrawEWalletVNStep1() {
        let address = this.withdrawEWalletVNStep1EdbAddress.string;
        let amountStr = this.withdrawEWalletVNStep1EdbAmount.string;
        let amount = parseInt(amountStr);

        // switch (this.withdrawEWalletTypeSelected) {
        //     case 1:
        //         //momo
        //         this.withDrawEWalletVNSpriteStep2.spriteFrame = this.spriteLgEWalletVN[0].getComponent(cc.Sprite).spriteFrame;
        //         break;
        //     case 3:
        //         //zalo
        //         this.withDrawEWalletVNSpriteStep2.spriteFrame = this.spriteLgEWalletVN[1].getComponent(cc.Sprite).spriteFrame;
        //         break;
        //     case 4:
        //         //vietelmoney
        //         this.withDrawEWalletVNSpriteStep2.spriteFrame = this.spriteLgEWalletVN[2].getComponent(cc.Sprite).spriteFrame;
        //         break;
        //     default:
        //      break;
        // }

        if (address === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('wallet_address_required'));
            return;
        }

        if (amountStr === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('amount_required'));
            return;
        }

        if (amount > this.withdrawEWalletVNMaxToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_less_than')} ${this.withdrawEWalletVNMaxToday.toLocaleString("vi-VN")}`);
            return;
        }

        if (amount < this.withdrawEWalletVNMinToday) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang('amount_must_be_greater_than')} ${this.withdrawEWalletVNMinToday.toLocaleString("vi-VN")}`);
            return;
        }

        if (isNaN(amount)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('value_invalid_amount'));
            return;
        }

        // if (address === "" || isNaN(amount) || amount < this.withdrawEWalletVNMinToday || amount > this.withdrawEWalletVNMaxToday) {
        //     App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
        //     return;
        // }

        let data = {
            WalletAccount: amount,
            WalletAccountName: address,
            WalletType: this.withdrawEWalletTypeSelected,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CashOutWalletCreateTrans'], payload, (status, response) => {
            App.instance.showLoading(false);
            if (status === 200 && response.c === 0) {
                if (response.p[0]) {
                    this.containerStep2EWalletVNWithdrawOTP.active = true;
                } else {
                    this.containerStep2EWalletVNWithdrawOTP.active = false;
                }

                this.withdrawEWalletVNStep2Address.string = address;
                this.withdrawEWalletVNStep2Amount.string = amount.toLocaleString("vi-VN");
                this.withdrawEWalletVNStep2Amount2.string = amount.toLocaleString("vi-VN");

                this.containerStep1EWalletVNWithdraw.active = false;
                this.containerStep2EWalletVNWithdraw.active = true;
            }
        });
    }

    actSubmitWithdrawEWalletVNStep2() {
        let data = {
            OTPType: 1,
            OTP: "000000",
            Timestamp: Utils.getTicks()
        }

        if (this.containerStep2EWalletVNWithdrawOTP.active) {
            data.OTPType = this.withdrawEWalletVNOTPOptionLabel.string === "SMS OTP" ? 1 : 3;
            data.OTP = this.withdrawEWalletVNEdbStep2OTP.string
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CashOutWalletCreateTransConfirm'], payload, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.containerStep2EWalletVNWithdraw.active = false;
                this.containerStep3EWalletVNWithdraw.active = true;

                this.withdrawEWalletVNStep3TransactionIDLabel.string = "TransactionID: " + response.d.transactionID;

                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            }
        });
    }

    populateOTPOptionWithdrawVNEWallet(event: any, data: string) {
        this.withdrawEWalletVNOTPOptionToggle.isChecked = false;
        if (data == "sms") {
            this.withdrawEWalletVNOTPOptionLabel.string = 'SMS OTP';
            return;
        }


        if (data == "telesafe") {
            this.withdrawEWalletVNOTPOptionLabel.string = 'Telesafe OTP';
            return;
        }
    }

    backWithdrawEWalletVNStep2() {
        this.containerStep1EWalletVNWithdraw.active = true;
        this.containerStep2EWalletVNWithdraw.active = false;
        this.containerStep3EWalletVNWithdraw.active = false;
    }

    showEWalletContainerVN() {
        this.hideContainerMenuWithdraw();
        this.eWalletCotainerVN.active = true;
        this.showEWallet(4);
    }

    hideContainerMenuDeposit() {
        this.containerMenuDeposit.active = false;
    }

    showContainerDepositWithCard() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === 20) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    this.containerDepositWithCard.active = true;
                    this.hideContainerMenuDeposit();
                    this.depositCardCostLabel.string = App.instance.getTextLang('txt_select_card_cost');
                    this.depositCardEdbSeri.string = "";
                    this.depositCardEdbCode.string = "";

                    Http.get(Configs.App.DOMAIN_CONFIG['GetListTelco'], {telcoType: 0}, (status, response) => {
                        if (status === 200 && response.c === 0) {
                            this.populateDropdownDeposit(response.d);

                            const viettelItem = response.d.find(item =>
                                item.telcoName.toLowerCase().includes("viettel")
                            );

                            if (viettelItem) {
                                this.depositCardTelcoNameLabel.string = viettelItem.telcoName;
                                this.onDropdownItemSelectedDeposit(viettelItem.telcoName);
                            }
                        }
                    });
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }
        }, false);
    }

    showContainerDepositWithBank() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === 2) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    this.containerDepositWithBank.active = true;
                    this.hideContainerMenuDeposit();
                    this.showTabBank(0);
                    // this.depositBankVNStep1.active = true;
                    // this.depositBankVNStep2.active = false;
                    // this.clearInputTempAmountDeposit(this.depositBankVNStep1.getComponentInChildren(cc.ToggleContainer));
                    // Http.get(Configs.App.DOMAIN_CONFIG['GetListBank'], { CurrencyID: 1}, (status, res) => {
                    //     if (status === 200 && res.c === 0) {
                    //         this.showTabBank(0);
                    //         this.lblNameBankDeposit.string = res.d.walletName;
                    //         this.lblNumberBankDeposit.string = res.d.walletAccount;
                    //         this.lblNameAccountBank.string = res.d.walletAccountName;
                    //         this.walletCodeDeposit = res.d.walletCode;
                    //
                    //         this.btnCreateCodeContent.getComponent(cc.Button).interactable = true;
                    //         this.btnCreateCodeContent.getChildByName("Active").active = true;
                    //         this.btnCreateCodeContent.getChildByName("notActive").active = false;
                    //         this.lblCodeContent.string = "";
                    //         this.lblTimeExpired.string = "";
                    //     } else {
                    //         App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                    //     }
                    // }, false);
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }

    showContainerDepositWithEWalletWorld() {
        App.instance.showLoading(true);

        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let enabledWalletTypes = response.d.map((button: any) => button.walletType);

                this.tabToggleEWalletDepositWorld.forEach((toggle, index) => {
                    let walletType = [10, 14, 12, 13][index];
                    toggle.node.active = enabledWalletTypes.includes(walletType);
                });

                for (let i = 0; i < this.tabToggleEWalletDepositWorld.length; i++) {
                    if (this.tabToggleEWalletDepositWorld[i].node.active) {
                        this.selectedToggleEWalletDepositWorld(this.tabToggleEWalletDepositWorld[i], `${i}`);
                        break;
                    }
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }


    copyLabelText(event: Event, labelName: string) {
        let label = this[labelName];
        if (label instanceof Label) {
            const text = label.string;
            if (sys.isBrowser) {
                navigator.clipboard.writeText(text).then(() => {
                    // App.instance.alertDialog.showMsg("Copied");
                }).catch(err => {
                    // cc.log("Error Copy", err);
                });
            }
        }
    }

    selectedToggleEWalletDepositWorld(toggle: Toggle, index: string) {
        toggle.isChecked = true;
        let tabIndex = parseInt(index);
        this.nodeContentCodeEWalletWorld.active = false;

        if (tabIndex === 0) {
            this.lblContentNoteEWalletWorld.string = App.instance.getTextLang("paypal_topup_instructions");
            this.depositEWalletWalletType = 10;
            this.keyWalletAccountEWalletWorldDeposit.string = App.instance.getTextLang("paypal_account_number");
        } else if (tabIndex === 1) {
            this.lblContentNoteEWalletWorld.string = App.instance.getTextLang("ewallet_topup_instructions_payeer");
            this.depositEWalletWalletType = 14;
            this.keyWalletAccountEWalletWorldDeposit.string = App.instance.getTextLang("payeer_account_number");
        } else if (tabIndex === 2) {
            this.lblContentNoteEWalletWorld.string = App.instance.getTextLang("ewallet_topup_instructions_perfectmoney");
            this.depositEWalletWalletType = 12;
            this.keyWalletAccountEWalletWorldDeposit.string = App.instance.getTextLang("perfectmoney_account_number");
        } else if (tabIndex === 3) {
            this.lblContentNoteEWalletWorld.string = App.instance.getTextLang("ewallet_topup_instructions_webmoney");
            this.depositEWalletWalletType = 13;
            this.keyWalletAccountEWalletWorldDeposit.string = App.instance.getTextLang("webmoney_account_number");
        }

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === this.depositEWalletWalletType) {
                        let promoNode = toggle.node
                            .getChildByName("Promotion")
                            ?.getChildByName("promo-sticker-flip")
                            ?.getChildByName("lblPromotion");

                        if (promoNode && promoNode.getComponent(Label)) {
                            const percent = button.promotion ? (button.promotion - 100) : 0;
                            // promoNode.getComponent(cc.Label).string = `${percent}%`;
                            if (percent > 0) {
                                promoNode.getComponent(Label).string = `${percent}%`;
                                promoNode.parent.active = true;
                            } else {
                                promoNode.parent.active = false;
                            }
                        }
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    toggle.isChecked = true;
                    this.containerDepositWithWalletWorld.active = true;
                    this.hideContainerMenuDeposit();
                    this.depositEWalletWorldStep1.active = true;


                    // this.depositEWalletWorldStep2.active = false;
                    // this.clearInputTempAmountDeposit(this.depositEWalletWorldStep1.getComponentInChildren(cc.ToggleContainer));
                    this.updateToggleUIEWalletWorld(tabIndex);

                    Http.get(Configs.App.DOMAIN_CONFIG['GetWalletGetInfo'], {WalletType: this.depositEWalletWalletType, CurrencyID: 1}, (status, res) => {
                        if (status === 200 && res.c === 0) {
                            this.lblWalletAccountEWalletWorldDeposit.string = res.d.walletAccount;
                            this.lblWalletAccountNameEWalletWorldDeposit.string = res.d.walletAccountName;
                            this.lblWalletNameEWalletWorldDeposit = res.d.walletName;
                            this.lblWalletCodeEWalletWorldDeposit = res.d.walletCode;

                            this.btnCreateCodeContentEWalletWorld.getComponent(Button).interactable = true;

                            this.btnCreateCodeContentEWalletWorld.getChildByName("Active").active = true;
                            this.btnCreateCodeContentEWalletWorld.getChildByName("notActive").active = false;
                            this.lblContentEWalletWorldDeposit.string = "";
                        } else {
                            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                        }
                    }, false);
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }

    updateToggleUIEWalletWorld(selectedIndex: number) {
        this.spriteActiveEWalletDepositWorld.forEach((sprite, i) => {
            sprite.active = (i === selectedIndex);
        });

        this.spriteNotActiveEWalletDepositWorld.forEach((sprite, i) => {
            sprite.active = (i !== selectedIndex);
        });

        this.spriteLgEWalletDepositWorld.forEach((sprite, i) => {
            sprite.active = (i === selectedIndex);
        });

        this.spriteLabelEWalletDepositWorld.forEach((label, i) => {
            let uiOpacity = label.getComponent(UIOpacity);
            if (!uiOpacity) {
                uiOpacity = label.addComponent(UIOpacity);
            }
            uiOpacity.opacity = i === selectedIndex ? 255 : 180;
        });
    }

    showContainerDepositWithEWalletVN() {
        this.selectedToggleEWalletDepositVN(this.tabToggleEWalletDepositVN[0], "0");
    }

    selectedToggleEWalletDepositVN(toggle: Toggle, index: string) {
        let tabIndex = parseInt(index);
        this.nodeContentCodeEWalletVN.active = false;

        if (tabIndex === 0) {
            this.depositEWalletWalletType = 1;
            this.lblContentNoteEWalletVN.string = App.instance.getTextLang("momo_topup_instructions");
            this.keyWalletAccountEWalletVNDeposit.string = App.instance.getTextLang("momo_account_number");
        } else if (tabIndex === 1) {
            this.depositEWalletWalletType = 3;
            this.lblContentNoteEWalletVN.string = App.instance.getTextLang("zalopay_topup_instructions");
            this.keyWalletAccountEWalletVNDeposit.string = App.instance.getTextLang("zalo_account_number");
        } else if (tabIndex === 2) {
            this.depositEWalletWalletType = 4;
            this.lblContentNoteEWalletVN.string = App.instance.getTextLang("viettelpay_topup_instructions");
            this.keyWalletAccountEWalletVNDeposit.string = App.instance.getTextLang("viettel_account_number");
        } else if (tabIndex === 3) {
            this.depositEWalletWalletType = 6;
            this.lblContentNoteEWalletVN.string = App.instance.getTextLang("vnptpay_topup_instructions");
            this.keyWalletAccountEWalletVNDeposit.string = App.instance.getTextLang("vnpt_account_number");
        } else if (tabIndex === 4) {
            this.depositEWalletWalletType = 7;
            this.lblContentNoteEWalletVN.string = App.instance.getTextLang("mobifonemoney_topup_instructions");
            this.keyWalletAccountEWalletVNDeposit.string = App.instance.getTextLang("mobifone_account_number");
        }

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === this.depositEWalletWalletType) {
                        isEnable = true;
                    }

                    let promoNode = toggle.node
                        .getChildByName("promo")
                        ?.getChildByName("promo-sticker-flip")
                        ?.getChildByName("lblPromotion");

                    if (promoNode && promoNode.getComponent(Label)) {
                        const percent = button.promotion ? (button.promotion - 100) : 0;
                        // promoNode.getComponent(cc.Label).string = `${percent}%`;
                        if (percent > 0) {
                            promoNode.getComponent(Label).string = `${percent}%`;
                            promoNode.parent.active = true;
                        } else {
                            promoNode.parent.active = false;
                        }
                    }

                });

                if (isEnable) {
                    toggle.isChecked = true;
                    this.showEWalletDepositVN(tabIndex);
                    this.containerDepositWithWalletVN.active = true;
                    this.hideContainerMenuDeposit();
                    this.depositEWalletVNStep1.active = true;

                    Http.get(Configs.App.DOMAIN_CONFIG['GetWalletGetInfo'], {WalletType: this.depositEWalletWalletType, CurrencyID: 1}, (status, res) => {
                        if (status === 200 && res.c === 0) {
                            this.lblWalletAccountEWalletVNDeposit.string = res.d.walletAccount;
                            this.lblWalletAccountNameEWalletVNDeposit.string = res.d.walletAccountName;
                            this.lblWalletNameEWalletVNDeposit = res.d.walletName;
                            this.lblWalletCodeEWalletVNDeposit = res.d.walletCode;

                            this.btnCreateCodeContentEWalletVN.getComponent(Button).interactable = true;

                            this.btnCreateCodeContentEWalletVN.getChildByName("Active").active = true;
                            this.btnCreateCodeContentEWalletVN.getChildByName("notActive").active = false;
                            this.lblContentEWalletVNDeposit.string = "";
                            // cc.log(res.d);
                        } else {
                            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                        }
                    }, false);

                    // this.depositEWalletVNStep2.active = false;
                    // this.clearInputTempAmountDeposit(this.depositEWalletVNStep1.getComponentInChildren(cc.ToggleContainer));
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }

    showEWalletDepositVN(index: number): void {
        // Deposit EWalletVN
        this.tabToggleEWalletDepositVN.forEach((tab, i) => {
            tab.isChecked = i === index;
        })

        this.spriteActiveEWalletDepositVN.forEach((tab, i) => {
            tab.active = (i === index);
        });

        this.spriteNotActiveEWalletDepositVN.forEach((sprite, i) => {
            sprite.active = (i !== index);
        });

        this.spriteLabelEWalletDepositVN.forEach((label, i) => {
            let uiOpacity = label.getComponent(UIOpacity);
            if (!uiOpacity) {
                uiOpacity = label.addComponent(UIOpacity);
            }
            uiOpacity.opacity = i === index ? 255 : 180;
        });

        this.spriteLgEWalletDepositVN.forEach((tab, i) => {
            tab.active = (i === index);
        });
    }

    showContainerDepositWithCrypToUSDT() {
        this.depositCoinWalletType = 5;
        this.showContainerDepositCoin();
    }
    showContainerDepositWithCrypToBUST() {
        this.depositCoinWalletType = 8;
        this.showContainerDepositCoin();
    }
    showContainerDepositWithCrypToUSDC() {
        this.depositCoinWalletType = 9;
        this.showContainerDepositCoin();
    }

    showContainerDepositCoin() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === this.depositCoinWalletType) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    this.getDataDepositCoin();

                    if (this.depositCoinWalletType === 5) {
                        this.containerDepositWithCryptoUSDT.active = true;
                        this.depositCoinUSDTEdbAmount.string = "";
                    } else if (this.depositCoinWalletType === 8) {
                        this.containerDepositWithCryptoBUST.active = true;
                        this.depositCoinBUSTEdbAmount.string = "";
                    } else if (this.depositCoinWalletType === 9) {
                        this.containerDepositWithCryptoUSDC.active = true;
                        this.depositCoinUSDCEdbAmount.string = "";
                    }

                    this.hideContainerMenuDeposit();

                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }

    getCardDataDeposit() {
        if (!Configs.Login.IsLogin) {
            return;
        }

        Http.get(Configs.App.DOMAIN_CONFIG['GetListTelco'], {telcoType: 0}, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.populateDropdownDeposit(response.d);
            }
        });
    }

    populateDropdownDeposit(data: any[]) {
        this.depositCardDropList.removeAllChildren();
        this.depositCardCostLabel.string = App.instance.getTextLang('txt_select_card_cost');
        this.depositCardDropListCost.active = false;
        this.depositCardEdbSeri.string = "";
        this.depositCardEdbCode.string = "";

        data.forEach(item => {
            let dropdownItem = instantiate(this.depositCardDropListItem);
            dropdownItem.active = true;
            dropdownItem.getChildByName("Label").getComponent(Label).string = item.telcoName;

            dropdownItem.on(Node.EventType.TOUCH_END, () => {
                this.onDropdownItemSelectedDeposit(item.telcoName);
                this.depositCardDropList.active = !this.depositCardDropList.active;
                this.depositCardTelcoNameLabel.string = item.telcoName;
            });

            this.depositCardDropList.addChild(dropdownItem);
        });
    }

    onDropdownItemSelectedDeposit(telcoName: string) {
        Http.get(Configs.App.DOMAIN_CONFIG['GetProductList'], {
            TelcoName: telcoName,
            telcoType: 0
        }, (status, response) => {
            if (status === 200 && response.c === 0) {
                this.populateDropdownItemCostDeposit(response.d);
            }
        });
    }

    populateDropdownItemCostDeposit(data: any[]) {
        this.depositCardDropListCost.removeAllChildren();
        this.depositCardCostLabel.string = App.instance.getTextLang('txt_select_card_cost');
        this.depositCardDropListCost.active = false;

        this.depositCardTableList.removeAllChildren();

        data.forEach(item => {
            let dropdownItem = instantiate(this.depositCardDropListCostItem);
            dropdownItem.active = true;
            dropdownItem.getChildByName("Label").getComponent(Label).string = item.amount.toLocaleString("vi-VN");

            dropdownItem.on(Node.EventType.TOUCH_END, () => {
                this.depositCardDropListCost.active = !this.depositCardDropListCost.active;
                this.depositCardCostLabel.string = item.amount.toLocaleString("vi-VN");
            });

            this.depositCardDropListCost.addChild(dropdownItem);

            let tableItem = instantiate(this.depositCardTableItem);
            tableItem.active = true;
            const menhGia = item.amount;
            const tong = item.amount * (item.promotion / 100);

            tableItem.getChildByName("MenhGia").getComponent(Label).string = menhGia.toLocaleString("vi-VN");
            tableItem.getChildByName("Tong").getComponent(Label).string = tong.toLocaleString("vi-VN") + "";

            if (tong > menhGia) {
                const khuyenMai = ((tong - menhGia) / menhGia) * 100;
                tableItem.getChildByName("km").getComponent(Label).string = khuyenMai.toLocaleString() + "%";
            }else {
                tableItem.getChildByName("km").getComponent(Label).string = "";
            }

            this.depositCardTableList.addChild(tableItem);
        });
    }

    actShowNotifiDepositCard() {
        let amount = parseInt(this.depositCardCostLabel.string.replace(/\./g, ""));
        let seri = this.depositCardEdbSeri.string;
        let code = this.depositCardEdbCode.string;

        if (isNaN(amount)) {
            App.instance.alertDialog.showMsg("Vui lòng chọn mệnh giá thẻ!");
            return;
        }
        if (seri == "") {
            App.instance.alertDialog.showMsg("Vui lòng nhập seri!");
            return;
        }


        this.depositCardNotifiConfirm.active = true;

        let seriText = `<color=#FFED26>${seri}</color>`;
        let telcoText = `<color=#FFED26>${this.depositCardTelcoNameLabel.string}</color>`;
        let amountText = `<color=#FFED26>${amount.toLocaleString("vi-VN")}</color>`;

        let confirmMsg = `${App.instance.getTextLang("card_code_entered")} ${seriText}, ${telcoText}, ${App.instance.getTextLang("card_denomination")} ${amountText}, ${App.instance.getTextLang("check_card_denomination_warning")}`;

        this.depositCardNotifiConfirmLabel.getComponent(RichText).string = confirmMsg;
    }

    actAgreeSubmitDepositCard() {
        this.actCancelSubmitDepositCard();
        let data = {
            CurrencyID: Configs.Login.CurrencyID,
            SeriCode: this.depositCardEdbSeri.string,
            CardCode: this.depositCardEdbCode.string,
            Telco: this.depositCardTelcoNameLabel.string,
            Price: parseInt(this.depositCardCostLabel.string.replace(/\./g, "")),
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['TopupUrl'], payload, (status, response) => {
            if (status === 200 && response.c === 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_deposit_card_success'));
                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                this.showTab(0);
            }
        });
    }

    actCancelSubmitDepositCard() {
        this.depositCardNotifiConfirm.active = false;
    }

    actSubmitDepositCard() {
        let amount = parseInt(this.depositCardCostLabel.string.replace(/\./g, ""));
        let seri = this.depositCardEdbSeri.string;
        let code = this.depositCardEdbCode.string;

        let seriText = `<color=#FFED26>${seri}</color>`;
        let telcoText = `<color=#FFED26>${this.depositCardTelcoNameLabel.string}</color>`;
        let amountText = `<color=#FFED26>${amount.toLocaleString("vi-VN")}</color>`;

        let confirmMsg = `Bạn đã nhập mã ${seriText}, ${telcoText}, mệnh giá ${amountText}, vui lòng kiểm tra đúng mệnh giá thẻ, nếu sai Nhà Phát Hành không hỗ trợ`;

        App.instance.confirmDialog.show3(
            confirmMsg,
            App.instance.getTextLang("TITLE_MESSAGE"),
            (isConfirm) => {
                if (isConfirm) {
                    if (!isConfirm) return;
                    let data = {
                        CurrencyID: Configs.Login.CurrencyID,
                        SeriCode: seri,
                        CardCode: code,
                        Telco: this.depositCardTelcoNameLabel.string,
                        Price: amount,
                        Timestamp: Utils.getTicks()
                    }

                    let payload = {
                        "Input": Utils.encryptWithRSA(JSON.stringify(data)),
                    }

                    Http.post(Configs.App.DOMAIN_CONFIG['TopupUrl'], payload, (status, response) => {
                        if (status === 200 && response.c === 0) {
                            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_deposit_card_success'));
                            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                            this.showTab(0);
                        }
                    });
                }
            }
        );
    }

    actSubmitDepositBankVN() {
        var amount = parseInt(this.edbDepositBankVNAmount.string);
        this.clearInputTempAmountDeposit(this.depositBankVNStep1.getComponentInChildren(ToggleContainer));
        if (isNaN(amount)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
            return;
        }

        App.instance.showLoading(true);
        let data = {
            WalletType: 2,
            IAmount: amount,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['BankTopup'], payload, (_status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            this.depositBankVNStep1.active = false;
            this.depositBankVNStep2.active = true;
            this.depositBankVNCode.string = response.d.referTransID;
            this.depositBankVNWalletNameLabel.string = response.d.walletName;
            this.depositBankVNWalletNumberLabel.string = response.d.walletAccount;
            this.depositBankVNWalletAccountLabel.string = response.d.walletAccountName;
            this.depositBankVNAmountLabel.string = amount.toLocaleString("VN");
            Utils.loadSpriteFrameFromBase64(response.d.qrCode, (sprFrame) => {
                this.depositBankVNQR.spriteFrame = sprFrame;
            });

            this.schedule(this.depositBankVNCountdownFunc = () => {
                let time = new Date(response.d.timeExpired).getTime() - new Date().getTime();
                if (time < 0) {
                    this.unschedule(this.depositBankVNCountdownFunc);
                    this.depositBankVNTime.string = '00:00';
                    this.depositBankVNStep1.active = true;
                    this.depositBankVNStep2.active = false;
                    return;
                }

                let minutes = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
                let seconds = Math.floor((time % (1000 * 60)) / 1000);

                let minutesStr = minutes < 10 ? `0${minutes}` : `${minutes}`;
                let secondsStr = seconds < 10 ? `0${seconds}` : `${seconds}`;

                this.depositBankVNTime.string = `${minutesStr}:${secondsStr}`;
            }, 1);
        });
    }

    actSubmitDepositEWallet() {
        var amount = 0;
        if ([1, 3, 4, 6, 7].includes(this.depositEWalletWalletType)) {
            amount = parseInt(this.edbDepositEWalletVNAmount.string);
            this.clearInputTempAmountDeposit(this.depositEWalletVNStep1.getComponentInChildren(ToggleContainer));
        } else if ([10, 12, 13, 14].includes(this.depositEWalletWalletType)) {
            amount = parseInt(this.edbDepositEWalletWorldAmount.string);
            this.clearInputTempAmountDeposit(this.depositEWalletWorldStep1.getComponentInChildren(ToggleContainer));
        }

        if (isNaN(amount) || amount == 0) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
            return;
        }

        App.instance.showLoading(true);
        let data = {
            WalletType: this.depositEWalletWalletType,
            IAmount: amount,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['WalletCreateTrans'], payload, (status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            var walletAccountName = response.d.walletAccountName;
            var walletAccount = response.d.walletAccount;
            var walletCode = response.d.walletCode;
            var timeExpired = response.d.timeExpired;

            if ([1, 3, 4, 6, 7].includes(this.depositEWalletWalletType)) {
                this.depositEWalletVNStep1.active = false;
                this.depositEWalletVNStep2.active = true;
                this.depositEWalletVNWalletAccountLabel.string = walletAccountName;
                this.depositEWalletVNWalletNumberLabel.string = walletAccount;
                this.depositEWalletVNAmount.string = amount.toLocaleString("VN");
                this.depositEWalletVNCode.string = walletCode;
                Utils.loadSpriteFrameFromBase64(response.d.qrCode, (sprFrame) => {
                    this.depositEWalletVNQR.spriteFrame = sprFrame;
                });

                this.unschedule(this.depositEWalletVNCountdownFunc);
                this.schedule(this.depositEWalletVNCountdownFunc = () => {
                    let time = new Date(timeExpired).getTime() - new Date().getTime();
                    if (time < 0) {
                        this.unschedule(this.depositEWalletVNCountdownFunc);
                        this.depositEWalletVNTime.string = '00:00';
                        this.depositEWalletVNStep1.active = true;
                        this.depositEWalletVNStep2.active = false;
                        return;
                    }

                    let minutes = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
                    let seconds = Math.floor((time % (1000 * 60)) / 1000);

                    let minutesStr = minutes < 10 ? `0${minutes}` : `${minutes}`;
                    let secondsStr = seconds < 10 ? `0${seconds}` : `${seconds}`;

                    this.depositEWalletVNTime.string = `${minutesStr}:${secondsStr}`;
                }, 1);
            } else if ([10, 12, 13, 14].includes(this.depositEWalletWalletType)) {
                this.depositEWalletWorldStep1.active = false;
                this.depositEWalletWorldStep2.active = true;
                this.depositEWalletWorldWalletAccountLabel.string = walletAccountName;
                this.depositEWalletWorldWalletNumberLabel.string = walletAccount;
                this.depositEWalletWorldAmount.string = amount.toLocaleString("VN");
                this.depositEWalletWorldCode.string = walletCode;
                this.depositEWalletWorldTime.string = new Date(response.d.timeExpired).toLocaleString("VN");
            }
        });
    }

    getDataDepositCoin() {
        Http.get(Configs.App.DOMAIN_CONFIG['ECoinGetInfo'], { CurrencyID: Configs.Login.CurrencyID, WalletType: this.depositCoinWalletType }, (status, response) => {
            if (status === 200) {
                var key = this.walletType[this.depositCoinWalletType];
                this[`depositCoin${key}WalletAddressLabel`].string = response.d.address;
            }
        });
    }

    actSubmitDepositCoin() {
        var key = this.walletType[this.depositCoinWalletType];
        var amountStr = this[`depositCoin${key}EdbAmount`].string.trim();

        if (amountStr === "") {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_empty_amount'));
            return;
        }

        var amount = parseFloat(amountStr);

        if (isNaN(amount)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_amount'));
            return;
        }

        if (amount < 5) {
            let warnKey = key === "USDT" ? 'usdt_minimum_warning' : 'usdc_minimum_warning';
            App.instance.alertDialog.showMsg(App.instance.getTextLang(warnKey));
            return;
        }

        App.instance.showLoading(true);

        let data = {
            Address: this[`depositCoin${key}WalletAddressLabel`].string,
            Amount: amount,
            WalletType: this.depositCoinWalletType,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['ECoinCreateTrans'], payload, (status, response) => {
            App.instance.showLoading(false);
            if (status === 200) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_success'));
            }
        });
    }

    actNexStep2Transfer() {
        this.step1ContainerTransfer.active = false;
        this.step2ContainerTransfer.active = true;
        this.editBoxUserNameTransfer.string = "";
        this.editBoxUserNameAgainTransfer.string = "";
        this.editBoxCoinTransfer.string = "";
        this.editBoxReasonTransfer.string = "";
    }

    onclickTempAmountDeposit(event: any, data: string) {
        this.edbDepositBankVNAmount.string = data;
        this.edbDepositEWalletVNAmount.string = data;
        this.edbDepositEWalletWorldAmount.string = data;
        var target = event.target.getComponent(Toggle);
        target.node.parent.getComponent(ToggleContainer).toggleItems.forEach((toggle: Toggle) => {
            const textNode = toggle.node.getChildByName('text');
            const label = textNode?.getComponent(Label);
            if (!label) return;

            if (target !== toggle) {
                toggle.isChecked = false;
                label.color = new Color().fromHEX('#69250D');
            } else {
                toggle.isChecked = true;
                label.color = Color.WHITE;
            }
        });
    }

    clearInputTempAmountDepositUI(event: any) {
        this.clearInputTempAmountDeposit(event.target.parent.parent.getComponentInChildren(ToggleContainer));
    }

    clearInputTempAmountDeposit(tc: ToggleContainer) {
        this.edbDepositBankVNAmount.string = "";
        this.edbDepositEWalletVNAmount.string = "";
        this.edbDepositEWalletWorldAmount.string = "";

        tc.toggleItems.forEach((toggle: Toggle) => {
            toggle.isChecked = false;

            const textNode = toggle.node.getChildByName('text');
            const label = textNode?.getComponent(Label);
            if (label) {
                label.color = new Color().fromHEX('#69250D');
            }
        });
    }

    backToMenuDeposit() {
        this.showTab(0);
        this.containerDepositWithCard.active = false;
        this.containerDepositWithBank.active = false;
        this.containerDepositWithWalletWorld.active = false;
        this.containerDepositWithWalletVN.active = false;
        this.containerDepositWithCryptoUSDT.active = false;
        this.containerDepositWithCryptoUSDC.active = false;
        this.containerDepositWithCryptoBUST.active = false;
        this.containerCashQR.active = false;
        this.containerBankThaiLand.active = false;
    }

    backToMenuWithdraw() {
        this.showTab(2);
        this.containerWithDrawWithCard.active = false;
        this.containerWithDrawWithBank.active = false;
        this.containerWithDrawWithWalletWorld.active = false;
        this.containerWithDrawWithWalletVN.active = false;
        this.containerWithDrawWithCrypto.active = false;

    }

    onTabSelectedCashQR(toggle: Toggle, index: string) {
        toggle.isChecked = true;
        let tabIndex = parseInt(index);

        this.showTabCashQR(tabIndex);
    }

    showTabCashQR(index: number) {
        this.tabCashQR.forEach((tab, i) => {
            tab.isChecked = i === index;
        });

        this.spriteActiveTabCashQR.forEach((tab, i) => {
            tab.active = (i === index);
        });

        this.spriteNotActiveTabCashQR.forEach((sprite, i) => {
            sprite.active = (i !== index);
        });

        if (index === 2) {
            this.depositCashQRBankWalletType = 17;
            this.boxDepositCashQRBankName.active = true;
            this.containerCashQREWalletVN.active = false;
            this.containerCashQREWalletWorld.active = false;
            this.clearInputTempAmountDepositCashQR(this.step1ContainerCashQR.getComponentInChildren(ToggleContainer));
            this.step2ContainerCashQR.active = false;
            this.step1ContainerCashQR.active = true;
            this.step1ContainerCashQRUSD.active = false;
            this.lblTextNotifiStep1USD.string = "";
            this.lblTextNotifiStep1.string = "";
        }

        if (index === 0) {
            this.showEWalletDepositVNCashQR(0);
            this.depositCashQRBankWalletType = 18;
            this.containerCashQREWalletVN.active = true;
            this.containerCashQREWalletWorld.active = false;
            this.clearInputTempAmountDepositCashQR(this.step1ContainerCashQR.getComponentInChildren(ToggleContainer));
            this.step2ContainerCashQR.active = false;
            this.step1ContainerCashQR.active = true;
            this.step1ContainerCashQRUSD.active = false;
            this.lblTextNotifiStep1USD.string = "";
            this.lblTextNotifiStep1.string = "";
            this.placeHolderEdbAmount.active = true;
        }

        if (index === 1) {
            this.setEnableDisableEWalletWorldCashQR();
            // this.showEWalletDepositWorldCashQR(0);
            this.depositCashQRBankWalletType = 23;
            this.containerCashQREWalletVN.active = false;
            this.containerCashQREWalletWorld.active = true;
            this.clearInputTempAmountDepositCashQRUSD(this.step1ContainerCashQRUSD.getComponentInChildren(ToggleContainer));
            this.step2ContainerCashQR.active = false;
            this.step1ContainerCashQR.active = false;
            this.step1ContainerCashQRUSD.active = true;
            this.lblTextNotifiStep1USD.string = "";
            this.lblTextNotifiStep1.string = "";
            this.placeHolderEdbAmountUSD.active = true;
        }
    }

    setEnableDisableEWalletWorldCashQR() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                const enabledWallets = response.d.map((b: any) => b.walletType);

                const walletMap = [
                    { eWalletType: 10 }, // PayPal
                    { eWalletType: 12 }, // PerfectMoney
                    { eWalletType: 14 }, // Payeer
                    { eWalletType: 13 }  // WebMoney
                ];

                this.tabToggleEWalletWorldCashQR.forEach((toggleNode, i) => {
                    const walletType = walletMap[i].eWalletType;
                    toggleNode.node.active = enabledWallets.includes(walletType);
                });

                let firstEnabledIndex = walletMap.findIndex(w => enabledWallets.includes(w.eWalletType));
                if (firstEnabledIndex !== -1) {
                    this.showEWalletDepositWorldCashQR(firstEnabledIndex);
                    this.depositCashQRBankWalletType = [23, 26, 24, 25][firstEnabledIndex];
                    this.depositEWalletWalletTypeCashQRWorld = walletMap[firstEnabledIndex].eWalletType;
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        });
    }

    showContainerCashQR() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === 2) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    this.showTabCashQR(0);
                    this.showEWalletDepositVNCashQR(0);
                    this.depositCashQRBankWalletType = 18;
                    this.containerCashQR.active = true;
                    this.hideContainerMenuDeposit();
                    this.step1ContainerCashQR.active = true;
                    this.step2ContainerCashQR.active = false;
                    this.clearInputTempAmountDepositCashQR(this.step1ContainerCashQR.getComponentInChildren(ToggleContainer));
                    this.containerCashQREWalletVN.active = true;
                    this.containerCashQREWalletWorld.active = false;
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }
            App.instance.showLoading(false);
        }, false);
    }

    onclickTempAmountCashQR(event: any, data: string) {
        this.placeHolderEdbAmount.active = false;

        let number = parseInt(data.replace(/\D/g, ''), 10);
        this.edbDepositCashQR.string = isNaN(number) ? "0" : number.toLocaleString("vi-VN");

        var target = event.target.getComponent(Toggle);
        target.node.parent.getComponent(ToggleContainer).toggleItems.forEach((toggle: Toggle) => {
            const textNode = toggle.node.getChildByName('text');
            const label = textNode?.getComponent(Label);
            if (!label) return;

            if (target !== toggle) {
                toggle.isChecked = false;
                label.color = new Color().fromHEX('#69250D');
            } else {
                toggle.isChecked = true;
                label.color = Color.WHITE;
            }
        });
    }

    onclickTempAmountCashQRUSD(event: any, data: string) {
        this.placeHolderEdbAmountUSD.active = false;

        let number = parseInt(data.replace(/\D/g, ''), 10);
        this.edbDepositCashQRUSD.string = isNaN(number) ? "0" : number.toLocaleString("vi-VN");

        var target = event.target.getComponent(Toggle);
        target.node.parent.getComponent(ToggleContainer).toggleItems.forEach((toggle: Toggle) => {
            const textNode = toggle.node.getChildByName('text');
            const label = textNode?.getComponent(Label);
            if (!label) return;

            if (target !== toggle) {
                toggle.isChecked = false;
                label.color = new Color().fromHEX('#69250D');
            } else {
                toggle.isChecked = true;
                label.color = Color.WHITE;
            }
        });
    }

    clearInputTempAmountDepositCashQR(tc: ToggleContainer) {
        this.edbDepositCashQR.string = "";

        tc.toggleItems.forEach((toggle: Toggle) => {
            toggle.isChecked = false;

            const textNode = toggle.node.getChildByName('text');
            const label = textNode?.getComponent(Label);
            if (label) {
                label.color = new Color().fromHEX('#69250D');
            }
        });
    }

    clearInputTempAmountDepositCashQRUSD(tc: ToggleContainer) {
        this.edbDepositCashQRUSD.string = "";
        tc.toggleItems.forEach((toggle: Toggle) => {
            toggle.isChecked = false;

            const textNode = toggle.node.getChildByName('text');
            const label = textNode?.getComponent(Label);
            if (label) {
                label.color = new Color().fromHEX('#69250D');
            }
        });
    }

    clearInputTempAmountCashQRUI(event: any) {
        this.clearInputTempAmountDepositCashQR(event.target.parent.parent.getComponentInChildren(ToggleContainer));
        this.placeHolderEdbAmount.active = true;
    }



    selectedToggleEWalletDepositVNCashQR(toggle: Toggle, index: string) {
        let tabIndex = parseInt(index);
        this.lblTextNotifiStep1.string = "";
        this.placeHolderEdbAmount.active = true;

        if (tabIndex === 0) {
            this.depositCashQRBankWalletType = 18;
            this.depositEWalletWalletTypeCashQRVN = 1;

        } else if (tabIndex === 1) {
            this.depositCashQRBankWalletType = 19;
            this.depositEWalletWalletTypeCashQRVN = 3;
        } else if (tabIndex === 2) {
            this.depositCashQRBankWalletType = 27;
            this.depositEWalletWalletTypeCashQRVN = 4;
        } else if (tabIndex === 3) {
            this.depositCashQRBankWalletType = 21;
            this.depositEWalletWalletTypeCashQRVN = 6;
        } else if (tabIndex === 4) {
            this.depositCashQRBankWalletType = 22;
            this.depositEWalletWalletTypeCashQRVN = 7;
        }

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                // cc.log(response.d);
                response.d.forEach((button: any) => {
                    if (button.walletType === this.depositEWalletWalletTypeCashQRVN) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    toggle.isChecked = true;
                    this.showEWalletDepositVNCashQR(tabIndex);
                    this.clearInputTempAmountDepositCashQR(this.step1ContainerCashQR.getComponentInChildren(ToggleContainer));
                    this.step2ContainerCashQR.active = false;
                    this.step1ContainerCashQR.active = true;
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }

    showEWalletDepositVNCashQR(index: number): void {
        // Deposit EWalletVN
        this.tabToggleEWalletVNCashQR.forEach((tab, i) => {
            tab.isChecked = i === index;
        })

        this.spriteActiveEWalletVNCashQR.forEach((tab, i) => {
            tab.active = (i === index);
        });

        this.spriteNotActiveEWalletVNCashQR.forEach((sprite, i) => {
            sprite.active = (i !== index);
        });

        this.spriteLabelEWalletVNCashQR.forEach((label, i) => {
            const uiOpacity = label.getComponent(UIOpacity);
            if (uiOpacity) {
                uiOpacity.opacity = (i === index) ? 255 : 180;
            }
        });
    }

    selectedToggleEWalletDepositWorldCashQR(toggle: Toggle, index: string) {
        let tabIndex = parseInt(index);
        this.lblTextNotifiStep1USD.string = "";
        this.placeHolderEdbAmountUSD.active = true;
        if (tabIndex === 0) {
            this.depositCashQRBankWalletType = 23;
            this.depositEWalletWalletTypeCashQRWorld = 10;
        } else if (tabIndex === 1) {
            this.depositCashQRBankWalletType = 26;
            this.depositEWalletWalletTypeCashQRWorld = 12;
        } else if (tabIndex === 2) {
            this.depositCashQRBankWalletType = 24;
            this.depositEWalletWalletTypeCashQRWorld = 14;
        } else if (tabIndex === 3) {
            this.depositCashQRBankWalletType = 25;
            this.depositEWalletWalletTypeCashQRWorld = 13;
        }

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === this.depositEWalletWalletTypeCashQRWorld) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    toggle.isChecked = true;
                    this.showEWalletDepositWorldCashQR(tabIndex);
                    this.clearInputTempAmountDepositCashQR(this.step1ContainerCashQR.getComponentInChildren(ToggleContainer));
                    this.step2ContainerCashQR.active = false;
                    this.step1ContainerCashQR.active = false;
                    this.step1ContainerCashQRUSD.active = true;
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }

    showEWalletDepositWorldCashQR(index: number): void {
        // Deposit EWalletWorld
        this.tabToggleEWalletWorldCashQR.forEach((tab, i) => {
            tab.isChecked = i === index;
        })

        this.spriteActiveEWalletWorldCashQR.forEach((tab, i) => {
            tab.active = (i === index);
        });

        this.spriteNotActiveEWalletWorldCashQR.forEach((sprite, i) => {
            sprite.active = (i !== index);
        });

        this.spriteLabelEWalletWorldCashQR.forEach((label, i) => {
            const uiOpacity = label.getComponent(UIOpacity);
            if (uiOpacity) {
                uiOpacity.opacity = (i === index) ? 255 : 180;
            }
            // label.opacity = i === index ? 255 : 180;
        });
    }




    actSubmitDepositCashQR() {
        var amount = 0;
        let isVND = false;
        // cc.log("---depositCashQRBankWalletType " +this.depositCashQRBankWalletType);
        if (this.depositCashQRBankWalletType === 17) {
            isVND = true;
            this.boxDepositCashQRBankName.active = true;
            amount = parseFloat(this.edbDepositCashQR.string.replace(/\./g, ''));
            this.clearInputTempAmountDepositCashQR(this.step1ContainerCashQR.getComponentInChildren(ToggleContainer));
        }
        else if ([18, 19, 21, 22, 27].includes(this.depositCashQRBankWalletType)) {
            isVND = true;
            this.boxDepositCashQRBankName.active = false;
            amount = parseFloat(this.edbDepositCashQR.string.replace(/\./g, ''));
            this.clearInputTempAmountDepositCashQR(this.step1ContainerCashQR.getComponentInChildren(ToggleContainer));
        }
        else if ([23, 24, 25, 26].includes(this.depositCashQRBankWalletType)) {
            isVND = false;
            this.boxDepositCashQRBankName.active = false;
            amount = parseFloat(this.edbDepositCashQRUSD.string.replace(/\./g, ''));
            this.clearInputTempAmountDepositCashQRUSD(this.step1ContainerCashQRUSD.getComponentInChildren(ToggleContainer));
        }

        if (isNaN(amount) || amount <= 0) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_invalid_input'));
            return;
        }

        if (isVND) {
            if (amount < 10000 || amount > ********) {
                this.lblTextNotifiStep1.string = "Bạn cần nhập tối thiểu 10.000 và tối đa 20.000.000";
                return;
            }
        } else {
            if (amount < 1 || amount > 5000) {
                this.lblTextNotifiStep1USD.string = "Bạn cần nhập tối thiểu 1 và tối đa 5.000";
                return;
            }
        }


        // if (isVND && amount < 10000 || amount > ********) {
        //     this.lblTextNotifiStep1.string = "Bạn cần nhập tối thiểu 10.000 và tối đa 20.000.000";
        //     return;
        // }
        //
        // if (!isVND && amount < 1 || amount > 5000) {
        //     this.lblTextNotifiStep1USD.string = "Bạn cần nhập tối thiểu 1 và tối đa 5.000";
        //     return;
        // }



        App.instance.showLoading(true);
        let data = {
            IAmount: amount,
            WalletType: this.depositCashQRBankWalletType,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['QRCodeCreateTran'], payload, (status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            this.checkWallet(Number(this.depositCashQRBankWalletType));

            // cc.log(response.d);
            var walletBankName = response.d.walletName;
            var walletAccountName = response.d.walletAccountName;
            var walletAccount = response.d.walletAccount;
            var walletCode = response.d.walletCode;
            var timeExpired = response.d.timeExpired;
            var referTransID = response.d.referTransID;

            this.step1ContainerCashQR.active = false;
            this.step1ContainerCashQRUSD.active = false;
            this.step2ContainerCashQR.active = true;
            this.lblDepositCashQRBankName.string = walletBankName;
            this.lblDepositCashQRNumberSTK.string = walletAccount;
            this.lblDepositCashQRNameOwner.string = walletAccountName;
            this.lblDepositCashQRContent.string = referTransID;
            if (isVND) {
                this.lblDepositCashQRAmount.string = amount.toLocaleString("vi-VN") + " VNĐ";
            }else {
                this.lblDepositCashQRAmount.string = amount.toLocaleString("vi-VN") + " USD";
            }
            // cc.log(response.d.qrCode);
            if (response.d.qrCode) {
                this.lblTimeWhenNotQR.string = "";
                this.lblTimeNoteWhenNotQR.string = "";
                this.depositSpriteCashQR.spriteFrame = null;

                this.containerQRCodeSprite.active = true;

                Utils.loadSpriteFrameFromBase64(response.d.qrCode, (sprFrame: SpriteFrame) => {
                    this.depositSpriteCashQR.spriteFrame = sprFrame;
                    this.depositSpriteCashQR.node.getComponent(UITransform).setContentSize(300, 300);
                });

            } else {
                this.containerQRCodeSprite.active = false;
                this.depositSpriteCashQR.spriteFrame = null;

                const expiredDate = new Date(response.d.timeExpired);

                const day = expiredDate.getDate().toString().padStart(2, '0');
                const month = (expiredDate.getMonth() + 1).toString().padStart(2, '0');
                const year = expiredDate.getFullYear();

                const hours = expiredDate.getHours().toString().padStart(2, '0');
                const minutes = expiredDate.getMinutes().toString().padStart(2, '0');
                const seconds = expiredDate.getSeconds().toString().padStart(2, '0');

                this.lblTimeWhenNotQR.string = App.instance.getTextLang("iap242")  + ` ${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
                this.lblTimeNoteWhenNotQR.string = App.instance.getTextLang("one_time_code");


                return;
            }

            this.unschedule(this.depositCashQRCountdownFunc);
            this.schedule(this.depositCashQRCountdownFunc = () => {
                let time = new Date(timeExpired).getTime() - new Date().getTime();
                if (time < 0) {
                    this.unschedule(this.depositCashQRCountdownFunc);
                    this.lblDepositCashQRTime.string = '00:00';
                    if (isVND) {
                        this.step1ContainerCashQR.active = true;
                    }else {
                        this.step1ContainerCashQRUSD.active = true;
                    }
                    this.step2ContainerCashQR.active = false;
                    return;
                }

                let minutes = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
                let seconds = Math.floor((time % (1000 * 60)) / 1000);

                let minutesStr = minutes < 10 ? `0${minutes}` : `${minutes}`;
                let secondsStr = seconds < 10 ? `0${seconds}` : `${seconds}`;

                this.lblDepositCashQRTime.string = `${minutesStr}:${secondsStr}`;
            }, 1);

        });
    }

    checkWallet(index: number) {
        switch (index) {
            case 18:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("18_momo");
                break;
            case 19:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("19_zalo");
                break;
            case 27:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("27_viettelpay");
                break;
            case 21:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("21_vnpt");
                break;
            case 22:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("22_mobifone");
                break;
            case 23:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("23_paypal");
                break;
            case 26:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("26_payeer");
                break;
            case 24:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("24_perfect");
                break;
            case 25:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("25_web");
                break;
            default:
                this.lblTextUserNameCashQR.string = App.instance.getTextLang("default_wallet");
                break;

        }
    }

    actCreateContentDepositBank() {
        App.instance.showLoading(true);
        let data = {
            WalletAccount: this.lblNumberBankDeposit.string,
            WalletAccountName: this.lblNameAccountBank.string,
            WalletName: this.lblNameBankDeposit.string,
            WalletCode: this.walletCodeDeposit,
            WalletType: 2,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['BankTopup'], payload, (_status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            this.nodeContentCodeBank.active = true;


            this.lblCodeContent.string = response.d.referTransID;

            const expiredDate = new Date(response.d.timeExpired);

            const day = expiredDate.getDate().toString().padStart(2, '0');
            const month = (expiredDate.getMonth() + 1).toString().padStart(2, '0');
            const year = expiredDate.getFullYear();

            const hours = expiredDate.getHours().toString().padStart(2, '0');
            const minutes = expiredDate.getMinutes().toString().padStart(2, '0');
            const seconds = expiredDate.getSeconds().toString().padStart(2, '0');

            this.lblTimeExpired.string = App.instance.getTextLang("iap242")  + ` ${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;

            this.btnCreateCodeContent.getComponent(Button).interactable = false;

            this.btnCreateCodeContent.getChildByName("Active").active = false;
            this.btnCreateCodeContent.getChildByName("notActive").active = true;


        });
    }

    actCreateContentDepositEWalletVN() {
        App.instance.showLoading(true);
        let data = {
            WalletAccount: this.lblWalletAccountEWalletVNDeposit.string,
            WalletAccountName: this.lblWalletAccountNameEWalletVNDeposit.string,
            WalletName: this.lblWalletNameEWalletVNDeposit,
            WalletCode: this.lblWalletCodeEWalletVNDeposit,
            WalletType: this.depositEWalletWalletType,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['WalletCreateTrans'], payload, (_status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            this.nodeContentCodeEWalletVN.active = true;

            this.lblContentEWalletVNDeposit.string = response.d.referTransID;

            const expiredDate = new Date(response.d.timeExpired);

            const day = expiredDate.getDate().toString().padStart(2, '0');
            const month = (expiredDate.getMonth() + 1).toString().padStart(2, '0');
            const year = expiredDate.getFullYear();

            const hours = expiredDate.getHours().toString().padStart(2, '0');
            const minutes = expiredDate.getMinutes().toString().padStart(2, '0');
            const seconds = expiredDate.getSeconds().toString().padStart(2, '0');


            this.lblTimeExpiredEWalletVN.string = App.instance.getTextLang("iap242")  + ` ${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;

            this.btnCreateCodeContentEWalletVN.getComponent(Button).interactable = false;

            this.btnCreateCodeContentEWalletVN.getChildByName("Active").active = false;
            this.btnCreateCodeContentEWalletVN.getChildByName("notActive").active = true;


        });
    }

    actCreateContentDepositEWalletWorld() {
        App.instance.showLoading(true);
        let data = {
            WalletAccount: this.lblWalletAccountEWalletWorldDeposit.string,
            WalletAccountName: this.lblWalletAccountNameEWalletWorldDeposit.string,
            WalletName: this.lblWalletNameEWalletWorldDeposit,
            WalletCode: this.lblWalletCodeEWalletWorldDeposit,
            WalletType: this.depositEWalletWalletType,
            CurrencyID: Configs.Login.CurrencyID,
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['WalletCreateTrans'], payload, (_status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            this.nodeContentCodeEWalletWorld.active = true;

            this.lblContentEWalletWorldDeposit.string = response.d.referTransID;

            const expiredDate = new Date(response.d.timeExpired);

            const day = expiredDate.getDate().toString().padStart(2, '0');
            const month = (expiredDate.getMonth() + 1).toString().padStart(2, '0');
            const year = expiredDate.getFullYear();

            const hours = expiredDate.getHours().toString().padStart(2, '0');
            const minutes = expiredDate.getMinutes().toString().padStart(2, '0');
            const seconds = expiredDate.getSeconds().toString().padStart(2, '0');


            this.lblTimeExpiredEWalletWorld.string = App.instance.getTextLang("iap242")  + ` ${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;

            this.btnCreateCodeContentEWalletWorld.getComponent(Button).interactable = false;

            this.btnCreateCodeContentEWalletWorld.getChildByName("Active").active = false;
            this.btnCreateCodeContentEWalletWorld.getChildByName("notActive").active = true;


        });
    }

    getDataBankTrucTiep() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetListBank'], { CurrencyID: 1}, (status, res) => {
            if (status === 200 && res.c === 0) {
                this.lblNameBankDeposit.string = res.d.walletName;
                this.lblNumberBankDeposit.string = res.d.walletAccount;
                this.lblNameAccountBank.string = res.d.walletAccountName;
                this.walletCodeDeposit = res.d.walletCode;

                this.btnCreateCodeContent.getComponent(Button).interactable = true;
                this.btnCreateCodeContent.getChildByName("Active").active = true;
                this.btnCreateCodeContent.getChildByName("notActive").active = false;
                this.lblCodeContent.string = "";
                this.lblTimeExpired.string = "";
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }
        }, false);
    }

    onTabSelectedBank(toggle: Toggle, index: string) {
        toggle.isChecked = true;
        let tabIndex = parseInt(index);
        // if (tabIndex === 0) {
        //     this.containerBankTrucTiep.active = false;
        //     this.containerBankGianTiep.active = true;
        //     this.selectedBankName = "";
        //     this.depositBankGianTiepLabel.string = App.instance.getTextLang('txt_select_bank');
        //     this.bankCode = "";
        //     this.lblCoinReceive.string = "0";
        //     this.edbAmountBankGianTiep.string = "0";
        // }
        //
        // if (tabIndex === 1) {
        //     this.containerBankTrucTiep.active = true;
        //     this.containerBankGianTiep.active = false;
        //     this.getDataBankTrucTiep();
        //     this.nodeContentCodeBank.active = false;
        // }


        this.showTabBank(tabIndex);
    }

    showTabBank(index: number) {
        this.tabBank.forEach((tab, i) => {
            tab.isChecked = i === index;
        });

        this.spriteActiveTabBank.forEach((tab, i) => {
            tab.active = (i === index);
        });

        this.spriteNotActiveTabBank.forEach((sprite, i) => {
            sprite.active = (i !== index);
        });

        if (index === 0) {
            this.containerBankTrucTiep.active = false;
            this.containerBankGianTiep.active = true;
            this.selectedBankName = "";
            this.depositBankGianTiepLabel.string = App.instance.getTextLang('txt_select_bank');
            this.bankCode = "";
            this.lblCoinReceive.string = "0";
            this.edbAmountBankGianTiep.string = "";
        }

        if (index === 1) {
            this.containerBankTrucTiep.active = true;
            this.containerBankGianTiep.active = false;
            this.getDataBankTrucTiep();
            this.nodeContentCodeBank.active = false;
        }
    }


    getBankDataDeposit() {
        if (!Configs.Login.IsLogin) return;

        if (this.isDepositDropdownInitialized) {
            this.depositBankDropList.active = !this.depositBankDropList.active;
            return;
        }

        let data = {
            CurrencyID: Configs.Login.CurrencyID,
            MoneyType: "VND"
        };

        Http.get(Configs.App.DOMAIN_CONFIG['GetBanks4CashIn'], data, (status, response) => {
            if (status === 200 && response.c === 0) {
                // cc.log(response);
                this.populateDropdownDepositBank(response.d.banks);
                this.isDepositDropdownInitialized = true;
                this.depositBankDropList.active = true;
                this.rate = response.d.prizeValues[0].rate;
            }
        });
    }

    populateDropdownDepositBank(data: any[]) {
        this.depositBankDropList.removeAllChildren();

        if (!this.selectedBankName) {
            this.depositBankGianTiepLabel.string = App.instance.getTextLang('txt_select_bank');
        } else {
            this.depositBankGianTiepLabel.string = this.selectedBankName;
        }

        data.forEach(item => {
            // cc.log(item);
            let dropdownItem = instantiate(this.depositBankDropListItem);
            dropdownItem.active = true;
            dropdownItem.getChildByName("Label").getComponent(Label).string = item.bankName + " - " + "VND";

            dropdownItem.on(Node.EventType.TOUCH_END, () => {
                this.selectedBankName = item.bankName;
                this.depositBankGianTiepLabel.string = item.bankName + " - " + "VND";
                this.bankCode = item.bankCode;
                this.depositBankDropList.active = false;
                this.minAmountBank = item.minAmount;
                this.maxAmountBank = item.maxAmount;
            });

            this.depositBankDropList.addChild(dropdownItem);
        });
    }

    actSubmitBankGianTiep() {
        let valueStr = this.edbAmountBankGianTiep.string.replace(/[^0-9]/g, '');

        if (!this.selectedBankName) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_select_bank1'));
            return;
        }

        if (!valueStr) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang("amount_required"));
            return;
        }

        let IBankValue = parseInt(valueStr, 10);

        if (IBankValue < this.minAmountBank) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang("amount_required_in_range")} ${Utils.formatNumber(this.minAmountBank)} ${App.instance.getTextLang("iap35")} ${Utils.formatNumber(this.maxAmountBank)}`);
            return;
        }

        if (IBankValue > this.maxAmountBank) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang("amount_required_in_range")} ${Utils.formatNumber(this.minAmountBank)} ${App.instance.getTextLang("iap35")} ${Utils.formatNumber(this.maxAmountBank)}`);
            return;
        }

        App.instance.showLoading(true);

        let data = {
            CurrencyID: Configs.Login.CurrencyID,
            BankCode: this.bankCode,
            IBankValue: parseInt(this.edbAmountBankGianTiep.string),
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CreateTran4BankCashIn'], payload, (_status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            if (response.m && typeof response.m === "string") {
                sys.openURL(response.m);
            } else {
                App.instance.alertDialog.showMsg("Không tìm thấy liên kết thanh toán.");
            }

            this.selectedBankName = "";
            this.depositBankGianTiepLabel.string = App.instance.getTextLang('txt_select_bank');
            this.bankCode = "";
            this.lblCoinReceive.string = "0";
            this.edbAmountBankGianTiep.string = "";
        });
    }

    showContainerDepositWithBankThaiLand() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetButtons'], { CurrencyID: 1, ButtonType: 1 }, (status, response) => {
            if (status === 200 && response.c === 0) {
                let isEnable = false;
                response.d.forEach((button: any) => {
                    if (button.walletType === 16) {
                        isEnable = true;
                    }
                });

                if (isEnable) {
                    this.containerBankThaiLand.active = true;
                    this.hideContainerMenuDeposit();
                } else {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
                }
            } else {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_func_not_open'));
            }

            App.instance.showLoading(false);
        }, false);
    }

    getBankDataDepositThaiLand() {
        if (!Configs.Login.IsLogin) return;

        if (this.isDepositDropdownInitializedThaiLand) {
            this.depositBankDropListThaiLand.active = !this.depositBankDropListThaiLand.active;
            return;
        }

        let data = {
            CurrencyID: Configs.Login.CurrencyID,
            MoneyType: "THB"
        };

        Http.get(Configs.App.DOMAIN_CONFIG['GetBanks4CashIn'], data, (status, response) => {
            if (status === 200 && response.c === 0) {
                // cc.log(response);
                this.populateDropdownDepositBankThaiLand(response.d.banks);
                this.isDepositDropdownInitializedThaiLand = true;
                this.depositBankDropListThaiLand.active = true;
                this.rateThaiLand = response.d.prizeValues[1].rate;
            }
        });
    }

    populateDropdownDepositBankThaiLand(data: any[]) {
        this.depositBankDropListThaiLand.removeAllChildren();

        if (!this.selectedBankNameThaiLand) {
            this.depositBankGianTiepLabelThaiLand.string = App.instance.getTextLang('txt_select_bank');
        } else {
            this.depositBankGianTiepLabelThaiLand.string = this.selectedBankNameThaiLand;
        }

        data.forEach(item => {
            let dropdownItem = instantiate(this.depositBankDropListItemThaiLand);
            dropdownItem.active = true;
            dropdownItem.getChildByName("Label").getComponent(Label).string = item.bankName + " - " + "THB";

            dropdownItem.on(Node.EventType.TOUCH_END, () => {
                this.selectedBankNameThaiLand = item.bankName;
                this.depositBankGianTiepLabelThaiLand.string = item.bankName + " - " + "THB";
                this.bankCodeThaiLand = item.bankCode;
                this.depositBankDropListThaiLand.active = false;
                this.minAmountBankThaiLand = item.minAmount;
                this.maxAmountBankThaiLand = item.maxAmount;
            });

            this.depositBankDropListThaiLand.addChild(dropdownItem);
        });
    }

    actSubmitBankGianTiepThaiLand() {
        let valueStr = this.edbAmountBankGianTiepThaiLand.string.replace(/[^0-9]/g, '');

        if (!this.selectedBankNameThaiLand) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_select_bank1'));
            return;
        }

        if (!valueStr) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang("amount_required"));
            return;
        }

        let IBankValue = parseInt(valueStr, 10);

        if (IBankValue < this.minAmountBankThaiLand) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang("amount_required_in_range")} ${Utils.formatNumber(this.minAmountBankThaiLand)} ${App.instance.getTextLang("iap35")} ${Utils.formatNumber(this.maxAmountBankThaiLand)}`);
            return;
        }

        if (IBankValue > this.maxAmountBankThaiLand) {
            App.instance.alertDialog.showMsg(`${App.instance.getTextLang("amount_required_in_range")} ${Utils.formatNumber(this.minAmountBankThaiLand)} ${App.instance.getTextLang("iap35")} ${Utils.formatNumber(this.maxAmountBankThaiLand)}`);
            return;
        }

        App.instance.showLoading(true);

        let data = {
            CurrencyID: Configs.Login.CurrencyID,
            BankCode: this.bankCodeThaiLand,
            IBankValue: parseInt(this.edbAmountBankGianTiepThaiLand.string),
            Timestamp: Utils.getTicks()
        }

        let payload = {
            "Input": Utils.encryptWithRSA(JSON.stringify(data)),
        }

        Http.post(Configs.App.DOMAIN_CONFIG['CreateTran4BankCashIn'], payload, (_status, response) => {
            App.instance.showLoading(false);
            if (response.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${response.c}`));
                return;
            }

            if (response.m && typeof response.m === "string") {
                sys.openURL(response.m);
            } else {
                App.instance.alertDialog.showMsg("Không tìm thấy liên kết thanh toán.");
            }

            this.selectedBankNameThaiLand = "";
            this.depositBankGianTiepLabelThaiLand.string = App.instance.getTextLang('txt_select_bank');
            this.bankCodeThaiLand = "";
            this.lblCoinReceiveThaiLand.string = "0";
            this.edbAmountBankGianTiepThaiLand.string = "";
        });
    }




}

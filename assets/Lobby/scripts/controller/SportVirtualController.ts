import {_decorator, Component, WebView} from 'cc';
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";

const { ccclass, property } = _decorator;

@ccclass('SportVirtualController')
export class SportVirtualController extends Component {
    @property(WebView)
    webView: WebView = null;
    private initialized = false;

    public onEnable() {
        this.webView.url = `${Configs.App.DOMAIN_CONFIG['SportVirtualUrl']}?page=virtualsports&token=${Configs.Login.AccessToken}`;
        this.webView.node.on(WebView.EventType.LOADED, this.callback, this);
    }

    callback(webview: WebView) {
        if (this.initialized) {
            this.dismiss();
            return;
        }

        this.initialized = true;
    }

    public dismiss() {
        App.instance.gotoLobby();
    }
}


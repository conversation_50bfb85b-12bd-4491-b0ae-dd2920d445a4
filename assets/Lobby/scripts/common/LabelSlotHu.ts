import { _decorator, Component, Label, math } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('LabelSlotHu')
export class LabelSlotHu extends Component {
    @property(Label)
    label: Label | null = null;

    @property
    milestone: number = 1000;

    @property
    finalNumber: number = 5000;

    @property
    fastDuration: number = 2;

    @property
    minStep: number = 100;

    @property
    maxStep: number = 1000;

    @property
    allowPauseInPhase2: boolean = true;

    @property
    pauseChance: number = 0.2;

    @property
    minPause: number = 1;

    @property
    maxPause: number = 2;

    private currentValue: number = 0;
    private _isRunning: boolean = false;

    onLoad() {
        this.startCounter();
    }

    onEnable() {
        if (!this._isRunning) {
            this.startCounter();
        }
    }

    startCounter() {
        this._isRunning = true;
        this.runSequence();
    }

    runSequence() {
        this.currentValue = 0;
        if (this.label) {
            this.label.string = "0";
        }

        const fastStep = (dt: number) => {
            let step = this.milestone / (this.fastDuration / dt);
            this.currentValue += step;
            if (this.currentValue >= this.milestone) {
                this.currentValue = this.milestone;
                this.unschedule(fastStep);
                this.schedule(slowRandomStep, 0.05);
            }
            this.updateLabel();
        };

        const slowRandomStep = () => {
            let randomStep = Math.floor(Math.random() * (this.maxStep - this.minStep)) + this.minStep;
            this.currentValue += randomStep;

            if (this.currentValue >= this.finalNumber) {
                this.currentValue = this.finalNumber;
                this.unschedule(slowRandomStep);
                this.scheduleOnce(() => {
                    this.runSequence();
                }, 1);
            }

            this.updateLabel();

            if (this.allowPauseInPhase2 && Math.random() < this.pauseChance) {
                const pauseTime = Math.random() * (this.maxPause - this.minPause) + this.minPause;
                this.unschedule(slowRandomStep);
                this.scheduleOnce(() => {
                    this.schedule(slowRandomStep, 0.05);
                }, pauseTime);
            }
        };

        this.schedule(fastStep, 0);
    }

    updateLabel() {
        if (this.label) {
            this.label.string = Math.floor(this.currentValue).toLocaleString('de-DE');
        }
    }

    onDisable() {
        this._isRunning = false;
        this.unscheduleAllCallbacks();
    }
}

import SignalRClient from "./Network.SignalRClient";
import Configs from "db://assets/Lobby/scripts/common/Config";

export default class CardGameSignalRClient extends SignalRClient {
    public static instance: CardGameSignalRClient = null;

    public static getInstance(): CardGameSignalRClient {
        if (this.instance == null) {
            this.instance = new CardGameSignalRClient();
        }
        return this.instance;
    }

    onSuccess: Function;

    public onstart() {
        this.onSuccess(true);
    }

    public onError() {
        this.onSuccess(false);
    }

    public connectHub(gameId: number, onSuccess: Function) {
        if (this.connection && this.isConnected()) {
            this.close();
        }
        this.onSuccess = onSuccess;
        var host = "";
        switch (gameId) {
            case Configs.InGameIds.TLMN:
                host = Configs.App.DOMAIN_CONFIG['TlmnHubUrl'];
                break;
            case Configs.InGameIds.TLMNSolo:
                host = Configs.App.DOMAIN_CONFIG['TlmnSoloHubUrl'];
                break;
            case Configs.InGameIds.SamLoc:
                host = Configs.App.DOMAIN_CONFIG['SamLocHubUrl'];
                break;
            case Configs.InGameIds.SamLocSolo:
                host = Configs.App.DOMAIN_CONFIG['SamLocSoloHubUrl'];
                break;
            case Configs.InGameIds.BaCay:
                host = Configs.App.DOMAIN_CONFIG['BaCayHubUrl'];
                break;
            case Configs.InGameIds.Poker:
                host = Configs.App.DOMAIN_CONFIG['PokerHubUrl'];
                break;
            case Configs.InGameIds.MauBinh:
                host = Configs.App.DOMAIN_CONFIG['MauBinhHubUrl'];
                break;
            case Configs.InGameIds.Catte:
                host = Configs.App.DOMAIN_CONFIG['CatteHubUrl'];
                break;
            default:
                throw new Error("Unknown game ID: " + gameId);
        }

        super.connect(host);
    }
}
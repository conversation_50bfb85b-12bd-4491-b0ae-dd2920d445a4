import {PhotonClient} from "./PhotonClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";

enum EOperationSubCodes {
    Login = 1,
    GetCaptcha = 2,
    CashInGold = 3,
    CashOutGold = 4,
    JoinRoom = 5,
    LeaveRoom = 6,
    OtherJoinRoom = 7,
    <PERSON><PERSON><PERSON>ighter = 8,
    RemoveFighter = 9,
    ChangeGun = 10,
    PlayerShooting = 11,
    PlayerShotFish = 12,
    UseTarget = 13,
    GetDoubleCanPlay = 14,
    PlayDouble = 15,

    Chat = 21,

    ShowCommandMessage = 22,
    GetRoomInfo = 23,
    GetCurrentJackpot = 24,
    CashInHistory = 25,
    CashOutHistory = 26,
    GetTopJackpot = 27,
    PlayGameHistory = 28,
    GetTopPlayer = 29,
    Ping = 30,
    Pong = 31,
    CashoutMin =32,
}

enum EParameterCodes {
    OperationSubCode = 0,

    // Login
    LoginMessage = 1,
    LoginResponse = 2,
    FishConfigsResponse = 3,
    GunConfigsResponse = 4,

    // Ping pong
    PingResponse = 5,
    PongMessage = 6,

    // Spawn fighter
    CreateFishResponse = 7,
    RemoveSingleFishResponse = 8,

    ClientParameterConfig = 10,

    // Join and leave room
    JoinRoomResponse = 11,
    OtherJoinRoomResponse = 12,
    LeaveRoomResponse = 13,
    RoomInfoResponse = 15,

    // Exchange coin
    ExchangeMessage = 16,
    CashInResponse = 17,
    CashOutResponse = 18,

    // History
    ExChangeHistoryResponse = 19,
    TopJackpotResponse = 20,
    PlayHistoryResponse = 21,
    CaptchaResponse = 22,

    // Change gun
    ChangeGunMessage = 23,
    ChangeGunResponse = 24,

    // Shooting
    ShootingMessage = 25,
    ShootingResponse = 26,
    FishShotMessage = 27,
    HeadShotResponse = 28,

    TimeToUse = 29,
    DoubleConfigsResponse = 30,
    DoubleValuesCanPlay = 31,
    DoubleMessage = 32,
    DoubleMessageResponse = 33,

    ChatMessage = 38,
    ChatResponse = 39,
    ErrorCode = 40,
    CurrencyId = 41,
    CurrentJackpot = 42,
    AccountId = 43,
    PortalId = 44,
    CashoutMinResponse = 45,
}

enum EGunType {
    BombGun = 101,
    MagneticGun = 102
}

enum EHistoryType {
    Shooting = 0,
    FighterDead = 1,
    SupplyChestDead = 2,
    AmmunitionChestDead = 3,
    BossDead = 4,
    Magnetic = 5,
    Bomb = 6
}

enum ENotificationType {
    Win = 1,
    Bomb = 2,
    BossWin = 3,
    Magnetic = 4
}

export class FishHunterPhotonClient extends PhotonClient {
    public static instance: FishHunterPhotonClient;
    static EOperationSubCodes = EOperationSubCodes;
    static EParameterCodes = EParameterCodes;

    public static getInstance(): FishHunterPhotonClient {
        if (this.instance == null) {
            this.instance = new FishHunterPhotonClient();
        }
        return this.instance;
    }

    public connect() {
        super.connect(Configs.App.DOMAIN_CONFIG['FishHunterServerUrl']);
    }
}
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";

enum EOperationCodes {
    Account = 10,
    Game = 20,
    Shop = 30,
    Statistic = 40
}

enum EParameterCodes {
    OperationSubCode = 0,
    LoginMessage = 1,
}

enum EOperationSubCodes {
    Login = 1,
}

export class PhotonClient {
    static getErrMsg(errCode: any): string {
        return App.instance.getTextLang(`fish_err${Math.abs(errCode)}`);
    }

    peer: Photon.PhotonPeer;
    host: string = "";
    isLoggedIn: boolean = false;
    static EOperationCodes = EOperationCodes;

    protected connect(host: string) {
        this.host = host;

        this.peer = new Photon.PhotonPeer(Photon.ConnectionProtocol.Wss, this.host);
        this.setupEventListeners();
        this.peer.connect('');
    }

    private sendLoginRequest() {
        App.instance.showLoading(true);

        const payload = {
            a: Configs.Login.AccessToken,
            c: Configs.Login.CurrencyID,
            p: Configs.Login.PortalID
        }

        var params = [];
        params.push(EParameterCodes.OperationSubCode, EOperationSubCodes.Login);
        params.push(EParameterCodes.LoginMessage, JSON.stringify(payload));

        this.sendOperation(PhotonClient.EOperationCodes.Account, params, true);
    }

    protected setupEventListeners(): void {
        this.addPeerStatusListener(Photon.PhotonPeer.StatusCodes.connect, () => {
            this.sendLoginRequest();
        });

        this.addPeerStatusListener(Photon.PhotonPeer.StatusCodes.disconnect, () => {
            if (App.instance.skillsGameNode.children.length > 0) {
                App.instance.showErrLoading(App.instance.getTextLang("fish_err15"));
                App.instance.skillsGameNode.removeAllChildren();
            }
        });
    }

    public handleErrorResponse(callback: () => void): void {
        this.addPeerStatusListener(Photon.PhotonPeer.StatusCodes.error, () => {
            if (callback) {
                callback();
            }
        });
    }

    public handleTimeoutResponse(callback: () => void): void {
        this.addPeerStatusListener(Photon.PhotonPeer.StatusCodes.timeout, () => {
            if (callback) {
                callback();
            }
        });
    }

    addResponseListener(operationCode: number, callback: (res: any) => void) {
        this.peer.addResponseListener(operationCode, callback);
    }

    addPeerStatusListener(statusCode: string, callback: () => void) {
        this.peer.addPeerStatusListener(statusCode, callback);
    }

    addEventListener(eventCode: number, callback: (res: any) => void) {
        this.peer.addEventListener(eventCode, callback);
    }

    sendOperation(operationCode: number, data?: any, sendReliable?: boolean, channelId?: number) {
        this.peer.sendOperation(operationCode, data, sendReliable, channelId);
    }
}

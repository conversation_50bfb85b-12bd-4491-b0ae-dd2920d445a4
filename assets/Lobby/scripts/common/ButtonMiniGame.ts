import {
    _decorator,
    Component,
    Node,
    Prefab,
    instantiate,
    tween,
    v3,
    Color,
    director,
    EventTarget,
    find,
    UITransform,
    UIOpacity,
    Tween,
    Label,
    easing,
    Scene,
} from 'cc';

import Configs from '../common/Config';
import App from '../common/App';
import {Utils} from '../common/Utils';
import MiniGameSignalRClient from '../common/networks/MiniGameSignalRClient';
import BundleControl from '../../../Loading/scripts/BundleControl';
import Config from "../common/Config";

const { ccclass, property } = _decorator;

@ccclass('ButtonMiniGame')
export default class ButtonMiniGame extends Component {
    static instance: ButtonMiniGame | null = null;
    @property(Node)
    button: Node = null!;
    @property(Node)
    panel: Node = null!;
    @property(Node)
    container: Node = null!;

    private systemEvent: Scene | null = null;

    onLoad() {
        ButtonMiniGame.instance = this;
        this.systemEvent = director.getScene();
        this.systemEvent.on(
            'ShowMoneyAuto',
            this.showEffMoney,
            this
        );
        this.systemEvent.on(
            'activeBlinkAuto',
            this.showEffectBlink,
            this
        );
    }

    show() {
        this.panel.active = false;
        this.button.active = true;
    }

    showEffMoney(data: any) {
        const { game, money: moneyStr } = data;
        const money = parseInt(moneyStr);
        const cb = (prefab: Prefab) => {
            const parent = this.panel.getChildByName('Content')?.getChildByName(game);
            if (parent) {
                const inst = instantiate(prefab);
                const lbl = inst.getComponentInChildren(Label);
                if (!lbl) return;
                lbl.node.setParent(parent.parent);
                lbl.node.setPosition(parent.position);
                lbl.string = `+${Utils.formatMoney(Math.abs(money))}`;
                tween(lbl.node)
                    .by(1.5, { position: v3(0, 120, 0) })
                    .delay(1.5)
                    .call(() => {
                        lbl.node.destroy();
                    })
                    .start();
            }
        };
        BundleControl.loadPrefabPopup('PrefabPopup/TextWinMini', cb);
    }

    showEffectBlink(game: string, active: boolean) {
        const parent = this.panel.getChildByName('Content')?.getChildByName(game);
        if (!parent) return;

        const opacity = parent.getComponent(UIOpacity);
        if (!opacity) return;


        if (active) {
            tween(opacity)
                .to(0.5, { opacity: 100 })
                .to(0.5, { opacity: 255 })
                .repeatForever()
                .start();
        } else {
            Tween.stopAllByTarget(opacity);
            opacity.opacity = 255;
        }
    }


    actButton() {
        this.panel.active = true;
        this.button.active = false;
        const cont = this.panel.getChildByName('Container')!;
        tween(cont)
            .set({ angle: -180, scale: v3(0, 0, 1) })
            .to(0.3, { angle: 0, scale: v3(1, 1, 1) }, { easing: easing.sineOut })
            .start();
    }

    actHidden() {
        const cont = this.panel.getChildByName('Container')!;
        tween(cont)
            .to(0.3, { angle: -180, scale: v3(0, 0, 1) }, { easing: easing.sineIn })
            .call(() => {
                this.panel.active = false;
                this.button.active = true;
            })
            .start();
    }

    actGame<T extends { connectHub: (cb: (s: boolean) => void) => void }>(
        client: T,
        openFn: (name: string, route: string) => void,
        route: string
    ) {
        if (!Configs.Login.IsLogin) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_need_login'));
            return;
        }
        client.connectHub((success) => {
            App.instance.showLoading(false);
            success ? openFn(route, route) : App.instance.showErrLoading(App.instance.getTextLang('me11'));
        });
        this.actHidden();
    }

    actGameTaiXiu() {
        App.instance.openGame(Config.InGameIds.TaiXiuMini);
        this.actHidden();
    }

    actGameTaiXiuMD5() {
        App.instance.openGame(Config.InGameIds.TaiXiuMD5);
        this.actHidden();
    }

    actXoSo() {
        App.instance.openGame(Config.InGameIds.XoSo);
        this.actHidden();
    }

    actVQMM() {
        App.instance.actVQMM();
        this.actHidden();
    }

    protected onDestroy() {
        this.systemEvent?.off('ShowMoneyAuto', this.showEffMoney, this);
        this.systemEvent?.off('activeBlinkAuto', this.showEffectBlink, this);
    }
}

import {
    _decorator,
    Component,
    Node,
    Size,
    Label,
    SpriteFrame,
    instantiate,
    easing,
    Tween,
    tween,
    Prefab,
    log,
    sys,
    native,
} from "cc";
import AlertDialog from "db://assets/Lobby/scripts/common/AlertDialog";
import {LanguageManager} from "db://assets/Lobby/scripts/common/language/Language.LanguageManager";
import ConfirmDialog from "db://assets/Lobby/scripts/common/ConfirmDialog";
import Config from "db://assets/Lobby/scripts/common/Config";
import MiniGameTX1SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX1SignalRClient";
import MiniGameTX2SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX2SignalRClient";
import MiniGameTX3SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTX3SignalRClient";
import MiniGameSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameSignalRClient";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import {MiniGame} from "db://assets/Lobby/scripts/common/MiniGame";
import AudioManager from "./AudioManager";
import {BroadcastReceiver} from "./BroadcastListener";
import MiniGameTXMD5SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXMD5SignalRClient";
import BlackjackSignalRClient from "db://assets/Lobby/scripts/common/networks/BlackjackSignalRClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import SignalRClient from "db://assets/Lobby/scripts/common/networks/Network.SignalRClient";
import BaccaratSignalRClient from "db://assets/Lobby/scripts/common/networks/BaccaratSignalRClient";
import DragonTigerSignalRClient from "db://assets/Lobby/scripts/common/networks/DragonTigerSignalRClient";
import RouletteSignalRClient from "db://assets/Lobby/scripts/common/networks/RouletteSignalRClient";
import SicboSignalRClient from "db://assets/Lobby/scripts/common/networks/SicboSignalRClient";
import SedieSignalRClient from "db://assets/Lobby/scripts/common/networks/SedieSignalRClient";
import KingdomSignalRClient from "./networks/KingdomSignalRClient";
import OceanSignalRClient from "./networks/OceanSignalRClient";
import DQSignalRClient from "./networks/DQSignalRClient";
import ForestSignalRClient from "./networks/ForestSignalRClient";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import CardGameSignalRClient from "db://assets/Lobby/scripts/common/networks/CardGameSignalRClient";
import Http from "db://assets/Lobby/scripts/common/Http";

const {ccclass, property} = _decorator;

@ccclass("App")
export default class App extends Component {
    @property(Node)
    public canvas: Node | null = null;
    static instance: App = null;
    inactivityTimer = 0;
    DataPass = [];

    @property
    designResolution: Size = new Size(1920, 1080);
    @property([SpriteFrame])
    sprFrameAvatars: Array<SpriteFrame> = new Array<SpriteFrame>();
    @property([SpriteFrame])
    listEmotionSpr: SpriteFrame[] = [];
    @property(AlertDialog)
    alertDialog: AlertDialog = null;
    @property(ConfirmDialog)
    confirmDialog: ConfirmDialog = null;
    @property(Node)
    loadingNode: Node = null;
    @property(Label)
    loadingLabel: Label = null;
    @property(Node)
    bigGameNode: Node = null;
    @property(Node)
    skillsGameNode: Node = null;
    @property(Node)
    miniGameNode: Node = null;
    @property(Node)
    popupNode: Node = null;
    @property(Node)
    nodeRoom: Node = null;
    @property(Node)
    tipzoJackpotEventX2X6Node: Node = null;
    @property(Node)
    tipzoMiniLiveNode: Node = null;
    @property(Node)
    alertToast: Node = null;
    @property([Node])
    allNodeGame: Node[] = [];

    public isShowNotifyJackpot = true;
    private timeOutLoading: any = null;
    private taiXiuJackpotMiniGame: MiniGame = null;
    private taiXiuMD5MiniGame: MiniGame = null;
    public gameNodeMap: Map<number, Node[]> = new Map();

    protected onLoad() {
        if (App.instance != null) {
            this.node.destroy();
            return;
        }
        App.instance = this;

        const gameIds = [
            Config.GameAvailableIds.SamLocSolo,
            Config.GameAvailableIds.BaCay,
            Config.GameAvailableIds.Catte,
            Config.GameAvailableIds.TLMN,
            Config.GameAvailableIds.MauBinh,
            Config.GameAvailableIds.Poker,
            Config.GameAvailableIds.TLMNSolo,
            Config.GameAvailableIds.SamLoc,
            Config.GameAvailableIds.Sedie,
            Config.GameAvailableIds.NewSicbo,
            Config.GameAvailableIds.NewRoulette,
            Config.GameAvailableIds.DragonTiger,
            Config.GameAvailableIds.Baccarat,
            Config.GameAvailableIds.Blackjack,
            Config.GameAvailableIds.Kingdom,
            Config.GameAvailableIds.Olympia,
            Config.GameAvailableIds.Ocean,
            Config.GameAvailableIds.Forest,
            Config.GameAvailableIds.GodOfFortune,
            Config.GameAvailableIds.Dancing,
            Config.GameAvailableIds.Disco,
            Config.GameAvailableIds.SoDo,
            Config.GameAvailableIds.SpaceWar,
            Config.GameAvailableIds.Sortie,
            Config.GameAvailableIds.Shark,
            Config.GameAvailableIds.TieuLongNgu,
            Config.GameAvailableIds.BanCa,
            Config.GameAvailableIds.PokerTournament,
            Config.GameAvailableIds.ABC, //chua ro game nay
            Config.GameAvailableIds.TournamentGoOn,
            Config.GameAvailableIds.TournamentOTT,
            Config.GameAvailableIds.MultiLuckyDiceLive,
            Config.GameAvailableIds.LuckyDiceMd5Live,
            Config.GameAvailableIds.SedieLive,
            Config.GameAvailableIds.MegaMillions,
            Config.GameAvailableIds.PowerBall,
            Config.GameAvailableIds.Keno,
            Config.GameAvailableIds.OTT,//xoso
            Config.GameAvailableIds.FantasySport,
            Config.GameAvailableIds.SportVirtual,
            Config.GameAvailableIds.VQMM, //MiniGame
            Config.GameAvailableIds.MiniPoker,
            Config.GameAvailableIds.TaiXiu,
            Config.GameAvailableIds.LuckyDiceMd5,
            Config.GameAvailableIds.HiLo,
            Config.GameAvailableIds.BauCua,
            Config.GameAvailableIds.PhucSinh,
            Config.GameAvailableIds.LuckyWild,
            Config.GameAvailableIds.OTT,
        ];


        this.allNodeGame.forEach((node, index) => {
            const gameId = gameIds[index];
            if (gameId != null) {
                if (!this.gameNodeMap.has(gameId)) {
                    this.gameNodeMap.set(gameId, []);
                }
                this.gameNodeMap.get(gameId).push(node);
                (node as any).gameId = gameId;
            }
        });


        this.setActiveGameById(Config.GameAvailableIds.ABC, false);

    }

    gotoLobby() {
        AudioManager.getInstance().turnOnMusic();
        this.bigGameNode.removeAllChildren();
        this.skillsGameNode.removeAllChildren();
        this.alertDialog.dismiss();
        this.alertToast.active = false;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        Tween.stopAllByTarget(this.alertToast);
        Utils.setStorageValue("last_open_game_id", "");
    }

     static sendEventAF(objectAF) {
            // exeample  Utils.sendEventAF({"eventName":"af_registration","af_System_User_ID":"aaa"})
         
            try {
                var text = JSON.stringify(objectAF);
                log("Send tracking ==== " + text);
                if (sys.os == sys.OS.ANDROID) {
                    // jsb.reflection.callStaticMethod("com/cocos/game/AppActivity", "JavaCopy", "(Ljava/lang/String;)V", text);
                    native.reflection.callStaticMethod("com/cocos/game/AppActivity", "sendEventAF", "(Ljava/lang/String;)V", text);
                } else if (sys.os == sys.OS.IOS) {
                    native.reflection.callStaticMethod("AppController", "sendEventAF:", text);
                } else {
                  
                }
            } catch (message) {
                //  log("Error Copy:", message);
            }
        }

    getAvatarSpriteFrame(avatar: string): SpriteFrame {
        let avatarInt = parseInt(avatar);

        if (
            isNaN(avatarInt) ||
            avatarInt < 0 ||
            avatarInt >= this.sprFrameAvatars.length
        ) {
            return this.sprFrameAvatars[0];
        }

        return this.sprFrameAvatars[avatarInt];
    }


    public getTextLang(key: string) {
        return LanguageManager.instance.getString(key);
    }

    showLoading(isShow: boolean, timeOut: number = 15) {
        if (!this.loadingNode || !this.loadingLabel) return;
        const lastChild = this.node.children[this.node.children.length - 1];
        this.loadingNode.setSiblingIndex(lastChild?.getSiblingIndex() + 1 || 0);

        this.loadingLabel.string = App.instance.getTextLang("IS_LOADING");

        if (this.timeOutLoading != null) {
            clearTimeout(this.timeOutLoading);
        }

        if (isShow) {
            if (timeOut > 0) {
                this.timeOutLoading = setTimeout(() => {
                    this.showLoading(false);
                }, timeOut * 1000);
            }
            this.loadingNode.active = true;
        } else {
            this.loadingNode.active = false;
        }
    }

    public ShowAlertDialog(mess: string) {
        this.alertDialog.showMsg(mess);
    }

    showErrLoading(msg?: string) {
        this.showLoading(true, 5);
        this.loadingLabel.string = msg ? msg : "Mất kết nối, đang thử lại...";
    }

    openGame(gameId: number) {
        switch (gameId) {
            case Config.InGameIds.TaiXiuMini:
            case Config.InGameIds.TaiXiuMD5:
            case Config.InGameIds.XoSo:
            case Config.InGameIds.LuckyWild:
            case Config.InGameIds.MiniPhucSinh:
            case Config.InGameIds.BauCua:
            case Config.InGameIds.HiLo:
            case Config.InGameIds.MiniPoker:
                break;
            default:
                Utils.setStorageValue("last_open_game_id", gameId.toString());
                break;
        }

        switch (gameId) {
            case Config.InGameIds.TaiXiuMini:
                this.actGameTaiXiuJackpot();
                break;
            case Config.InGameIds.TaiXiuLive:
                this.actGameTaiXiuJackpot(true);
                break;
            case Config.InGameIds.TaiXiuMD5:
                this.actGameTaiXiuMD5();
                break;
            case Config.InGameIds.TaiXiuMD5Live:
                this.actGameTaiXiuMD5(true);
                break;
            case Config.InGameIds.XoSo:
                this.actXoSoMiniGame();
                break;
            case Config.InGameIds.BlackJack:
            case Config.InGameIds.Baccarat:
            case Config.InGameIds.RongHo:
            case Config.InGameIds.Roullete:
            case Config.InGameIds.Sicbo:
            case Config.InGameIds.XocDia:
                this.actGoToCasino(gameId);
                break;
            case Config.InGameIds.Catte:
            case Config.InGameIds.TLMN:
            case Config.InGameIds.TLMNSolo:
            case Config.InGameIds.SamLoc:
            case Config.InGameIds.SamLocSolo:
            case Config.InGameIds.BaCay:
            case Config.InGameIds.Poker:
            case Config.InGameIds.MauBinh:
                this.actCardGame(gameId);
                break;
            case Config.InGameIds.XuatKich:
                this.actGameXuatKich();
                break;
            case Config.InGameIds.LongVuong:
                this.actGameLongVuong();
                break;
            case Config.InGameIds.PhiDoi:
                this.actGamePhiDoi();
                break;
            case Config.InGameIds.CaMap:
                this.actGameCaMap();
                break;
            case Config.InGameIds.CaKiem:
                this.actGameCaKiem();
                break;
            case Config.InGameIds.VirtualSports:
                this.actOpenVirtualSports();
                break;
            case Config.InGameIds.Kingdom:
                this.actGameVuongQuoc();
                break;
            case Config.InGameIds.ThuyCung:
                this.actGameThuyCung();
                break;
            case Config.InGameIds.RungVang:
                this.actGameRungVang();
                break;
            case Config.InGameIds.GaiNhay:
                this.actGameGaiNhay();
                break;
            case Config.InGameIds.ThanTai:
                this.actGameThanTai();
                break;
            case Config.InGameIds.VuTruong:
                this.actGameVuTruong();
                break;
            default:
                break;
        }
    }


    openPrefabGame(bundleName, sceneName, callback) {
        // this.showLoading(true, -1);
        // SignalRClient.closeHubMiniGameAll();

        BundleControl.loadPrefabGame(
            bundleName,
            sceneName,
            (finish, total) => {
                this.showLoading(true);
                // this.showLoadingProcess(true);
                // this.lblStatus.string = parseInt(String((finish / total) * 100)) + "%";
                // this.spriteProgress.fillRange = (finish / total);
                // this.nodeSlider.progress = this.spriteProgress.fillRange;
            },
            (prefab, bundle) => {
                this.showLoading(false);
                // this.showLoadingProcess(false);
                bundle.loadDir("res/prefabs", Prefab, (finish, total) => {

                    },
                    (err, arrPrefab) => {

                        callback(bundle, prefab);
                    }
                );
            }
        );
        // }
    }

    actVQMM() {
        Http.get(Configs.App.DOMAIN_CONFIG["VQMM_GetTurnUrl"], {type: 0}, (status, res) => {
            if (status === 200 && res.c == 0 && res.d > 0) {
                BundleControl.loadPrefabPopup("prefabs/PopupSpinWheel", (prefab: any) => {
                    let popup = instantiate(prefab).getComponent("PopupSpinWheel");
                    App.instance.popupNode.addChild(popup.node);
                    popup.show();
                });
            }
        });
    }

    addGameToNode(game: any) {
        let node = instantiate(game);
        this.bigGameNode.addChild(node);
    }

    actGameTaiXiuJackpot(isLive: boolean = false) {
        const showError = () => {
            App.instance.showLoading(false);
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    success ? resolve() : reject();
                });
            });
        };
        App.instance.showLoading(true);
        connectHub(MiniGameTX1SignalRClient.getInstance())
            .then(() => connectHub(MiniGameTX2SignalRClient.getInstance()))
            .then(() => connectHub(MiniGameTX3SignalRClient.getInstance()))
            .then(() => connectHub(MiniGameSignalRClient.getInstance()))
            .then(() => {
                App.instance.showLoading(false);

                if (isLive) {
                    BundleControl.loadPrefabGame("TaiXiuLive", "TaiXiuLive", (finish, total) => {
                            // @TODO with global loading
                            this.showLoading(true, -1);
                        },
                        (prefab) => {
                            this.showLoading(false);
                            this.bigGameNode.removeAllChildren();
                            let node = instantiate(prefab);
                            node.active = true;
                            node.removeComponent("TaiXiuLiveMD5.Controller");
                            this.bigGameNode.addChild(node);
                        }
                    );

                    return;
                }

                if (this.taiXiuJackpotMiniGame == null || this.taiXiuJackpotMiniGame.node == null) {
                    BundleControl.loadPrefabGame("TaiXiuDouble", "TaiXiuDouble", (finish, total) => {
                            // @TODO with global loading
                            this.showLoading(true, 0);
                        },
                        (prefab) => {
                            this.showLoading(false);
                            let node = instantiate(prefab);
                            node.parent = this.miniGameNode;
                            node.active = false;
                            this.taiXiuJackpotMiniGame = node.getComponent(MiniGame);
                            this.taiXiuJackpotMiniGame.show();
                        }
                    );
                } else {
                    if (this.taiXiuJackpotMiniGame.node.parent === null) {
                        this.taiXiuJackpotMiniGame.node.parent = this.miniGameNode;
                    }
                    this.taiXiuJackpotMiniGame.show();
                }
            })
            .catch(showError);
    }

    actGameTaiXiuMD5(isLive: boolean = false) {
        App.instance.showLoading(true);
        MiniGameTXMD5SignalRClient.getInstance().connectHub((success: Function) => {
            App.instance.showLoading(false);
            if (success) {
                if (isLive) {
                    BundleControl.loadPrefabGame("TaiXiuLive", "TaiXiuLive", (finish, total) => {
                            // @TODO with global loading
                            this.showLoading(true, -1);
                        },
                        (prefab) => {
                            this.showLoading(false);
                            this.bigGameNode.removeAllChildren();
                            let node = instantiate(prefab);
                            node.active = true;
                            node.removeComponent("TaiXiuLiveJP.Controller");
                            this.bigGameNode.addChild(node);
                        }
                    );
                    return;
                }

                if (this.taiXiuMD5MiniGame == null || this.taiXiuMD5MiniGame.node == null) {
                    BundleControl.loadPrefabGame("TaiXiuMD5", "TaiXiuMD5", (finish, total) => {
                            // @TODO with global loading
                            this.showLoading(true, -1);
                        },
                        (prefab) => {
                            this.showLoading(false);
                            let node = instantiate(prefab);
                            node.parent = this.miniGameNode;
                            node.active = false;
                            this.taiXiuMD5MiniGame = node.getComponent(MiniGame);
                            this.taiXiuMD5MiniGame.show();
                        }
                    );
                } else {
                    if (this.taiXiuMD5MiniGame.node.parent === null) {
                        this.taiXiuMD5MiniGame.node.parent = this.miniGameNode;
                    }
                    this.taiXiuMD5MiniGame.show();
                }
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actXoSoMiniGame() {
        BundleControl.loadPrefabPopup("prefabs/PopupXoSo", (prefab: any) => {
            let popup = instantiate(prefab).getComponent("PopupMiniGameXoSo");
            App.instance.popupNode.addChild(popup.node);
            popup.show();
        });
    }

    actOpenVirtualSports() {
        this.showLoading(true, -1);
        BundleControl.loadPrefabPopup("prefabs/SportVirtual/SportVirtual", (prefab: any) => {
            this.showLoading(false);
            let popup = instantiate(prefab).getComponent("SportVirtualController");
            App.instance.bigGameNode.addChild(popup.node);
        });
    }

    actCardGame(gameId: number) {
        this.nodeRoom.removeAllChildren();

        App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub(gameId, (success: any) => {
            App.instance.showLoading(false);
            if (success) {
                // App.instance.lblStatus.string = "100%";
                // App.instance.spriteProgress.fillRange = 1;
                App.instance.showLoading(true);
                BundleControl.loadPrefabPopup("prefabs/RoomCards", (prefab: any) => {
                    App.instance.showLoading(false);
                    const roomCard = instantiate(prefab).getComponent("RoomCards");
                    this.nodeRoom.addChild(roomCard.node);
                    roomCard.setDataRoom(gameId);
                });
            } else {
                // App.instance.showLoadingProcess(false);
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actGoToCasino(gameId: number) {
        // App.instance.showLoadingProcess(true);
        // App.instance.lblStatus.string = "0%";
        // App.instance.spriteProgress.fillRange = 0;

        var hub: SignalRClient;
        switch (gameId) {
            case Configs.InGameIds.Baccarat:
                hub = BaccaratSignalRClient.getInstance();
                break;
            case Configs.InGameIds.BlackJack:
                hub = BlackjackSignalRClient.getInstance();
                break;
            case Configs.InGameIds.RongHo:
                hub = DragonTigerSignalRClient.getInstance();
                break;
            case Configs.InGameIds.Roullete:
                hub = RouletteSignalRClient.getInstance();
                break;
            case Configs.InGameIds.Sicbo:
                hub = SicboSignalRClient.getInstance();
                break;
            case Configs.InGameIds.XocDia:
                hub = SedieSignalRClient.getInstance();
                break;
        }

        App.instance.showLoading(true);
        // @ts-ignore
        hub.connectHub((success: Function) => {
            App.instance.showLoading(false);
            if (success) {
                // App.instance.lblStatus.string = "100%";
                // App.instance.spriteProgress.fillRange = 1;

                setTimeout(() => {
                    // App.instance.showLoadingProcess(false);
                    this.actGoToCasinoGame(gameId);
                }, 500);
            } else {
                // App.instance.showLoadingProcess(false);
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    private actGoToCasinoGame(gameID: number) {
        this.nodeRoom.removeAllChildren();
        BundleControl.loadPrefabPopup("prefabs/Casino/Lobby", (prefab: any) => {
            const popupCasino = instantiate(prefab).getComponent("Casino.Lobby");
            this.nodeRoom.addChild(popupCasino.node);
            popupCasino.setDataRoom(gameID);
        });
    }

    private actGameXuatKich() {
        BundleControl.loadPrefabGame("XuatKich", "XuatKich", (finish, total) => {
                // this.showXuatKichLoadingProcess(true);
                // this.xuatKichSpriteProgress.fillRange = (finish / total);
                // this.xuatKichNodeSlider.progress = this.xuatKichSpriteProgress.fillRange;
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                // this.showXuatKichLoadingProcess(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGameLongVuong() {
        BundleControl.loadPrefabGame("LongVuong", "LongVuong", (finish, total) => {
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGamePhiDoi() {
        BundleControl.loadPrefabGame("SpaceWar", "prefabs/SpaceWar", (finish, total) => {
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGameCaMap() {
        BundleControl.loadPrefabGame("TheShark", "prefabs/TheShark", (finish, total) => {
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGameCaKiem() {
        BundleControl.loadPrefabGame("FishHunter", "prefabs/FishHunter", (finish, total) => {
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    actGameVuongQuoc() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(KingdomSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("Kingdom", "Kingdom", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actGameThuyCung() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(OceanSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("Ocean", "Ocean", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actGameGaiNhay() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(DQSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("DancingQueen", "DancingQueen", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    actGameThanTai() {
        // TODO
    }

    actGameVuTruong() {
        // TODO
    }

    actGameRungVang() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (
            client: { connectHub: (cb: (success: boolean) => void) => void }
        ): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(ForestSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame("Forest", "Forest", (finish, total) => {
                    // @TODO with global loading
                    this.showLoading(true, 0);
                },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);

    }


    public showToast(msg: string) {
        this.alertToast.active = true;
        this.alertToast.getComponent(Label).string = msg;

        Tween.stopAllByTarget(this.alertToast);
        tween(this.alertToast)
            .set({position: this.alertToast.position.set(this.alertToast.position.x, 0, 0)})
            .to(2.0, {position: this.alertToast.position.set(this.alertToast.position.x, 100, 0)}, {easing: easing.sineOut})
            .call(() => {
                this.alertToast.active = false;
            })
            .start();
    }

    public setActiveGameById(gameId: number, active: boolean) {
        const nodes = this.gameNodeMap.get(gameId);
        if (nodes) {
            nodes.forEach(node => node.active = active);
        }
    }

}

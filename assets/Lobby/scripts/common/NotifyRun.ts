import { _decorator, Component, Node, RichText, instantiate, v3, tween, NodePool, UITransform, view } from 'cc';
import eventBus from "db://assets/Lobby/scripts/common/EventBus";
import App from "./App";
import ChatHubSignalRClient from "db://assets/Lobby/scripts/common/networks/ChatHubSignalRClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const { ccclass, property } = _decorator;

@ccclass('NotifyRun')
export default class NotifyRun extends Component {
    @property(Node)
    listNotify: Node = null!;
    @property(Node)
    notify: Node = null!;

    private speed: number = 100;
    private gap: number = 100;
    private queueNodeNotify: Node[] = [];
    private pool: NodePool = new NodePool();
    private maxNotifies: number = 10;
    private initialized: boolean = false;

    onLoad() {
        for (let i = 0; i < this.maxNotifies; i++) {
            const item = instantiate(this.notify);
            this.pool.put(item);
        }

        this.listNotify.removeAllChildren();

        if (Configs.Login.IsLogin) {
            this.onLoginSuccess();
        } else {
            eventBus.on('LOGIN_SUCCESS', this.onLoginSuccess, this);
        }

        eventBus.on('LOGOUT', this.onLogout, this);
    }

    onEnable() {
        this.listNotify.removeAllChildren();
        this.queueNodeNotify = [];
        this.pool.clear();
        this.unscheduleAllCallbacks();

        if (this.initialized) {
            this.initHub();
        }
    }

    protected onDestroy(): void {
        eventBus.off('LOGIN_SUCCESS', this.onLoginSuccess, this);
        eventBus.off('LOGOUT', this.onLogout, this);
    }

    private onLogout(): void {
        this.initialized = false;
        eventBus.on('LOGIN_SUCCESS', this.onLoginSuccess, this);
        this.listNotify.removeAllChildren();
        this.queueNodeNotify = [];
        this.pool.clear();
        this.unscheduleAllCallbacks();
    }

    private onLoginSuccess(): void {
        if (this.initialized) return;

        this.initialized = true;
        this.initHub();
    }

    private initHub(): void {
        const textWin = App.instance.getTextLang("txt_win");

        ChatHubSignalRClient.getInstance().receiveNotify((data: any[]) => {
            data.forEach((item: any, index: number) => {
                this.scheduleOnce(() => {
                    const messages = item.Message.split(',');

                    if (messages.length < 4) {
                        this.addMessage(item.Message);
                        return;
                    }

                    const gameName = Configs.InGameIds.getGameName(item.GameID);
                    const message =
                        `<color=#fdad46><i>(${gameName})</i></c>` +
                        `<color=#31b6ce> ${messages[1]}</c>` +
                        `<color=#ffffff> ${textWin}</c>` +
                        `<color=#f1cc0e> ${Utils.formatNumber(parseInt(messages[2]))}</c>`;

                    this.addMessage(message);
                }, 0.1 * index);
            });
        });
    }

    private addMessage(message: string): void {
        if (!this.notify || !this.listNotify) return;
        if (this.queueNodeNotify.length >= this.maxNotifies) return;

        const notifyNode = this.pool.size() > 0 ? this.pool.get()! : instantiate(this.notify);
        const labelComp = notifyNode.getComponent(RichText);
        if (labelComp) labelComp.string = message;

        notifyNode.active = false;
        this.listNotify.addChild(notifyNode);

        this.scheduleOnce(() => {
            let startX = view.getVisibleSize().width / 2;
            if (this.queueNodeNotify.length > 0) {
                const last = this.queueNodeNotify[this.queueNodeNotify.length - 1];
                const lastRight = last.position.x + last.getComponent(UITransform)!.width;
                startX = Math.max(startX, lastRight + this.gap);
            }

            notifyNode.setPosition(startX, 0);
            notifyNode.active = true;

            this.queueNodeNotify.push(notifyNode);
            this.runMarquee(notifyNode);
        }, 0);
    }

    private runMarquee(notifyNode: Node): void {
        const width = notifyNode.getComponent(UITransform)!.width;
        const distance = notifyNode.position.x + width + view.getVisibleSize().width / 2;
        const duration = distance / this.speed;

        tween(notifyNode)
            .to(duration, { position: v3(-view.getVisibleSize().width / 2 - width, 0, 0) }, { easing: 'linear' })
            .call(() => {
                this.queueNodeNotify = this.queueNodeNotify.filter(n => n !== notifyNode);
                notifyNode.removeFromParent();
                notifyNode.setPosition(v3(0, 0, 0));
                this.pool.put(notifyNode);
            })
            .start();
    }
}
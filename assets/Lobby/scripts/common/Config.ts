import {LanguageManager} from "db://assets/Lobby/scripts/common/language/Language.LanguageManager";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

namespace Configs {
    export class Login {
        static AccountID: number = 0;
        static CurrencyID: number = 1;
        static Username: string = "";
        static Password: string = "";
        static Nickname: string = "";
        static Avatar: string = "";
        static CoinBalance: number = 0;
        static GoldBalance: number = 0;
        static IsLogin: boolean = false;
        static ConfirmStatus: number = 0;
        /**
         * 0: Chưa confirm gì <br/>
         * 1: Confirm Mobile <br/>
         * 2: Confirm Email <br/>
         * 3: Confirm Telesafe <br/>
         * 4: Confirm Email và Mobile <br/>
         * 5: confirm Telesafe và Mobile <br/>
         * 6: confirm Telesafe và Email <br/>
         * 7: confirm Telesafe Email và Mobile <br/>
         */
        static SecurityStatus: number = 0;
        static AccessToken: string = "";
        static SessionKey: string = "";
        static CreateTime: string = "";
        static Birthday: string = "";
        static IpAddress: string = "";
        static VipPoint: number = 0;
        static VipPointLevel: number = 0;
        static VipPointSave: number = 0;
        static Mail: string = "";
        static MobilePhone: string = "";
        static TeleSafe: string = "";
        static PortalID: number = 0;

        static clear() {
            this.AccountID = 0;
            this.CurrencyID = 1;
            this.Username = "";
            this.Password = "";
            this.Nickname = "";
            this.Avatar = "";
            this.CoinBalance = 0;
            this.GoldBalance = 0;
            this.IsLogin = false;
            this.ConfirmStatus = 0;
            this.SecurityStatus = 0;
            this.AccessToken = "";
            this.SessionKey = "";
            this.CreateTime = "";
            this.Birthday = "";
            this.IpAddress = "";
            this.VipPoint = 0;
            this.VipPointLevel = 0;
            this.VipPointSave = 0;
            this.Mail = "";
            this.MobilePhone = "";
            this.TeleSafe = "";
        }

        static set(data: any) {
            this.AccountID = data['accountID'];
            this.Nickname = data['nickname'];
            this.Username = data['username'];
            this.Avatar = data['avatar'];
            this.CoinBalance = data['coinBalance'];
            this.GoldBalance = data['goldBalance'];
            this.IpAddress = data['clientIP'];
            this.CreateTime = data['activatedTime'];
            this.Birthday = data['birthday'];
            this.VipPoint = data['vipPoint'];
            this.VipPointLevel = data['vipLevel'];
            this.ConfirmStatus = data['confirmStatus'];
            this.SecurityStatus = data['securityStatus'];
            this.CurrencyID = data['currencyID'];
            this.Mail = data['email'];
            this.MobilePhone = data['mobile'];
            this.TeleSafe = data['teleSafe'];
            this.PortalID = data['portalID'];
        }
    }

    export class SMSService {
        static FORGOT_PASSWORD: number = 11;
        static LOGIN_BY_OTP: number = 12;
        static CHANGE_PASSWORD: number = 13;
        static CONFIRM_PHONE: number = 14;
        static CHANGE_PHONE: number = 15;
        static ACTIVE_LOGIN_OTP: number = 16;
        static SAFE_BALANCE: number = 17;
        static MANAGE_GAME: number = 18;
        static BUY_CARD: number = 19;
        static CHECK_CARD: number = 20;
        static EXCHANGE_BALANCE: number = 21;
        static VERIFY_PHONE: number = 22;
    }

    export class OTPType {
        static AppOTP: number = 1;
        static SmsOTP: number = 2;
        static TeleSafe: number = 3;
        static Telegram: number = 4;
    }

    export class App {
        static G88_KEY_DECRYPT_CONFIG: string = "YeJqQu2yr1E4kWY1G1QpZyj0ZkPYJJSgNI5nUouA4Pw=";
        static G88_KEY_DECRYPT_CONFIG_APP: string = "StA/zuCaB9aqqrUQd85rtw8/3933wQa28zhSxCUMWTg=";
        static G88_DOMAIN_GET_CONFIG: string = "";
        static G88_BUNDLE_ID: string = "test.hot.cc";
        static G88_VERSION: string = "0.0.4";
        static DOMAIN_CONFIG: any = [];
        static G88_CONFIG: any = {};
        static GAME_AVAILABLE_IDS: any = [];

        static init() {
            if (Utils.isBrowser()) {
                App.G88_DOMAIN_GET_CONFIG = 'https://gameapi-alpha.bavenoth.com/api/v1/global/cfinfo';
            } else {
                App.G88_DOMAIN_GET_CONFIG = 'https://config-alpha.bavenoth.com/api/v1/app/info';
            }
        }
    }

    export class GameAvailableIds {
        static readonly None = 0;
        static readonly BaCay = 1;
        static readonly Poker = 13;
        static readonly TLMN = 7;
        static readonly XiTo = 5;
        static readonly MauBinh = 9;
        static readonly SamLoc = 15;
        static readonly TLMNSolo = 33;
        static readonly SamLocSolo = 35;
        static readonly BaiCao = 37;
        static readonly Lieng = 17;
        static readonly Catte = 57;
        static readonly Tala = 3;
        static readonly TalaSolo = 39;
        static readonly SlotTournament = 230;
        static readonly VQMM = 111;
        static readonly LuckyWild = 121;
        static readonly Lottery = 113;
        static readonly BanCa = 301;
        static readonly TieuLongNgu = 302;
        static readonly BanCaCooming = 991;
        static readonly Shark = 605;
        static readonly Sortie = 607;
        static readonly SpaceWar = 609;
        static readonly MultiLuckyDiceLive = 1001;
        static readonly LuckyDiceMd5Live = 1002;
        static readonly CrabfishLive = 1003;
        static readonly SedieLive = 1004;
        static readonly XocXoc = 25;
        static readonly SoDo = 401;
        static readonly Sicbo = 403;
        static readonly NewSicbo = 51;
        static readonly Roulette = 405;
        static readonly NewRoulette = 49;
        static readonly Baccarat = 43;
        static readonly Blackjack = 45;
        static readonly Sedie = 47;
        static readonly DragonTiger = 53;
        static readonly CrabFish = 55;
        static readonly PokerTournament = 501;
        static readonly TournamentGoOn = 503;
        static readonly TournamentSam = 505;
        static readonly TournamentOTT = 407;
        static readonly MutilSlot = 990
        static readonly Kingdom = 201;
        static readonly Olympia = 203;
        static readonly Dancing = 205;
        static readonly Ocean = 207;
        static readonly Forest = 211;
        static readonly GodOfFortune = 213;
        static readonly Disco = 215;
        static readonly PowerBall = 601;
        static readonly MegaMillions = 603;
        static readonly Keno = 611;
        static readonly SportLive = 701;
        static readonly SportVirtual = 703;
        static readonly ESport = 705;
        static readonly CockFighting = 707;
        static readonly FantasySport = 709;
        static readonly FantasySportSkill = 7090;
        static readonly TradingPro = 119;
        static readonly LuckyDiceMd5 = 123;
        static readonly ComingSoon = 999;
        static readonly MiniPoker = 101;
        static readonly TaiXiu = 103;
        static readonly HiLo = 105;
        static readonly BauCua = 107;
        static readonly PhucSinh = 109;
        static readonly OTT = 115;
        static readonly ABC = 1151;



        static isGameAvailable(gameId: number): boolean {
            const config = Configs.App.GAME_AVAILABLE_IDS.find((item: any) => item.GameId === gameId);
            return config?.IsAvaiable ?? false;
        }
    }

    export class InGameIds {
        static readonly None = 0;
        static readonly BaCay = 1;
        static readonly TaLa = 3;
        static readonly XiTo = 5;
        static readonly TLMN = 7;
        static readonly MauBinh = 9;
        static readonly TLMB = 11;
        static readonly Poker = 13;
        static readonly SamLoc = 15;
        static readonly Lieng = 17;
        static readonly Chan = 19;
        static readonly DanhGaBaCay = 21;
        static readonly DanhBienBaCay = 23;
        static readonly TLMNNhatAnTat = 27;
        static readonly CoTuong = 29;
        static readonly CoUp = 31;
        static readonly TLMNSolo = 33;
        static readonly SamLocSolo = 35;
        static readonly BaiCao = 37;
        static readonly BaiCaoBien = 39;
        static readonly TaLaSolo = 41;
        static readonly Baccarat = 43;
        static readonly BlackJack = 45;
        static readonly XocDia = 47;
        static readonly Roullete = 49;
        static readonly Sicbo = 51;
        static readonly RongHo = 53;
        static readonly Catte = 57;
        static readonly MiniPoker = 101;
        static readonly HiLo = 105;
        static readonly MiniPhucSinh = 107;
        static readonly BauCua = 109;
        static readonly LuckyWild = 111;
        static readonly OTT = 113;
        static readonly XoSo = 115;
        static readonly TaiXiuMini = 117;
        static readonly Trading = 119;
        static readonly Keno = 121;
        static readonly Kingdom = 201;
        static readonly SamTruyen = 203;
        static readonly GaiNhay = 205;
        static readonly ThuyCung = 207;
        static readonly DaoVang = 209;
        static readonly RungVang = 211;
        static readonly ThanTai = 213;
        static readonly VuTruong = 215;
        static readonly TourPoker = 401;
        static readonly TourTLMN = 403;
        static readonly TourSamLoc = 405;
        static readonly TourOTT = 407;
        static readonly Sports = 501;
        static readonly PowerBall = 503;
        static readonly MegaMillion = 504;
        static readonly VirtualSports = 505;
        static readonly ESports = 507;
        static readonly FantasySport = 509;
        static readonly CaKiem = 601;
        static readonly TLN = 603;
        static readonly CaMap = 605;
        static readonly XuatKich = 607;
        static readonly PhiDoi = 609;

        //Not true
        static readonly SoDo = 0;
        static readonly LongVuong = 1;
        static readonly ABC = 2;
        static readonly TaiXiuLive = 3;
        static readonly TaiXiuMD5 = 4;
        static readonly XocDiaLive = 5;
        static readonly TaiXiuMD5Live = 6;

        static getGameName(gameId: number): string {
            const idToLangKey: Record<number, string> = {
                [InGameIds.BaCay]: 'na1',
                [InGameIds.Poker]: 'na2',
                [InGameIds.TLMN]: 'na3',
                [InGameIds.SamLoc]: 'na4',
                [InGameIds.TLMNSolo]: 'na5',
                [InGameIds.SamLocSolo]: 'na6',
                [InGameIds.MauBinh]: 'na7',
                [InGameIds.Kingdom]: 'na8',
                [InGameIds.SamTruyen]: 'na9',
                [InGameIds.ThuyCung]: 'na10',
                [InGameIds.GaiNhay]: 'na11',
                [InGameIds.XocDia]: 'na13',
                [InGameIds.BlackJack]: 'na14',
                [InGameIds.Baccarat]: 'na15',
                [InGameIds.Roullete]: 'na16',
                [InGameIds.Sicbo]: 'na17',
                [InGameIds.RongHo]: 'na18',
                [InGameIds.CaKiem]: 'na19',
                [InGameIds.MiniPoker]: 'na22',
                [InGameIds.TaiXiuMini]: 'na21',
                [InGameIds.MiniPhucSinh]: 'na23',
                [InGameIds.BauCua]: 'na24',
                [InGameIds.HiLo]: 'na25',
                [InGameIds.OTT]: 'na26',
                [InGameIds.LuckyWild]: 'na27',
                [InGameIds.XoSo]: 'na28',
                [InGameIds.SoDo]: 'na29',
                [InGameIds.Catte]: 'na30'
            };

            const langKey = idToLangKey[gameId];
            if (langKey) {
                return LanguageManager.instance.getString(langKey);
            }

            return "Unknown";
        }

        static getChatChannelName(gameId: number): string {
            switch (gameId) {
                case InGameIds.MauBinh:
                    return "maubinh_lobby";
                case InGameIds.SamLoc:
                    return "loc_lobby";
                case InGameIds.TLMN:
                    return "tlmn_dl_lobby";
                case InGameIds.Poker:
                    return "poker_lobby";
                case InGameIds.TLMNSolo:
                    return "tlmn_dl_solo_lobby";
                case InGameIds.SamLocSolo:
                    return "loc_solo_lobby";
                case InGameIds.Catte:
                    return "catte_lobby";
                case InGameIds.RongHo:
                    return "dragontiger_lobby";
                case Configs.InGameIds.XocDia:
                    return "sedie_lobby";
                case Configs.InGameIds.BlackJack:
                    return "blackjack_lobby";
                case Configs.InGameIds.Baccarat:
                    return "baccarat_lobby";
                case Configs.InGameIds.Sicbo:
                    return "casinosicbo_lobby";
                case Configs.InGameIds.Roullete:
                    return "roulette_lobby";
            }
        }
    }
}
export default Configs;

Configs.App.init();
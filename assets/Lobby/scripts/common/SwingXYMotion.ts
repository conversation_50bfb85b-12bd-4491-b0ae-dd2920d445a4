import { _decorator, Component, Vec3, tween, CCInteger } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('SwingXYMotion')
export class SwingXYMotion extends Component {

    @property(CCInteger)
    amplitudeX: number = 10;

    @property(CCInteger)
    amplitudeY: number = 5;

    @property(CCInteger)
    duration: number = 1.2;

    @property(CCInteger)
    delayStart: number = 1.2;

    private _originalPos: Vec3 = new Vec3();

    start() {
        this._originalPos.set(this.node.position);

        this.scheduleOnce(() => {
            this.startSwing();
        }, this.delayStart);
    }

    startSwing() {
        const quarter = this.duration / 4;

        const rightUp = new Vec3(this._originalPos.x + this.amplitudeX, this._originalPos.y + this.amplitudeY, 0);
        const leftUp = new Vec3(this._originalPos.x - this.amplitudeX, this._originalPos.y + this.amplitudeY, 0);
        const leftDown = new Vec3(this._originalPos.x - this.amplitudeX, this._originalPos.y - this.amplitudeY, 0);
        const rightDown = new Vec3(this._originalPos.x + this.amplitudeX, this._originalPos.y - this.amplitudeY, 0);

        tween(this.node)
            .repeatForever(
                tween()
                    .to(quarter, { position: rightUp })
                    .to(quarter, { position: leftUp })
                    .to(quarter, { position: leftDown })
                    .to(quarter, { position: rightDown })
            )
            .start();
    }

    onDestroy() {
        tween(this.node).stop();
    }
}
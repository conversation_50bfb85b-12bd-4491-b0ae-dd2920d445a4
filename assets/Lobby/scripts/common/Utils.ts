import {
    assetManager,
    Component,
    native,
    Node,
    Sprite,
    SpriteFrame,
    sys,
    Texture2D,
    UIOpacity,
    UITransform,
    v2,
    v3,
    Vec2,
    ImageAsset,
    log
} from "cc";
import {SPUtils} from "db://assets/Lobby/scripts/common/SPUtils";
declare const JSEncrypt: any;
export class Utils {
    static Rad2Deg = 57.2957795;
    static Deg2Rad = 0.0174532925;

    static loadImgFromUrl(
        sprite: Sprite | null,
        url: string,
        parentScale: Node | null = null
    ) {
        if (!sprite || !url) return;
        assetManager.loadRemote(url, (err, asset: ImageAsset) => {
            if (err || !asset) return;
            sprite.spriteFrame = SpriteFrame.createWithImage(asset);

            if (parentScale) {
                const parentTransform = parentScale.getComponent(UITransform);
                const childTransform = sprite.getComponent(UITransform);
                if (!parentTransform || !childTransform) return;

                const scaleW = parentTransform.width / childTransform.width - 0.1;
                const scaleH = parentTransform.height / childTransform.height - 0.1;

                const scale = Math.max(0.5, Math.min(scaleW, scaleH));
                sprite.node.setScale(v3(scale, scale, scale));
            }
        });
    }

    static copy(text: string) {
        if (sys.isNative) {
            native?.copyTextToClipboard?.(text);
        } else {
            const el = document.createElement("textarea");
            el.value = text;
            document.body.appendChild(el);
            el.select();
            document.execCommand("copy");
            document.body.removeChild(el);
        }
    }

      static  formatString(template: string, ...args: any[]): string {
         return template.replace(/{(\d+)}/g, (match, index) => args[index]);
     }
    static shareNativeWithText(text: string) {
        if (sys.os === sys.OS.ANDROID) {
            native.reflection.callStaticMethod(
                "com/cocos/game/AppActivity",
                "createShareText",
                "(Ljava/lang/String;Ljava/lang/String;)V",
                text
            );
        } else if (sys.os === sys.OS.IOS) {
            native.reflection.callStaticMethod("AppDelegate", "createShareText:", text);
        }
    }

    static degreesToVec2(degrees: number): Vec2 {
        return this.radianToVec2(degrees * this.Deg2Rad);
    }

    static radianToVec2(radian: number): Vec2 {
        return v2(Math.cos(radian), Math.sin(radian));
    }

    static numberToEnum<T>(value: number, typeEnum: T): T[keyof T] | undefined {
        return typeEnum[typeEnum[value]];
    }

    static ToVND(num: number): string {
        const result = num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        return result === "NaN" ? "0" : result;
    }

    static ToInt(vnd: string): number {
        return parseInt(vnd.replace(/\./g, "")) || 0;
    }

    static IsJsonString(text: string): boolean {
        try {
            JSON.parse(text);
            return true;
        } catch {
            return false;
        }
    }

    static loadSpriteFrameFromBase64(base64: string, callback: (sprFrame: SpriteFrame) => void) {
        const img = new Image();
        const texture = new Texture2D();
        img.onload = function () {
            texture.reset({width: img.width, height: img.height});
            texture.uploadData(img, 0, 0);
            const sp = new SpriteFrame();
            sp.texture = texture;
            callback(sp);
        };
        img.src = "data:image/png;base64," + base64;
    }

    static formatNumberBank(n: string): string {
        return n.replace(/[^0-9]/g, "");
    }

    static formatEditBox(n: string): number {
        return parseInt(n.replace(/\./g, "")) || 0;
    }

    static formatNumber(n: number): string {
        return n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    static formatMoney(money: number, isForMatK = false) {
        let format = "";
        let mo = Math.abs(money);
        if (mo >= **********) {
            mo /= **********;
            format = "B";
        } else if (mo >= 1000000) {
            mo /= 1000000;
            format = "M";
        } else if (mo >= 1000 && isForMatK) {
            mo /= 1000;
            format = "K";
        } else {
            return this.formatNumber(money);
        }

        let str = Math.abs(money).toString();
        let str1 = Math.floor(mo).toString();

        let strResult = str[str1.length] + str[str1.length + 1]
        if (strResult === '00') {
            return (money < 0 ? "-" : "") + Math.floor(mo) + format;
        } else {
            if (strResult[1] === '0') {
                return (money < 0 ? "-" : "") + Math.floor(mo) + "." + strResult[0] + format;
            } else
                return (money < 0 ? "-" : "") + Math.floor(mo) + "." + strResult + format;
        }
    }

    static formatMoneyOnlyK(money: number): string {
        if (money < 1000) return money.toString();
        let mo = Math.floor(money / 100) / 10;
        return this.formatNumber(mo) + "K";
    }

    static removeDot(val: string): number {
        return parseInt(val.replace(/[,+]/g, "")) || 0;
    }

    static formatNumberMin(value: number): string | number {
        if (value < 1000) return value;
        const suffixes = ["", "K", "M", "B", "T"];
        let suffixNum = Math.floor(("" + value).length / 3);
        let shortValue = value / Math.pow(1000, suffixNum);

        for (let precision = 2; precision >= 1; precision--) {
            shortValue = parseFloat(shortValue.toPrecision(precision));
            if ((shortValue + "").replace(/[^a-zA-Z0-9]+/g, "").length <= 2) break;
        }
        return shortValue + suffixes[suffixNum];
    }

    static stringToInt(s: string): number {
        const n = parseInt(s.replace(/[.,]/g, ""));
        return isNaN(n) ? 0 : n;
    }

    static randomRangeInt(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min)) + min;
    }

    static randomRange(min: number, max: number): number {
        return Math.random() * (max - min) + min;
    }

    static v2Distance(v1: Vec2, v2: Vec2): number {
        return Math.sqrt(Math.pow(v2.x - v1.x, 2) + Math.pow(v2.y - v1.y, 2));
    }

    static v2Degrees(v1: Vec2, v2: Vec2): number {
        return Math.atan2(v2.y - v1.y, v2.x - v1.x) * 180 / Math.PI;
    }

    static dateToYYYYMMdd(date: Date) {
        const mm = date.getMonth() + 1;
        const dd = date.getDate();

        return [
            date.getFullYear(),
            (mm > 9 ? '' : '0') + mm,
            (dd > 9 ? '' : '0') + dd
        ].join('-');
    }

    static dateToYYYYMM(date: Date) {
        const mm = date.getMonth() + 1;

        return [
            date.getFullYear(),
            (mm > 9 ? '' : '0') + mm
        ].join('-');
    }

    static removeDuplicate<T>(array: T[]): T[] {
        return [...new Set(array)];
    }

    static setOpacity(node: Node, opacity: number) {
        const comp = this.addOrGetComponent(node, UIOpacity);
        comp.opacity = opacity;
    }

    static getOrAddOpacity(node: Node): UIOpacity {
        return this.addOrGetComponent(node, UIOpacity);
    }

    static replaceAt(str: string, index: number, replacement: string): string {
        return str.substring(0, index) + replacement + str.substring(index + replacement.length);
    }

    static notEmpty<T>(value: T | null | undefined): value is T {
        return value !== null && value !== undefined;
    }

    static getOs(): string {
        if (sys.os === sys.OS.ANDROID) return sys.isBrowser ? "web" : "and";
        if (sys.os === sys.OS.IOS) return sys.isBrowser ? "web" : "ios";
        return "web";
    }

    static convertTime(dateFormat: string | number | Date): string {
        const date = new Date(dateFormat);
        const pad = (v: number) => (v < 10 ? "0" + v : v);
        return `${pad(date.getDate())}/${pad(date.getMonth() + 1)}/${date.getFullYear()} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
    }

    static checkOnlyLettersAndNumbers(str: string): boolean {
        return /^[a-zA-Z0-9.,*$!@#%&\-+_=~^]+$/.test(str);
    }

    static addOrGetComponent<T extends Component>(node: Node, componentType: { new(): T }): T {
        return node.getComponent(componentType) || node.addComponent(componentType);
    }

    static getStorageKey(key: string): string | null {
        return sys.localStorage.getItem(key);
    }

    static setStorageValue(key: string, value: string): void {
        sys.localStorage.setItem(key, value);
    }

    static removeStorageKey(key: string): void {
        sys.localStorage.removeItem(key);
    }

    static getClientToken() {
        return this.getStorageKey('client-token') || '';
    }

    static getTicks() {
        const ticksPerSecond = 10000000;
        const unixEpochStartTicks = 621355968000000000;
        const now = Date.now();
        const gmt7OffsetMilliseconds = 7 * 60 * 60 * 1000;
        const adjustedNow = now + gmt7OffsetMilliseconds;
        const currentTicks = unixEpochStartTicks + adjustedNow * (ticksPerSecond / 1000);
        // @ts-ignore
        return BigInt(currentTicks).toString();
    }

    static getDeviceId() {
        if (sys.isBrowser) {
            let deviceID: string;
            if (localStorage.getItem("deviceID")) {
                deviceID = localStorage.getItem("deviceID");
            } else {
                deviceID = 'web-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                localStorage.setItem("deviceID", deviceID);
            }

            return deviceID;
        }

        let deviceID: any;
        if (sys.os === sys.OS.ANDROID) {
            deviceID = native.reflection.callStaticMethod("com/cocos/game/AppActivity", "getDeviceId2", "()Ljava/lang/String;");
        }

        if (sys.os === sys.OS.IOS) {
            deviceID = native.reflection.callStaticMethod("AppController", "getDeviceId:andParams:", "", "");
        }

        return deviceID;
    }

    static getBundle() {
        let deviceID = "";
        if (sys.os == sys.OS.ANDROID) {
            deviceID = native.reflection.callStaticMethod("com/cocos/game/AppActivity", "getBundle", "()Ljava/lang/String;");
        }

        if (sys.os == sys.OS.IOS) {
            deviceID = native.reflection.callStaticMethod("AppController", "getBundle:andParams:", "", "");
        }

        return deviceID;
    }

    static isBrowser() {
        return sys.isBrowser;
    }

    static encryptWithRSA(plaintext: string): string {
        let publicKeyPem = SPUtils.getRSAPublicKey()
        let encrypt = new JSEncrypt();
        encrypt.setPublicKey(publicKeyPem);
        return encrypt.encrypt(plaintext);
        // const publicKeyPem = SPUtils.getRSAPublicKey();
        // const publicKey = window.forge.pki.publicKeyFromPem(publicKeyPem);
        // const encryptedBytes = publicKey.encrypt(plaintext);

        // return window.forge.util.encode64(encryptedBytes)
    }

    static Log(...data: unknown[]) {
        log(data);
    }

    static formatDatetime88(inputString:string): string{
            const date = new Date(inputString);
        
            // Step 2: Extract day, month, and year
            const day = date.getDate().toString().padStart(2, '0'); // Ensures two digits for the day
            const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Month is zero-based
            const year = date.getFullYear();
            
            // Step 3: Format the date into "DD/MM/YYYY"
            const formattedDate = `${day}/${month}/${year}`;
            return formattedDate;
    }

    static formatDatetime(inputString: string, format: string): string {
        const date = new Date(inputString);

        const pad = (num: number): string => (num < 10 ? '0' + num : num.toString());

        const replacements: Record<string, string> = {
            "dd": pad(date.getDate()),
            "MM": pad(date.getMonth() + 1),
            "yyyy": date.getFullYear().toString(),
            "HH": pad(date.getHours()),
            "mm": pad(date.getMinutes()),
            "ss": pad(date.getSeconds()),
        };

        return format.replace(/dd|MM|yyyy|HH|mm|ss/g, match => replacements[match]);
    }

    static formatNameBank(n: string): string {
        var name = n.toLowerCase();
        var arr = {
            "àáảãạăắằẳẵặâấầẩẫậ": "a",
            "óòỏõọơớờởỡợôốồổỗộ": "o",
            "éèẻẽẹêếềểễệ": "e",
            "úùủũụưứừửữự": "u",
            "íìỉĩị": "i",
            "ýỳỷỹỵ": "y",
            "đ": "d",
            "~!@#$%^&*()_+`[]\{}|;':*-+\",./<>?**********": "_"
        };
        for (var i = 0; i < name.length; i++) {
            for (var key in arr) {
                for (var j = 0; j < key.length; j++) {
                    if (name[i] == key[j]) {
                        name = name.replace(name[i], arr[key]);
                    }
                }
            }
        }
        name = name.replace(/_/g, '');
        // name = name.replace(/[^a-zA-Z ]/g, "");
        name = name.toUpperCase();
        return name;
    }

    static formatName(n: string): string {
        var name = n;
        var arr = {
            "àáảãạăắằẳẵặâấầẩẫậ": "a",
            "óòỏõọơớờởỡợôốồổỗộ": "o",
            "éèẻẽẹêếềểễệ": "e",
            "úùủũụưứừửữự": "u",
            "íìỉĩị": "i",
            "ýỳỷỹỵ": "y",
            "đ": "d",
        };
        for (var i = 0; i < name.length; i++) {
            for (var key in arr) {
                for (var j = 0; j < key.length; j++) {
                    if (name[i] == key[j]) {
                        name = name.replace(name[i], arr[key]);
                    }
                }
            }
        }
        name = name.trim();
        return name;
    }
}

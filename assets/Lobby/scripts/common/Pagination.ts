import { _decorator, Button, Component, instantiate, Label, Node, Prefab, Sprite, Sprite<PERSON>rame } from "cc";

const { ccclass, property } = _decorator;

@ccclass
export default class PaginationUI extends Component {
    @property(Button)
    backButton: Button = null;

    @property(Button)
    nextButton: Button = null;

    @property(Node)
    pageButtonsContainer: Node = null;

    @property(Prefab)
    pageButtonPrefab: Prefab = null;

    @property(SpriteFrame)
    activePageButtonSprite: SpriteFrame = null;

    @property(SpriteFrame)
    inactivePageButtonSprite: SpriteFrame = null;

    private loadPageCallback: (page: number) => void;
    private currentPage: number = 1;
    private totalPages: number = 1;

    onLoad() {
        this.backButton.node.on('click', this.onBackClicked, this);
        this.nextButton.node.on('click', this.onNextClicked, this);
    }

    initListener(loadPageCallback: (page: number) => void) {
        this.loadPageCallback = loadPageCallback;
    }

    private onBackClicked(){
        if (this.currentPage > 1) {
            this.loadPageCallback(this.currentPage - 1);
        }
    }

    private onNextClicked(){
        if (this.currentPage < this.totalPages) {
            this.loadPageCallback(this.currentPage + 1);
        }
    }

    updatePagination(totalPages: number, currentPage: number) {
        this.totalPages = totalPages;
        this.currentPage = currentPage;
        this.updatePaginationUI();
    }

    private updatePaginationUI() {
        const visiblePages = 5;
        this.pageButtonsContainer.removeAllChildren();

        let startPage: number, endPage: number;
        
        if (this.totalPages <= visiblePages) {
            startPage = 1;
            endPage = this.totalPages;
        } else {
            const maxPagesBeforeCurrent = Math.floor(visiblePages / 2);
            const maxPagesAfterCurrent = Math.ceil(visiblePages / 2) - 1;
            
            if (this.currentPage <= maxPagesBeforeCurrent) {
                startPage = 1;
                endPage = visiblePages;
            } else if (this.currentPage + maxPagesAfterCurrent >= this.totalPages) {
                startPage = this.totalPages - visiblePages + 1;
                endPage = this.totalPages;
            } else {
                startPage = this.currentPage - maxPagesBeforeCurrent;
                endPage = this.currentPage + maxPagesAfterCurrent;
            }
        }

        // Create page buttons
        for (let i = startPage; i <= endPage; i++) {
            const pageButtonNode = instantiate(this.pageButtonPrefab);
            const pageButton = pageButtonNode.getComponent(Button);
            const label = pageButtonNode.getComponentInChildren(Label);
            
            label.string = i.toString();
            
            if (i === this.currentPage) {
                pageButton.interactable = false;
                pageButtonNode.getComponentInChildren(Sprite).spriteFrame = this.activePageButtonSprite;
            }
            else {
                pageButtonNode.getComponentInChildren(Sprite).spriteFrame = this.inactivePageButtonSprite;
            }
            
            pageButton.node.on('click', () => this.loadPageCallback(i));
            this.pageButtonsContainer.addChild(pageButtonNode);
        }

        this.backButton.node.active = this.currentPage > 1;
        this.nextButton.node.active = this.currentPage < this.totalPages;
    }

}
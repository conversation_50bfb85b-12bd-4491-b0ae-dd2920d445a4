import { _decorator, Label, RichText } from 'cc';
import Dialog from './Dialog';
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const { ccclass, property } = _decorator;

@ccclass('ConfirmDialog')
export default class ConfirmDialog extends Dialog {
    @property(Label)
    lblMessage: Label = null!;

    private onDismissed: ((isConfirmed: boolean) => void) | null = null;
    private isConfirmed: boolean = false;
    isClickdConfirm: boolean = false;


    showMsg(
        msg: string,
        onDismissed?: (isConfirmed: boolean) => void
    ): void {
        this.isConfirmed = false;
        this.onDismissed = onDismissed ?? null;
        this.lblMessage!.string = msg;

        super.show();
    }


    show3(msg: string, confirmTitle: string, onDismissed: (isConfirm: boolean)=>void){
        this.show4(msg, null, confirmTitle, onDismissed);
    }

    show4(msg: string, doneTitle?: string, confirmTitle?: string, onDismissed?: (isConfirm: boolean)=>void) : void {
        this.isClickdConfirm = false;
        // this.lblDone.string = !doneTitle ? "Hủy" : doneTitle;
        // this.lblConfirm.string = !confirmTitle ? "Đồng ý" : confirmTitle;
        this.onDismissed = onDismissed;
        this.lblMessage.string = msg;
        super.show()
    }

    actConfirm(): void {
        this.isConfirmed = true;
        this.dismiss();
    }

    _onDismissed(): void {
        super._onDismissed();
        this.onDismissed?.(this.isConfirmed);
    }
}
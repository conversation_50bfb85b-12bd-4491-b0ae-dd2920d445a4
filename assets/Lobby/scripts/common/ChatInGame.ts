import {_decorator, Component, Node, EditBox, instantiate, Label, Sprite, log} from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import RouletteSignalRClient from "db://assets/Lobby/scripts/common/networks/RouletteSignalRClient";
import BlackjackSignalRClient from "db://assets/Lobby/scripts/common/networks/BlackjackSignalRClient";
import SedieSignalRClient from "db://assets/Lobby/scripts/common/networks/SedieSignalRClient";
import BaccaratSignalRClient from "db://assets/Lobby/scripts/common/networks/BaccaratSignalRClient";
import DragonTigerSignalRClient from "db://assets/Lobby/scripts/common/networks/DragonTigerSignalRClient";
import SicboSignalRClient from "db://assets/Lobby/scripts/common/networks/SicboSignalRClient";
import CardGameSignalRClient from "db://assets/Lobby/scripts/common/networks/CardGameSignalRClient";

const {ccclass, property} = _decorator;

@ccclass
export default class ChatInGame extends Component {

    @property(Node)
    itemTemplate: Node = null;
    @property(EditBox)
    edbAmount: EditBox = null;
    dictChatQuick = {};
    @property(Node)
    noteText: Node = null;
    @property(Node)
    noteIcon: Node = null;
    @property(Node)
    emotionTemplate: Node = null;
    @property(Node)
    emotionContainer: Node = null;

    gameID = 7;

    protected onLoad(): void {
        this.edbAmount.node.on('return', this.sendMessage, this);
        this.noteText.active = false;
        this.noteIcon.active = true;
        /// DANH SACH CHAT KHAI BAO O DUOI COPY LEN DANG VI DU TLMN
        this.dictChatQuick = {
            "7": [
                "Hellloooooo",
                "Quit...",
                "Thank you vinamilk",
                "Sorry baby",
                App.instance.getTextLang("chat1"),
                App.instance.getTextLang("chat20"),//"Thối heo rồi :(",
                App.instance.getTextLang("chat21"),//"Cóng đê",
                App.instance.getTextLang("chat15"),//"Quất thôi, sao phải xoắn",
                App.instance.getTextLang("chat16"),//"Hết xiền rùi",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                App.instance.getTextLang("chat18"),//"Quá nhọ",
                App.instance.getTextLang("chat2"),//"Bài chán vãi",
                "hihi!",
                "Hazzz!"
            ],
            "1": [
                "Helllooo",
                "Quit...",
                "Thank you",
                "Sorry baby",
                App.instance.getTextLang("chat1"), // Nhanh lên ông ơi
                App.instance.getTextLang("chat2"),// "Bài chán vãi",
                App.instance.getTextLang("chat3"),//"Quá ngon, quá nguy hiểm",
                App.instance.getTextLang("chat4"),//"Đánh biên đê",
                App.instance.getTextLang("chat5"),//"Góp gà ăn to nào",
                App.instance.getTextLang("chat6"),//"10 Át cụ",
                App.instance.getTextLang("chat7"),//"1 Tịt",
                App.instance.getTextLang("chat8"),//"5 Nửa đời",
                App.instance.getTextLang("chat9"),//"Phát lương",
                App.instance.getTextLang("chat10"),//"Cả làng sang tiền"
            ],// 3 cay khai bao 
            "13": [
                "Hellloooooo",
                "Quit...",
                "Thank you vinamilk",
                "Sorry baby",
                App.instance.getTextLang("chat1"),//"Nhanh lên ông ơi",
                App.instance.getTextLang("chat13"),//"Xoắn gì, tố đi",
                App.instance.getTextLang("chat14"),//"Bao nhiêu cũng chơi",
                App.instance.getTextLang("chat15"),//"Quất thôi, sao phải xoắn",
                App.instance.getTextLang("chat16"),//"Hết xiền rùi",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                App.instance.getTextLang("chat18"),//"Quá nhọ",
                App.instance.getTextLang("chat19"),//"Thêm tiền đi",
                "hihi!",
                "Hazzz!"
            ],// poker
            "15": [
                "Hellloooooo",
                "Quit...",
                "Thank you vinamilk",
                "Sorry baby",
                App.instance.getTextLang("chat1"),//"Nhanh lên ông ơi",
                App.instance.getTextLang("chat22"),//"Thắng cả làng",
                App.instance.getTextLang("chat21"),//"Cóng đê",
                App.instance.getTextLang("chat23"),//"Tứ quý đây",
                App.instance.getTextLang("chat16"),//"Hết xiền rùi",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                App.instance.getTextLang("chat18"),//"Quá nhọ",
                App.instance.getTextLang("chat2"),//"Bài chán vãi",
                "hihi!",
                "Hazzz!"
            ], //SAM
            "9": [
                "Hellloooooo",
                App.instance.getTextLang("chat24"),//"Cứ từ từ!",
                App.instance.getTextLang("chat18"),//"Quá nhọ",
                App.instance.getTextLang("chat25"),//"Thắng vào mắt",
                App.instance.getTextLang("chat1"),//"Nhanh lên ông ơi",
                App.instance.getTextLang("chat26"),//"Sập dập mặt!",
                App.instance.getTextLang("chat27"),//"Đời quá đen",
                App.instance.getTextLang("chat28"),//"Làm giàu hơi khó",
                App.instance.getTextLang("chat16"),//"Hết xiền rùi",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                "Thank youuu",
                "OMG!",
                App.instance.getTextLang("chat29"),//"Rồng cuốn cmnr!",
                "hihi!",
                "Hazzz!"
            ], // MAU BINH
            "57": [
                "Helllooo",
                "hihi!",
                "Haizzz!",
                App.instance.getTextLang("chat73"),
                App.instance.getTextLang("chat74"),
                App.instance.getTextLang("chat75"),
                App.instance.getTextLang("chat76"),
                App.instance.getTextLang("chat77"),
                App.instance.getTextLang("chat78"),
                App.instance.getTextLang("chat79")
            ], // CATTE
            "45": [
                App.instance.getTextLang("chat37"),//"Thánh Blackjack đến đây!",
                App.instance.getTextLang("chat38"),//"Bài vi diệu vãi!",
                App.instance.getTextLang("chat39"),//"Củ đậu rau má thằng chia bài",
                App.instance.getTextLang("chat33"),//"Thôi xong rồi T_T",
                App.instance.getTextLang("chat40"),//"Khổ quá, sướng không chịu nổi",
                App.instance.getTextLang("chat41"),//"Thêm bài đi còn chờ gì nữa",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                App.instance.getTextLang("chat42"),//"Đừng có thêm nữa, nín thở bóp cò đi",
                "OMG!",
                "Quit!",
                "hihi!",
                "Hazzz!"
            ],
            "49": [
                App.instance.getTextLang("chat43"),//"Thánh Roulette đến đây!",
                App.instance.getTextLang("chat44"),//"Cò quay vi diệu vãi!",
                App.instance.getTextLang("chat45"),//"Đen thôi, đỏ quên đi!",
                App.instance.getTextLang("chat46"),//"Quay đều quay đều",
                App.instance.getTextLang("chat47"),//"Game này hay vãi",
                App.instance.getTextLang("chat33"),//"Thôi xong rồi T_T",
                App.instance.getTextLang("chat40"),//"Khổ quá, sướng không chịu nổi",
                App.instance.getTextLang("chat48"),//"Đặt cửa anh em ơi",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                "I'm quit!",
                "OMG!",
                "Bye bye!",
                "hihi!",
                "Hazzz!"
                // 
            ],
            "53": [
                "Helllooo",
                App.instance.getTextLang("chat48"),//"Đặt cửa anh em ơi!",
                App.instance.getTextLang("chat49"),//"1000% Rồng!",
                App.instance.getTextLang("chat50"),//"1000% Hổ!",
                App.instance.getTextLang("chat51"),//"Gẫy cầu rồi...",
                App.instance.getTextLang("chat33"),//"Thôi xong rồi T_T",
                App.instance.getTextLang("chat45"),//"Đen thôi! Đỏ quên đi",
                "Bye bye!!!!!!",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                "OMG!",
                "Quit!",
                "hihi!",
                "Hazzz!"
            ],
            "43": [
                "Helllooo",
                App.instance.getTextLang("chat48"),//"Đặt cửa anh em ơi!",
                App.instance.getTextLang("chat63"),//"Điêu vãi!",
                App.instance.getTextLang("chat39"),//"Củ đậu rau má thằng chia bài",
                App.instance.getTextLang("chat64"),//"Player, Banker gì cũng chén hết!",
                App.instance.getTextLang("chat65"),//"Thà cược nhầm còn hơn cược sót",
                "Bye bye, I'm quit!",
                App.instance.getTextLang("chat40"),//"Khổ quá, sướng không chịu nổi",
            ],
            "47": [
                "Helllooo",
                App.instance.getTextLang("chat18"),//"Quá nhọ",
                App.instance.getTextLang("chat52"),//"Chẵn nè!",
                App.instance.getTextLang("chat53"),//"Lẻ rồi!",
                App.instance.getTextLang("chat54"),//"Tứ đỏ nhé anh em!",
                App.instance.getTextLang("chat55"),//"Chẵn hay Lẻ đây?",
                App.instance.getTextLang("chat56"),//"Tất tay Lẻ nào!",
                App.instance.getTextLang("chat57"),//"Tất tay Chẵn đi!",
                App.instance.getTextLang("chat1"),//"Nhanh lên ông ơi",
                App.instance.getTextLang("chat58"),//"1000% Lẻ",
                App.instance.getTextLang("chat17"),//"Hên vãi",
                App.instance.getTextLang("chat59"),//"Đánh hay đấy!",
                App.instance.getTextLang("chat60"),//"Tứ xanh!",
                App.instance.getTextLang("chat61"),//"1000% Chẵn",
                App.instance.getTextLang("chat62"),//"Đánh to vào anh em ơi!"
            ],
            "51": [
                "Helllooo",
                "hihi!",
                "Haizzz!",
                App.instance.getTextLang("chat67"),
                App.instance.getTextLang("chat68"),
                App.instance.getTextLang("chat69"),
                App.instance.getTextLang("chat70"),
                App.instance.getTextLang("chat71"),
                App.instance.getTextLang("chat33"),
                App.instance.getTextLang("chat17"),
                App.instance.getTextLang("chat45"),
            ]
        };
        this.emotionContainer.removeAllChildren();
    }

    start() {
        for (let index = 0; index < this.dictChatQuick[this.gameID.toString()].length; index++) {
            const element = this.dictChatQuick[this.gameID.toString()][index];

            let item = instantiate(this.itemTemplate);
            item.active = true;
            item.parent = this.itemTemplate.parent;
            item.children[0].getComponent(Label).string = element;
            item.on("click", () => {
                this.hiddenBoxChat();
                this.sendSignal(element);
            });
        }

        var listEmotion = App.instance.listEmotionSpr;
        for (let index = 0; index < listEmotion.length; index++) {
            const element = listEmotion[index];
            let item = instantiate(this.emotionTemplate);
            item.active = true;
            item.parent = this.emotionContainer;
            item.getComponent(Sprite).spriteFrame = element;
            item.on("click", () => {
                this.sendEmotion('E__' + index);
            });
        }
    }

    sendSignal(content) {
        switch (this.gameID) {
            case Configs.InGameIds.TLMN:
            case Configs.InGameIds.TLMNSolo:
            case Configs.InGameIds.SamLoc:
            case Configs.InGameIds.SamLocSolo:
            case Configs.InGameIds.Poker:
            case Configs.InGameIds.MauBinh:
            case Configs.InGameIds.BaCay:
            case Configs.InGameIds.Catte:
                CardGameSignalRClient.getInstance().send('SendMessage', [content], (_data) => {

                });
                break;
            case Configs.InGameIds.Sicbo:
                SicboSignalRClient.getInstance().send('SendMessage', [content], (_data) => {
                });
                break;
            case Configs.InGameIds.RongHo:
                DragonTigerSignalRClient.getInstance().send('SendMessage', [content], (_data) => {
                });
                break;
            case Configs.InGameIds.Baccarat:
                BaccaratSignalRClient.getInstance().send('SendMessage', [content], (_data) => {
                });
                break;
            case Configs.InGameIds.XocDia:
                SedieSignalRClient.getInstance().send('SendMessage', [content], (_data) => {
                });
                break;
            case Configs.InGameIds.BlackJack:
                BlackjackSignalRClient.getInstance().send('SendMessage', [content], (_data) => {
                });
                break;
            case Configs.InGameIds.Roullete:
                RouletteSignalRClient.getInstance().send('SendMessage', [content], (_data) => {
                });
                break;
            default:
                break;
        }

    }

    show(gameID: any) {
        this.gameID = gameID;
        if (gameID == Configs.InGameIds.TLMNSolo) {
            this.gameID = Configs.InGameIds.TLMN;
        }
        if (gameID == Configs.InGameIds.SamLocSolo) {
            this.gameID = Configs.InGameIds.SamLoc;
        }
        if (gameID == Configs.InGameIds.Catte) {
            this.gameID = Configs.InGameIds.Catte;
        }
        this.node.active = true;
    }

    actIcon() {
        this.noteIcon.active = !this.noteIcon.active;
        this.noteText.active = !this.noteIcon.active;
    }

    hiddenBoxChat() {
        App.instance.inactivityTimer = 0;
        this.node.active = false;
    }

    sendEmotion(data: string) {
        this.hiddenBoxChat();
        this.sendSignal(data);
    }

    sendMessage() {
        this.hiddenBoxChat();
        var content = this.edbAmount.string;
        if (content == "") {
            return;
        }
        this.edbAmount.string = "";
        this.sendSignal(content);
    }
}
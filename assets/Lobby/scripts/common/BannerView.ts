import { _decorator, Component, PageView, instantiate, Node, Sprite, Button, sys } from 'cc';
import { Utils } from 'db://assets/Lobby/scripts/common/Utils';

const { ccclass, property } = _decorator;

@ccclass('BannerView')
export default class BannerView extends Component {
    static instance: BannerView | null = null;

    @property(PageView)
    pageView: PageView | null = null;

    @property(Node)
    bannerTemplate: Node | null = null;

    private currentIndex: number = 0;
    private totalPages: number = 0;
    private pageTimeouts: number[] = [];
    private autoScrollScheduled = false;

    onLoad(): void {
        BannerView.instance = this;
        this.pageView?.content.removeAllChildren();
    }

    showDetail(banners: any[]): void {
        if (!this.pageView || !this.bannerTemplate) return;

        this.pageTimeouts = [];
        this.currentIndex = 0;

        banners.forEach((item) => {
            if (!item.Release) return;

            const bannerNode = instantiate(this.bannerTemplate);
            bannerNode.active = true;
            this.pageView.addPage(bannerNode);

            const sprite = bannerNode.getComponent(Sprite);
            Utils.loadImgFromUrl(sprite, item.ImgName, bannerNode);

            const button = bannerNode.getComponent(Button);
            if (button) {
                button.node.on('click', () => sys.openURL(item.EventUrl), this);
            }

            this.pageTimeouts.push(item.TimeOut ?? 2);
        });

        this.totalPages = this.pageView.getPages().length;

        if (this.totalPages > 1) {
            this.scheduleNextScroll();
        }
    }

    scheduleNextScroll(): void {
        if (this.autoScrollScheduled || !this.pageView) return;

        this.autoScrollScheduled = true;

        const timeout = this.pageTimeouts[this.currentIndex] ?? 2;

        this.scheduleOnce(() => {
            this.currentIndex = (this.currentIndex + 1) % this.totalPages;
            this.pageView!.scrollToPage(this.currentIndex, 0.3);

            this.autoScrollScheduled = false;
            this.scheduleNextScroll();
        }, timeout);
    }

    onDisable(): void {
        this.unscheduleAllCallbacks();
        this.autoScrollScheduled = false;
    }
}
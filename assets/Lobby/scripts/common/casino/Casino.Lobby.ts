import { _decorator, Component, Label, Node, EditBox, Prefab, instantiate, RichText, UIOpacity, Toggle } from 'cc';
import SignalRClient from "db://assets/Lobby/scripts/common/networks/Network.SignalRClient";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import CasinoPopupRank from "db://assets/Lobby/scripts/common/casino/Casino.PopupRank";
import Configs from "db://assets/Lobby/scripts/common/Config";
import DragonTigerSignalRClient from "db://assets/Lobby/scripts/common/networks/DragonTigerSignalRClient";
import BlackjackSignalRClient from "db://assets/Lobby/scripts/common/networks/BlackjackSignalRClient";
import BaccaratSignalRClient from "db://assets/Lobby/scripts/common/networks/BaccaratSignalRClient";
import SedieSignalRClient from "db://assets/Lobby/scripts/common/networks/SedieSignalRClient";
import SicboSignalRClient from "db://assets/Lobby/scripts/common/networks/SicboSignalRClient";
import RouletteSignalRClient from "db://assets/Lobby/scripts/common/networks/RouletteSignalRClient";
import App from "db://assets/Lobby/scripts/common/App";
import ChatHubSignalRClient from "db://assets/Lobby/scripts/common/networks/ChatHubSignalRClient";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Casino/Lobby")
export default class CasinoLobby extends Component {
    static instance: CasinoLobby = null;

    @property(Label)
    labelGameName: Label = null;
    @property([Node])
    listRoomVip: Node[] = [];
    @property([Node])
    listRoomNormal: Node[] = [];
    @property(Node)
    containerVIP: Node = null;
    @property(Node)
    containerNormal: Node = null;
    @property(Node)
    contentMsg: Node = null;
    @property(Node)
    itemMsg: Node = null;
    @property(EditBox)
    editBoxChat: EditBox = null;
    @property(Prefab)
    popupGuide: Prefab = null;

    private gameID = 0;
    private chatChannel = "";
    private hub: SignalRClient = null;
    private guideText = "";

    isTableVip = true;

    start () {
        CasinoLobby.instance = this;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    setDataRoom(gameID: number) {
        this.gameID = gameID;
        this.node.getComponentInChildren(CasinoPopupRank).gameId = gameID;
        this.labelGameName.string = Configs.InGameIds.getGameName(gameID).toUpperCase();
        this.chatChannel = Configs.InGameIds.getChatChannelName(gameID);

        switch (gameID) {
            case Configs.InGameIds.RongHo:
                this.hub = DragonTigerSignalRClient.getInstance();
                break;
            case Configs.InGameIds.XocDia:
                this.hub = SedieSignalRClient.getInstance();
                break;
            case Configs.InGameIds.BlackJack:
                this.hub = BlackjackSignalRClient.getInstance();
                break;
            case Configs.InGameIds.Baccarat:
                this.hub = BaccaratSignalRClient.getInstance();
                break;
            case Configs.InGameIds.Sicbo:
                this.hub = SicboSignalRClient.getInstance();
                break;
            case Configs.InGameIds.Roullete:
                this.hub = RouletteSignalRClient.getInstance();
                break;
            default:
                return;
        }

        var dataVip = Configs.App.G88_CONFIG["ListRoomVip"][this.gameID];
        var dataNormal = Configs.App.G88_CONFIG["ListRoomNormal"][this.gameID];

        for (let i0 = 0; i0 < this.listRoomVip.length; i0++) {
            if (dataVip[i0].IsLock) {
                this.listRoomVip[i0].getComponent(UIOpacity).opacity = 130;
            } else {
                this.listRoomVip[i0].on("click", () => {
                    this.actJoinRoom(dataVip[i0].Value, 1);
                    this.isTableVip = true;
                });
            }
        }

        for (let i1 = 0; i1 < this.listRoomNormal.length; i1++) {
            if (dataNormal[i1].IsLock) {
                this.listRoomNormal[i1].getComponent(UIOpacity).opacity = 130;
            } else {
                this.listRoomNormal[i1].on("click", () => {
                    this.actJoinRoom(dataNormal[i1].Value, 0);
                    this.isTableVip = false;
                });
            }
        }

        this.hub.send('EnterGame', [Configs.Login.CurrencyID], (data) => {
            if (data.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${data.c}`));
                return;
            }

            const lastRoomValue = parseInt(Utils.getStorageKey("last_open_casino_room_value"));
            const lastRoomCurrency = parseInt(Utils.getStorageKey("last_open_casino_room_currency"));
            if (lastRoomValue > 0 && lastRoomCurrency >= 0) {
                this.isTableVip = lastRoomCurrency == 1;
                this.actJoinRoom(lastRoomValue, lastRoomCurrency);
            }
        });

        this.contentMsg.removeAllChildren();
        ChatHubSignalRClient.getInstance().registerChat(this.chatChannel, (_response) => {});
        ChatHubSignalRClient.getInstance().receiveChat((response) => {
            for (var i = 0; i < response.length; i++) {
                const data = response[i];
                if (data.i !== this.chatChannel) {
                    return;
                }

                this.editBoxChat.string = "";
                var item = instantiate(this.itemMsg);
                if (data.v >= 6) {
                    item.getComponent(RichText).string = `<color=#ffffff>${data.c}</color>`;
                } else if (`${data.a}:${data.p}` === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    item.getComponent(RichText).string = `<color=#fff600>${data.n}: </c><color=#ffffff>${data.c}</color>`;
                } else {
                    item.getComponent(RichText).string = `<color=#3c91e6>${data.n}: </c><color=#ffffff>${data.c}</color>`;
                }
                this.contentMsg.addChild(item);
            }
        });
    }

    showVIP(toggle: Toggle) {
        if (toggle.isChecked == false) return;
        this.containerVIP.active = true;
        this.containerNormal.active = false;
        this.node.getComponentInChildren(CasinoPopupRank).loadDataGold();
    }

    showNormal(toggle: Toggle) {
        if (toggle.isChecked == false) return;
        this.containerVIP.active = false;
        this.containerNormal.active = true;
        this.node.getComponentInChildren(CasinoPopupRank).loadDataSilver();
    }

    actBack(){
        this.hub.send('ExitGame', [], (_data) => {});
        ChatHubSignalRClient.getInstance().unRegisterChat(this.chatChannel, (_response) => {});
        this.node.destroy();
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        Utils.setStorageValue("last_open_game_id", "");
        Utils.setStorageValue("last_open_casino_room_value", "");
        Utils.setStorageValue("last_open_casino_room_currency", "");
    }

    protected onDestroy() {
        this.hub.dontReceive();
        ChatHubSignalRClient.getInstance().dontReceive();
    }

    actJoinRoom(roomValue: number, currency: number) {
        if ((currency === 0 && roomValue > Configs.Login.CoinBalance) || (currency === 1 && roomValue > Configs.Login.GoldBalance)) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang("ca-10005"));
            return;
        }

        var bundleName: string = "";
        var prefabName: string = "";
        var playComponent: string = "";

        switch (this.gameID) {
            case Configs.InGameIds.RongHo:
                bundleName = "RongHo";
                prefabName = "res/prefabs/Play";
                playComponent = "RongHo.Play";
                break;
            case Configs.InGameIds.XocDia:
                bundleName = "XocDia";
                prefabName = "res/prefabs/Play";
                playComponent = "XocDia.Play";
                break;
            case Configs.InGameIds.BlackJack:
                bundleName = "BlackJack";
                prefabName = "res/prefabs/Play";
                playComponent = "BlackJack.Play";
                break;
            case Configs.InGameIds.Baccarat:
                bundleName = "Baccarat";
                prefabName = "res/prefabs/Play";
                playComponent = "Baccarat.Play";
                break;
            case Configs.InGameIds.Sicbo:
                bundleName = "Sicbo";
                prefabName = "res/prefabs/Play";
                playComponent = "Sicbo.Play";
                break;
            case Configs.InGameIds.Roullete:
                bundleName = "Roulette";
                prefabName = "res/prefabs/Play";
                playComponent = "Roulette.Play";
                break;
            default:
                return;
        }

        App.instance.showLoading(true, -1);
        BundleControl.loadPrefabGame(
            bundleName,
            prefabName,
            (finish: number, total: number) => {
                // App.instance.showLoadingProcess(true);
                // App.instance.lblStatus.string = parseInt(String((finish / total) * 100)) + "%";
                // App.instance.spriteProgress.fillRange = (finish / total);
                // App.instance.showErrLoading(App.instance.getTextLang("txt_loading1") + parseInt(String((finish / total) * 100)) + "%");
            },
            (prefab: Prefab) => {
                // App.instance.showLoadingProcess(false);
                App.instance.showLoading(false);
                const playCasino = instantiate(prefab).getComponent(playComponent);
                Utils.setStorageValue("last_open_casino_room_value", roomValue.toString());
                Utils.setStorageValue("last_open_casino_room_currency", currency.toString());
                // @ts-ignore
                playCasino.init(roomValue, currency);
                App.instance.bigGameNode.addChild(playCasino.node);
            }
        );
    }

    sendMessage() {
        var content = this.editBoxChat.string;
        if (content == "") {
            return;
        }

        ChatHubSignalRClient.getInstance().sendChat(this.chatChannel, content, (_response) => {
            this.editBoxChat.string = "";
        });
    }

    actPopupGuide() {
        switch (this.gameID) {
            case Configs.InGameIds.RongHo:
                this.guideText = App.instance.getTextLang("ca259");
                break;
            case Configs.InGameIds.XocDia:
                this.guideText = App.instance.getTextLang("ca249");
                break;
            case Configs.InGameIds.BlackJack:
                this.guideText = App.instance.getTextLang("ca257");
                break;
            case Configs.InGameIds.Baccarat:
                this.guideText = App.instance.getTextLang("ca222") + '\n\n' + App.instance.getTextLang("ca223") + '\n\n' + App.instance.getTextLang("ca224");
                break;
            case Configs.InGameIds.Sicbo:
                this.guideText = App.instance.getTextLang("ca244") + '\n\n' + App.instance.getTextLang("ca245") + '\n\n' + App.instance.getTextLang("ca246");
                break;
            case Configs.InGameIds.Roullete:
                this.guideText = App.instance.getTextLang("ca258");
                break;
            default:
                return;
        }

        var guide = instantiate(this.popupGuide);
        guide.getComponentInChildren(RichText).string = this.guideText;
        App.instance.bigGameNode.addChild(guide);
    }
}

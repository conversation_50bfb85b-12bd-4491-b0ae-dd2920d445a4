import { _decorator, Component, Label, Sprite } from 'cc';
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import {SpaceWarPhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SpaceWarPhotonClient";
const { ccclass, property, menu } = _decorator;

@ccclass('SpaceWarLobby')
@menu('SpaceWar/Lobby')
export class SpaceWarLobby extends Component {

    @property(Label)
    lblLoginStatus: Label = null;
    @property(Label)
    lblGoldBalance: Label = null;
    @property(Label)
    lblGemBalance: Label = null;
    @property(Sprite)
    sprAvatar: Sprite = null;

    private photonClient: SpaceWarPhotonClient = null;

    protected start() {
        this.photonClient = SpaceWarPhotonClient.getInstance();
        this.photonClient.connect();
        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Account, (res: any) => {
            if (res.errCode < 0) {
                this.lblLoginStatus.string = 'Login failed';
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            App.instance.showLoading(false);
            this.photonClient.isLoggedIn = true;
            this.lblLoginStatus.string = 'Login successful';

            var IAccountModel = JSON.parse(res.vals[SpaceWarPhotonClient.EParameterCodes.LoginResponse]);
            this.lblGoldBalance.string = `Gold: ${Utils.formatNumber(IAccountModel.go)}`;
            this.lblGemBalance.string = `Gem: ${Utils.formatNumber(IAccountModel.ge)}`;
            this.sprAvatar.spriteFrame = App.instance.getAvatarSpriteFrame(IAccountModel.ai);
        });
    }

    dismiss() {
        App.instance.gotoLobby();
        SpaceWarPhotonClient.getInstance().peer.disconnect();
    }
}


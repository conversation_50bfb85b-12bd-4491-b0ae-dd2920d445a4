import { _decorator, Component, tween, UIOpacity } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('BlinkNodeBulletXK')
export class BlinkNode extends Component {

    @property({ tooltip: "Time Delay" })
    delayStart: number = 0;

    @property({ tooltip: "Time Done" })
    blinkDuration: number = 0.6;

    @property({ tooltip: "Opacity lowest" })
    minOpacity: number = 50;

    private _uiOpacity: UIOpacity | null = null;

    start() {
        this._uiOpacity = this.node.getComponent(UIOpacity);
        if (!this._uiOpacity) {
            this._uiOpacity = this.node.addComponent(UIOpacity);
        }

        this.scheduleOnce(() => {
            this.startBlinking();
        }, this.delayStart);
    }

    startBlinking() {
        const halfDuration = this.blinkDuration / 2;

        tween(this._uiOpacity!)
            .repeatForever(
                tween()
                    .to(halfDuration, { opacity: this.minOpacity })
                    .to(halfDuration, { opacity: 255 })
            )
            .start();
    }

    onDestroy() {
        tween(this._uiOpacity!).stop();
    }
}

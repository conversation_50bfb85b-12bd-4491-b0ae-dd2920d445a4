import { _decorator, Component, Node, Label, AudioClip, sys, instantiate, Prefab, log, Tween } from 'cc';
import Play from "db://assets/XuatKich/SortieScript/Play";
import {SortiePhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SortiePhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import Configs from "db://assets/Lobby/scripts/common/Config";
import PanelMenu from "db://assets/XuatKich/SortieScript/PanelMenu";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("Sortie/Lobby")
export default class Lobby extends Component {

    public static instance: Lobby = null;

    @property(Node)
    playNode: Node = null;
    @property(Label)
    lblGoldBalance: Label = null;
    @property(Label)
    lblGemBalance: Label = null;
    @property(Label)
    currentJackpot: Label = null;
    @property(AudioClip)
    bgMusic1: AudioClip = null;
    @property(AudioClip)
    bgMusic2: AudioClip = null;
    @property(Node)
    popupContainer: Node = null;
    @property(Prefab)
    exchangeCoinPrefab: Prefab = null;
    @property(Prefab)
    popupGuidePrefab: Prefab = null;
    @property(Prefab)
    popupHistoryPrefab: Prefab = null;
    @property(Prefab)
    popupJackpotPrefab: Prefab = null;
    @property(Node)
    popupSetting: Node = null;
    @property(Node)
    iconClosePopupSetting: Node = null;
    @property(Node)
    iconOffSoundMusic: Node = null;
    @property(Node)
    iconOnSoundMusic: Node = null;
    @property(Node)
    iconOffSoundEffect: Node = null;
    @property(Node)
    iconOnSoundEffect: Node = null;
    
    private play: Play = null;
    private photonClient: SortiePhotonClient = null;
    private currentMusicIndex: number = 0;
    public listGunConfig = [];
    public clientParameterConfig = null;
    public currentUserAccumulate = 0;

    protected start() {
        this.photonClient = SortiePhotonClient.getInstance();
        this.photonClient.connect();
        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Account, (res: any) => {
            if (res.errCode < 0) {
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            log("Logged in successfully");
            App.instance.showLoading(false);
            this.photonClient.isLoggedIn = true;

            this.listGunConfig = JSON.parse(res.vals[SortiePhotonClient.EParameterCodes.GunConfigsResponse]);
            this.clientParameterConfig = JSON.parse(res.vals[SortiePhotonClient.EParameterCodes.ClientParameterConfig]);

            var IAccountModel = JSON.parse(res.vals[SortiePhotonClient.EParameterCodes.LoginResponse]);
            this.currentUserAccumulate = IAccountModel.ua;
            const goldBalance = IAccountModel.go;
            const gemBalance = IAccountModel.ge;

            this.lblGoldBalance.string = Utils.formatNumber(goldBalance);
            this.lblGemBalance.string = Utils.formatNumber(gemBalance);

            var data = [];
            data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.GetCurrentJackpot);
            data.push(SortiePhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID);
            this.photonClient.sendOperation(PhotonClient.EOperationCodes.Game, data, true);

            this.playMusic();
        });

        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Game, (res: any) => {
            if (res.errCode < 0) {
                return;
            }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code == SortiePhotonClient.EParameterCodes.CurrentJackpot) {
                    // Tween.numberTo(this.currentJackpot, data.value, 0.3); @TODO
                }
            });
        });
        this.updateSoundIcons();
    }

    updateBalance(gold: number, gem: number) {
        this.lblGemBalance.string = Utils.formatNumber(gem);
        this.lblGoldBalance.string = Utils.formatNumber(gold);
    }

    playMusic() {
        let visitCount = parseInt(sys.localStorage.getItem("XK_visit_count") || "0", 10);
        visitCount++;

        sys.localStorage.setItem("XK_visit_count", visitCount.toString());

        this.currentMusicIndex = visitCount % 2;

        let musicToPlay = this.currentMusicIndex === 0 ? this.bgMusic1 : this.bgMusic2;
        let isPlaying = JSON.parse(sys.localStorage.getItem("XK_is_playing"));

        if (musicToPlay && isPlaying) {
            // audioEngine.playMusic(musicToPlay, true);
        }

        this.updateSoundIcons();
    }

    onLoad() {
        Lobby.instance = this;

        this.play = this.playNode.getComponent(Play);
        this.play.node.active = false;
        this.lblGoldBalance.string = "0";
        this.lblGemBalance.string = "0";


        if (sys.localStorage.getItem("XK_is_playing") === null) {
            sys.localStorage.setItem("XK_is_playing", JSON.stringify(true));
        }

        if (sys.localStorage.getItem("XK_is_effect_on") === null) {
            sys.localStorage.setItem("XK_is_effect_on", JSON.stringify(true));
        }
    }

    public show(isShow: boolean) {
        this.node.active = isShow;
        if (isShow) {
            // audioEngine.stopAll();
            this.playMusic();
        }
    }

    actBack() {
        Tween.stopAllByTarget(this.node);
        // audioEngine.stopMusic();
        // audioEngine.stopAll();
        App.instance.gotoLobby();
        SortiePhotonClient.getInstance().peer.disconnect();
    }

    actPlay() {
        if (!this.photonClient || !this.photonClient.isLoggedIn) {
            return;
        }

        var params = [];
        params.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.JoinRoom);
        this.photonClient.sendOperation(PhotonClient.EOperationCodes.Game, params, true);

        this.playNode.active = true;
        this.show(false);
        // audioEngine.stopMusic();
        PanelMenu.instance.hide();
        PanelMenu.instance.updateSoundEffect();
        Play.instance.updateSoundMusic();
    }

    actExchangeCoin() {
        let exchangeCoin = instantiate(this.exchangeCoinPrefab);
        this.popupContainer.addChild(exchangeCoin);
        // @ts-ignore
        exchangeCoin.getComponent("PopupExchangeCoin").show();
    }

    actShowPopupGuide() {
        let popupGuide = instantiate(this.popupGuidePrefab);
        this.popupContainer.addChild(popupGuide);
        // @ts-ignore
        popupGuide.getComponent("PopupGuideXK").show();
    }

    actShowPopupHistory() {
        let popupHistory = instantiate(this.popupHistoryPrefab);
        this.popupContainer.addChild(popupHistory);
        // @ts-ignore
        popupHistory.getComponent("PopupHistory").show();
    }

    actShowPopupTopJackpot() {
        let popupJackpot = instantiate(this.popupJackpotPrefab);
        this.popupContainer.addChild(popupJackpot);
        // @ts-ignore
        popupJackpot.getComponent("PopupJackpot").show();
    }

    actShowPopupSetting() {
        this.popupSetting.active = true;
    }

    actClosePopupSetting() {
        this.popupSetting.active = false;
    }

    turnOnSoundMusic() {
        // audioEngine.resumeMusic();
        sys.localStorage.setItem("XK_is_playing", JSON.stringify(true));
        this.playMusic();
        this.updateSoundIcons();
    }

    turnOffSoundMusic() {
        // audioEngine.pauseMusic();
        sys.localStorage.setItem("XK_is_playing", JSON.stringify(false));
        this.updateSoundIcons();
    }

    turnOnSoundEffect() {
        let isEffectOn = JSON.parse(sys.localStorage.getItem("XK_is_effect_on") || "false");
        if (!isEffectOn) {
            sys.localStorage.setItem("XK_is_effect_on", JSON.stringify(true));
            // audioEngine.setEffectsVolume(1);
        }
        this.iconOnSoundEffect.active = true;
        this.iconOffSoundEffect.active = false;
    }

    turnOffSoundEffect() {
        let isEffectOn = JSON.parse(sys.localStorage.getItem("XK_is_effect_on") || "true");
        if (isEffectOn) {
            sys.localStorage.setItem("XK_is_effect_on", JSON.stringify(false));
            // audioEngine.setEffectsVolume(0);
        }
        this.iconOnSoundEffect.active = false;
        this.iconOffSoundEffect.active = true;
    }

    updateSoundIcons() {
        let isPlaying = JSON.parse(sys.localStorage.getItem("XK_is_playing") || "false");
        this.iconOnSoundMusic.active = isPlaying;
        this.iconOffSoundMusic.active = !isPlaying;

        let isEffectOn = JSON.parse(sys.localStorage.getItem("XK_is_effect_on") || "false");
        this.iconOnSoundEffect.active = isEffectOn;
        this.iconOffSoundEffect.active = !isEffectOn;
    }
}

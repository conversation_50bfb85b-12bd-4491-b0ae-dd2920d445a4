import {
    _decorator,
    Animation,
    Collider2D,
    Component,
    Contact2DType,
    IPhysics2DContact,
    math,
    Node,
    Size,
    Vec3
} from 'cc';
import Play from "db://assets/XuatKich/SortieScript/Play";
import Airplane from "db://assets/XuatKich/SortieScript/Airplane";
import {SortiePhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SortiePhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const { ccclass, property, menu } = _decorator;

@ccclass('Bullet')
@menu("Sortie/Bullet")
export default class Bullet extends Component {

    @property(Node)
    bullet: Node = null;
    @property(Node)
    exploreNode: Node = null;

    public gunId: number = -1;
    public positionID: number = -1;
    public targetAirplaneId = -1;
    private readonly worldSize: Size = new Size(1920, 1080);
    private readonly exploreDuration: number = 0.8;
    private vX = 0;
    private vY = 0;
    private collisionCount = 2;
    public isExplored = false;
    public isExploring = false;
    private curExplore = 0;
    private play: Play;

    protected onLoad() {
        this.play = Play.instance;
    }

    protected onEnable() {
        const collider = this.getComponent(Collider2D);
        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
        }
    }

    protected onDisable() {
        const collider = this.getComponent(Collider2D);
        if (collider) {
            collider.off(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
        }
    }

    private onBeginContact(_selfCollider: Collider2D, otherCollider: Collider2D, _contact: IPhysics2DContact | null) {
        if (this.isExploring || this.isExplored) return;

        const airplaneComponent = otherCollider.node.parent?.parent?.parent?.getComponent(Airplane);

        if (!airplaneComponent || !airplaneComponent.node.active) return;

        if (this.play.isTargetAirplane && this.play.targetAirplane !== airplaneComponent) return;

        if (airplaneComponent.type == 101 || airplaneComponent.type == 102) {
            this.exploreNode.setPosition(0, this.exploreNode.position.y);
        }
        this.explore();
        airplaneComponent.hurt();

        if (this.positionID !== this.play.mePlayer.serverPos) return;

        const data = {
            ft: airplaneComponent.id,
            si: this.play.shootingId,
            gi: this.play.mePlayer.gunId,
        };

        const params = [];
        params.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.PlayerShotFighter);
        params.push(SortiePhotonClient.EParameterCodes.ShotFighterMessage, JSON.stringify(data));

        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
    }

    public run() {
        const speed = 3000;
        const dir = Utils.degreesToVec2(this.node.angle);

        this.vX = dir.x * speed;
        this.vY = dir.y * speed;

        this.collisionCount = 2;
        this.isExplored = false;
        this.isExploring = false;
        this.node.active = true;
        this.bullet.active = true;
        this.exploreNode.active = true;

        const paddingMap: { [key: number]: number } = {
            1: 20, 2: 20, 3: 30, 4: 25, 5: 35,
            6: 35, 7: 40, 8: 40, 9: 40, 10: 45,
        };

        this.exploreNode.setPosition(paddingMap[this.gunId] || 0, this.exploreNode.position.y);
    }

    public updateRealTime(dt: number) {
        if (this.isExplored) return;

        if (this.isExploring) {
            this.curExplore -= dt;
            if (this.curExplore <= 0) {
                this.isExplored = true;
                this.node.active = false;
            }
            return;
        }

        const currentPos = this.node.position;
        const newPos = new Vec3(currentPos.x + this.vX * dt, currentPos.y + this.vY * dt, 0);

        let collided = false;
        if (Math.abs(newPos.x) > this.worldSize.width / 2) {
            this.vX *= -1;
            newPos.x = Math.sign(newPos.x) * this.worldSize.width / 2;
            collided = true;
        }
        else if (Math.abs(newPos.y) > this.worldSize.height / 2) {
            this.vY *= -1;
            newPos.y = Math.sign(newPos.y) * this.worldSize.height / 2;
            collided = true;
        }

        this.node.setPosition(newPos);

        if (collided) {
            this.node.angle = math.toDegree(Math.atan2(this.vY, this.vX));
            this.collisionCount--;
        }

        if (this.collisionCount < 0) {
            this.isExplored = true;
            this.node.active = false;
            return;
        }
    }

    public explore() {
        if (this.isExploring) return;

        this.isExploring = true;
        this.curExplore = this.exploreDuration;
        this.bullet.active = false;

        const effectExplore = this.exploreNode.getComponent(Animation);
        effectExplore.play(`Ex${this.gunId}`);
        effectExplore.once(Animation.EventType.FINISHED, () => {
            this.node.destroy();
        });
    }
}
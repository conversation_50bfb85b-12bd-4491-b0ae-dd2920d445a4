import { _decorator, Component, sp, Node, Label, SpriteFrame, instantiate, Vec2, v2, Vec3, v3, Prefab, Toggle, Tween, Button, Animation, Sprite, AudioClip, sys, tween, RichText, EventTouch, UIOpacity, UITransform, PolygonCollider2D } from 'cc';

import Player from './Player';
import Bullet from './Bullet';
import Airplane from './Airplane';
import {SortiePhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SortiePhotonClient";
import Lobby from './Lobby';
import XuatKichSyncTimeControl from './XuatKich.SyncTimeControl';
import { PhotonClient } from '../../Lobby/scripts/common/networks/skills/PhotonClient';
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("Sortie/Play")
export default class Play extends Component {

    public static instance: Play = null;

    @property(Node)
    lobby: Node = null;
    @property(Label)
    lblRoomId: Label = null;
    @property(Node)
    loading: Node = null;
    @property(Node)
    touchPad: Node = null;
    @property([SpriteFrame])
    sprFramesBullet: SpriteFrame[] = [];
    @property(Node)
    bulletTemplate: Node = null;
    @property(Node)
    bombTemplate: Node = null;
    @property([Player])
    players: Player[] = [];
    @property([Node])
    listAirplaneAnim: Node[] = [];
    @property(Node)
    listAirplaneNode: Node = null;
    @property(Node)
    airplaneTemplate: Node = null;
    @property(Toggle)
    toggleAuto: Toggle = null;
    @property({ type: AudioClip })
    soundShootGun1: AudioClip = null;
    @property({ type: AudioClip })
    soundShootGun2: AudioClip = null;
    @property({ type: AudioClip })
    soundShootGun3: AudioClip = null;
    @property({ type: AudioClip })
    soundShootGun4: AudioClip = null;
    @property({ type: AudioClip })
    soundShootGunUp: AudioClip = null;
    @property({ type: AudioClip })
    soundShootGunSpecial: AudioClip = null;
    @property(AudioClip)
    playSound1: AudioClip = null;
    @property(AudioClip)
    playSound2: AudioClip = null;
    @property(Node)
    gun: Node = null;
    @property([Node])
    backgrounds: Node[] = [];
    @property([SpriteFrame])
    sprEmotions: SpriteFrame[] = [];
    @property([SpriteFrame])
    sprEmotionInGames: SpriteFrame[] = [];@property(Label)
    lblJackpot: Label = null;
    @property(Label)
    lblJackpotPerGun: Label = null;
    @property(Label)
    lblPercentJackpot: Label = null;
    @property(Node)
    bossComing: Node = null;
    @property(Prefab)
    popupGuidePrefab: Prefab = null;
    @property(Prefab)
    popupChatPrefab: Prefab = null;
    @property(Node)
    popupContainer: Node = null;
    @property(Label)
    lblCountRadar: Label = null;
    @property(Label)
    lblCountTarget: Label = null;
    @property(Label)
    lblCountMagnetic: Label = null;
    @property(Sprite)
    targetTimeCounter: Sprite = null;
    @property(Node)
    radarStatusSprite: Node = null;
    @property(Sprite)
    processAccumulate: Sprite = null;
    @property(Node)
    processArrowNode: Node = null;
    @property(Node)
    targetNode: Node = null;
    @property(SpriteFrame)
    iconTarget: SpriteFrame = null;
    @property(Node)
    radarNode: Node = null;
    @property(SpriteFrame)
    iconRadar: SpriteFrame = null;
    @property(Node)
    magneticNode: Node = null;
    @property(SpriteFrame)
    iconMagnetic: SpriteFrame = null;
    @property(Node)
    lineTemplate: Node = null;
    @property(Node)
    radarClickNode: Node = null;
    @property(Node)
    effectRadar: Node = null;
    @property(Node)
    magneticClickNode: Node = null;
    @property(Node)
    effectMagnetic: Node = null;
    @property(Node)
    bombClickNode: Node = null;
    // BIG WIN
    @property(Node)
    effectBigWinXK: Node = null;
    @property(Node)
    effectJackpotXK: Node = null;
    @property(RichText)
    toast: RichText = null;
    
    public mePlayer: Player = null;
    private bullets: Bullet[] = [];
    private listAirplane: Airplane[] = [];
    private isShoot = false;
    public isTargetAirplane = false;
    public targetAirplane: Airplane = null;
    private readonly intervalFindTargetAirplane = 2;
    private curIntervalFindTargetAirplane = 0;
    private shootCooldown: number = 0;
    private shootInterval: number = 0.5;
    public shootingId = "";
    private timeNoAction = 0;
    private currentJackpot = 0;
    private currentJackpotPercent = 0;
    private countItemTarget = 0;
    private countItemRadar = 0;
    private countItemMagnetic = 0;
    private timeToUseTarget = 0;
    private currentTimeToUseTarget = 0;
    private dataChat = [];
    private allowUseItemTarget = true;
    private allowUseItemRadar = true;
    private allowUseItemMagnetic = true;
    private allowUseItemBomb = false;
    private lineSegments: Node[] = [];

    onLoad() {
        Play.instance = this;
    }

    protected onEnable() {
        this.listAirplaneNode.removeAllChildren();
        this.listAirplane = [];
        this.bullets = [];
        for (let i = 0; i < this.players.length; i++) {
            this.players[i].leave();
        }
        this.mePlayer = this.players[0];
        this.radarClickNode.active = false;
        this.magneticClickNode.active = false;
        this.bombClickNode.active = false;
        this.mePlayer.magneticEffect.active = false;
        this.dataChat = [];
        this.allowUseItemTarget = true;
        this.allowUseItemRadar = true;
        this.allowUseItemMagnetic = true;
        this.allowUseItemBomb = false;
        this.updateProcessAccumulate();
        this.timeNoAction = 0;
        this.toast.node.active = false;
        this.lblCountTarget.string = "0";
        this.lblCountRadar.string = "0";
        this.lblCountMagnetic.string = "0";
        this.countItemTarget = 0;
        this.countItemRadar = 0;
        this.countItemMagnetic = 0;
    }

    start() {
        this.touchPad.on(Node.EventType.TOUCH_START, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.mePlayer.rotateGun(touchPos);
            this.isShoot = true;
        }, this.touchPad);

        this.touchPad.on(Node.EventType.TOUCH_MOVE, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.mePlayer.rotateGun(touchPos);
        }, this.touchPad);

        this.touchPad.on(Node.EventType.TOUCH_END, (_event: EventTouch) => {
            this.isShoot = false;
        }, this.touchPad);

        this.touchPad.on(Node.EventType.TOUCH_CANCEL, (_event: EventTouch) => {
            this.isShoot = false;
        }, this.touchPad);

        this.toggleAuto.node.on("toggle", () => {
            if (this.toggleAuto.isChecked) {
                this.touchPad.active = false;
                this.curIntervalFindTargetAirplane = this.intervalFindTargetAirplane;
                this.isShoot = true;
                this.findAirplaneInWorld();
            } else {
                this.stopAutoShoot();
            }
            this.toggleBtnChatAndAuto();
        });

        SortiePhotonClient.getInstance().handleErrorResponse(() => {
            XuatKichSyncTimeControl.instance.resetPingOffline();
            this.node.active = false;
            this.lobby.getComponent(Lobby).show(true);
        });

        SortiePhotonClient.getInstance().handleTimeoutResponse(() => {
            XuatKichSyncTimeControl.instance.resetPingOffline();
            this.node.active = false;
            this.lobby.getComponent(Lobby).show(true);
        });

        SortiePhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Game, (res: any) => {
            if (res.errCode < 0) {
                this.stopAutoShoot();
                if (res.errCode == -7) {
                    App.instance.alertDialog.showMsg(PhotonClient.getErrMsg(res.errCode), () => {
                        this.back();
                    })
                } else {
                    App.instance.alertDialog.showMsg(PhotonClient.getErrMsg(res.errCode));
                    this.back();
                }
                return;
            }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code != SortiePhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (e) {}
                }

                if (code == SortiePhotonClient.EParameterCodes.PingResponse) {
                    XuatKichSyncTimeControl.instance.setReceivePingTime(data);
                }

                if (code == SortiePhotonClient.EParameterCodes.LeaveRoomResponse) {
                    if (data.id == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                        for (let i = 0; i < this.players.length; i++) {
                            this.players[i].leave();
                        }

                        XuatKichSyncTimeControl.instance.resetPingOffline();
                        this.node.active = false;
                        this.lobby.getComponent(Lobby).show(true);
                        this.lobby.getComponent(Lobby).updateBalance(data.go, data.ge);
                        App.instance.alertDialog.dismiss();
                        this.unscheduleAllCallbacks();
                    } else {
                        var leavePlayer = this.getPlayerById(data.id);
                        if (leavePlayer) {
                            this.showToast(`<color=#ffff00>${leavePlayer.nickname}</c><color=#ffffff>${App.instance.getTextLang('vc19').replace('{0}', '')}</color>`);
                            leavePlayer.leave();
                        }
                    }
                }

                if (code == SortiePhotonClient.EParameterCodes.JoinRoomResponse) {
                    XuatKichSyncTimeControl.instance.startPingServer();

                    this.lblRoomId.string = `#${data.r}`;
                    var mePlayerData = data.a.find((player: any) => player.id == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`);
                    this.mePlayer.set(mePlayerData);
                    this.mePlayer.localPos = 1;
                    var circle_1 = this.mePlayer.node.getChildByName('circle_1');
                    var circle_2 = this.mePlayer.node.getChildByName('circle_2');
                    if (circle_1 && circle_2) {
                        Tween.stopAllByTarget(circle_1);
                        Tween.stopAllByTarget(circle_2);
                        circle_1.getComponent(UIOpacity).opacity = 255;
                        circle_2.getComponent(UIOpacity).opacity = 255;

                        tween(circle_1.getComponent(UIOpacity))
                            .repeatForever(
                                tween()
                                    .to(0.5, { opacity: 50 })
                                    .to(0.5, { opacity: 255 })
                            )
                            .start();

                        tween(circle_2.getComponent(UIOpacity))
                            .repeatForever(
                                tween()
                                    .to(0.5, { opacity: 255 })
                                    .to(0.5, { opacity: 50 })
                            )
                            .start();
                    }

                    var posMe = mePlayerData.p;

                    var playerData: any;
                    for (let pos = 1; pos <= 4; pos++) {
                        if (pos != posMe && pos != 1) {
                            playerData = data.a.find((player: any) => player.p == pos);
                            if (playerData) {
                                this.players[pos - 1].set(playerData);
                                this.players[pos - 1].localPos = pos;
                            }
                        }
                    }

                    // swap player posMe to pos 1
                    if (posMe != 1) {
                        playerData = data.a.find((player: any) => player.p == 1);
                        if (playerData) {
                            this.players[posMe - 1].set(playerData);
                            this.players[posMe - 1].localPos = posMe;
                        }
                    }

                    for (let i = 0; i < data.f.length; i++) {
                        let airplaneNode = instantiate(this.airplaneTemplate);
                        let airplane = airplaneNode.getComponent(Airplane);
                        airplane.node.parent = this.listAirplaneNode;
                        airplane.setData(data.f[i]);
                        this.listAirplane.push(airplane);
                    }

                    this.updateJackpotObject(data.j, null);
                }

                if (code == SortiePhotonClient.EParameterCodes.OtherJoinRoomResponse) {
                    var playerOJR: Player;
                    if (data.p === 1) {
                        playerOJR = this.players[this.mePlayer.serverPos - 1];
                        playerOJR.localPos = this.mePlayer.serverPos;
                    } else {
                        playerOJR = this.players[data.p - 1];
                        playerOJR.localPos = data.p;
                    }
                    playerOJR.set(data);

                    this.showToast(`<color=#4ac7f7>${data.n}</c><color=#ffffff>${App.instance.getTextLang('vc18').replace('{0}', '')}</color>`);
                }

                if (code == SortiePhotonClient.EParameterCodes.ChangeGunResponse) {
                    var playerChangeGun = this.getPlayerById(data.i);
                    if (playerChangeGun) {
                        playerChangeGun.setGun(data.gi);
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.CreateFighterResponse) {
                    for (let i = 0; i < data.length; i++) {
                        let airplaneNode = instantiate(this.airplaneTemplate);
                        let airplane = airplaneNode.getComponent(Airplane);
                        airplane.node.parent = this.listAirplaneNode;
                        airplane.setData(data[i]);
                        this.listAirplane.push(airplane);
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.RemoveSingleFighterResponse) {
                    for (let i = 0; i < this.listAirplane.length; i++) {
                        var airplaneRSF = this.listAirplane[i];
                        if (airplaneRSF.type == data && airplaneRSF.node != null && airplaneRSF.node.active) {
                            airplaneRSF.die(null, null);
                        }
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.ShootingResponse) {
                    if (data.i == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                        this.timeNoAction = 0;
                    }
                    var playerSR = this.getPlayerById(data.i);
                    if (playerSR) {
                        playerSR.updateGold(data.g);

                        // update bullet
                        var shootingRes = data.s;
                        var shootingPos = parseInt(shootingRes.id.charAt(0));
                        var shootingPlayer = this.getPlayerByPos(shootingPos);
                        if (shootingPlayer) {
                            let bullet = this.getBullet(shootingPos, shootingRes.gi, shootingPlayer.id == this.mePlayer.id);

                            var vx = shootingRes.vx;
                            var vy = shootingRes.vy;

                            const magnitude = Math.sqrt(vx * vx + vy * vy);
                            if (magnitude > 1.5) {
                                vx /= magnitude;
                                vy /= magnitude;
                            }
                            let rad = Math.atan2(vy, vx);
                            let radByMe = rad;
                            bullet.node.angle = radByMe * Utils.Rad2Deg;

                            switch (shootingPlayer.localPos) {
                                case 1:
                                    radByMe = rad;
                                    break;
                                case 2:
                                    radByMe = Math.PI - rad;
                                    bullet.node.angle = radByMe * Utils.Rad2Deg;
                                    break;
                                case 3:
                                    radByMe = rad;
                                    bullet.node.angle = (radByMe + Math.PI) * Utils.Rad2Deg;
                                    break;
                                case 4:
                                    radByMe = Math.PI - rad;
                                    bullet.node.angle = (radByMe + Math.PI) * Utils.Rad2Deg;
                                    break;
                            }

                            shootingPlayer.rotateGunByAngle(radByMe * Utils.Rad2Deg);

                            var pos = bullet.node.parent.getComponent(UITransform).convertToNodeSpaceAR(shootingPlayer.fireEffect.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO));
                            bullet.node.setPosition(pos);
                            bullet.run();
                            shootingPlayer.startAnimFireGun();
                        }
                    }

                    this.updateJackpotObject(data.j, null);
                }

                if (code === SortiePhotonClient.EParameterCodes.HeadShotResponse) {
                    this.headShotResponseHandle(data);
                }

                if (code === SortiePhotonClient.EParameterCodes.ChatResponse) {
                    if (data.c.includes("__EIG__")) {
                        const [t, emotionId] = data.c.replace("__EIG__", "").split("__");
                        this.displayEmotion(data.f, t, parseInt(emotionId));
                        return;
                    }

                    this.dataChat.push(data);
                    var playerCR = this.getPlayerById(data.f);
                    if (playerCR) {
                        playerCR.receiveMessage(data.c);
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.TimeToUse) {
                    this.countItemTarget -= 1;
                    this.lblCountTarget.string = this.countItemTarget.toString();
                    this.isShoot = false;
                    this.toggleAuto.isChecked = false;
                    this.toggleAuto.enabled = false;
                    this.touchPad.active = false;
                    this.listAirplane.forEach((airplane: Airplane) => {
                        var airplaneAnim = airplane.anim.children[0].children[0];
                        airplaneAnim.addComponent(Button).enabled = true;
                        airplane.target.active = false;
                        airplaneAnim.off("click");
                        airplaneAnim.on("click", () => {
                            if (this.targetAirplane != null) {
                                this.targetAirplane.target.active = false;
                            }
                            airplane.target.active = true;
                            this.targetAirplane = airplane;
                            this.toggleAuto.isChecked = true;
                            this.isShoot = true;
                            this.isTargetAirplane = true;
                        });
                    });

                    this.targetTimeCounter.node.active = true;
                    this.timeToUseTarget = data;
                    this.currentTimeToUseTarget = data;
                    this.scheduleOnce(() => {
                        this.timeToUseTarget = 0;
                        this.currentTimeToUseTarget = 0;
                        this.targetTimeCounter.node.active = false;
                        this.touchPad.active = true;
                        this.toggleAuto.enabled = true;
                        this.isShoot = false;
                        this.toggleAuto.isChecked = false;
                        this.isTargetAirplane = false;
                        this.targetAirplane = null;
                        this.listAirplane.forEach((airplane: Airplane) => {
                            var fishAnim = airplane.anim.children[0].children[0];
                            fishAnim.removeComponent(Button);
                            airplane.target.active = false;
                        });
                        this.allowUseItemTarget = true;
                    }, data);
                }

                if (code == SortiePhotonClient.EParameterCodes.ReleaseRadarResponse) {
                    this.allowUseItemRadar = false;
                    this.effectRadar.active = true;
                    this.radarStatusSprite.active = true;

                    for (let i = 0; i < data.length; i++) {
                        let airplane = this.getAirplaneById(data[i]);
                        if (airplane) {
                            airplane.radarAffected = true;
                        }
                    }
                }

                if (code == SortiePhotonClient.EParameterCodes.CancelRadarResponse) {
                    this.allowUseItemRadar = true;
                    this.effectRadar.active = false;
                    this.radarStatusSprite.active = false;
                    this.listAirplane.forEach(x => {
                        x.radarAffected = false;
                    });
                }

                if (code == SortiePhotonClient.EParameterCodes.ShotMagneticMessage) {
                    var playerSMM = this.getPlayerById(data.i);
                    if (playerSMM) {
                        playerSMM.setGun(12);
                        var vxSMM = data.vx;
                        var vySMM = data.vy;

                        const magnitude = Math.sqrt(vxSMM * vxSMM + vySMM * vySMM);
                        if (magnitude > 1.5) {
                            vxSMM /= magnitude;
                            vySMM /= magnitude;
                        }
                        var radSMM = Math.atan2(vySMM, vxSMM);
                        if ([2,4].includes(playerSMM.localPos)) {
                            radSMM = Math.PI - radSMM;
                        }
                        playerSMM.rotateGunByAngle(radSMM * Utils.Rad2Deg);
                        playerSMM.magneticEffect.active = true;
                        setTimeout(() => {
                            playerSMM.magneticEffect.active = false;

                            if (playerSMM.id == this.mePlayer.id) {
                                this.allowUseItemMagnetic = true;
                                var data = [];
                                data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.ReleaseMagnetic);
                                SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);
                            }
                        }, 1000);
                    }
                }

                if (code == SortiePhotonClient.EParameterCodes.ReleaseMagneticResponse) {
                    var playerRMR = this.getPlayerById(data.i);
                    if (data.i == this.mePlayer.id) {
                        this.countItemMagnetic = data.ri;
                        this.lblCountMagnetic.string = this.countItemMagnetic.toString();
                    }

                    if (playerRMR) {
                        playerRMR.setGun(data.g.gi);

                        data.r.f.forEach((f_id: number) => {
                            var airplaneRMR = this.getAirplaneById(f_id);
                            if (airplaneRMR) {
                                airplaneRMR.effectMagnetic.active = true;
                                airplaneRMR.effectMagnetic.getComponent(Animation).play();
                            }
                        })

                        setTimeout(() => {
                            this.headShotResponseHandle(data.r);
                            this.allowUseItemMagnetic = true;
                        }, 2000);
                    }
                }

                if (code == SortiePhotonClient.EParameterCodes.DropBombMessage) {
                    var playerDBM = this.getPlayerById(data.i);
                    if (playerDBM) {
                        const originalGunId = playerDBM.gunId;
                        playerDBM.setGun(11);
                         this.scheduleOnce(() => {
                             playerDBM.lblBet.string = "0";
                             var vxDBM = data.vx;
                             var vyDBM = data.vy;

                             const magnitude = Math.sqrt(vxDBM * vxDBM + vyDBM * vyDBM);
                             if (magnitude > 1.5) {
                                 vxDBM /= magnitude;
                                 vyDBM /= magnitude;
                             }
                             var radDBM = Math.atan2(vyDBM, vxDBM);
                             let bomb = instantiate(this.bombTemplate);
                             this.bombTemplate.parent.addChild(bomb);
                             bomb.active = true;
                             bomb.angle = radDBM * Utils.Rad2Deg;
                             switch (playerDBM.localPos) {
                                 case 2:
                                     bomb.angle = (Math.PI - radDBM) * Utils.Rad2Deg;
                                     break;
                                 case 3:
                                     bomb.angle = (radDBM + Math.PI) * Utils.Rad2Deg;
                                     break;
                                 case 4:
                                     bomb.angle = (- radDBM) * Utils.Rad2Deg;
                                     break;
                             }
                             if ([2,4].includes(playerDBM.localPos)) {
                                 radDBM = Math.PI - radDBM;
                             }
                             playerDBM.rotateGunByAngle(radDBM * Utils.Rad2Deg);
                             const startPos = bomb.parent.getComponent(UITransform).convertToNodeSpaceAR(
                                 playerDBM.fireEffect.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO)
                             );
                             bomb.setPosition(startPos);
                             const dy = 0 - startPos.y;
                             const dx = dy / Math.tan(radDBM);
                             const endX = startPos.x + dx;
                             const endPos = v3(endX, 0);

                             tween(bomb)
                                 .to(1, { position: endPos }, { easing: "quadIn" })
                                 .call(() => {
                                     bomb.destroy();
                                     setTimeout(() => {
                                         playerDBM.setGun(originalGunId);
                                     }, 500);
                                     if (this.mePlayer.id == data.i) {
                                         this.updateProcessAccumulate();
                                         var payload = {
                                             x: this.bombClickPath.x,
                                             y: this.bombClickPath.y
                                         }

                                         var dataRB = [];
                                         dataRB.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.ReleaseBomb);
                                         dataRB.push(SortiePhotonClient.EParameterCodes.ReleaseBombMessage, JSON.stringify(payload));

                                         SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, dataRB, true);
                                     }
                                 })
                                 .start();
                         }, this.mePlayer.id == data.i ? 0 : 1 );
                    }
                }

                if (code == SortiePhotonClient.EParameterCodes.ReleaseBombResponse) {
                    var playerRBR = this.getPlayerById(data.i);

                    if (playerRBR) {
                        playerRBR.setGun(data.g.gi);
                        this.headShotResponseHandle(data.r);
                    }
                }
            });

        });

        this.updateSoundMusic();
    }

    headShotResponseHandle(data: any) {
        var playerHSR = this.getPlayerById(data.i);
        if (playerHSR) {
            playerHSR.updateGold(data.go);
        }

        const airplane0 = this.getAirplaneById(data.f[0]);
        const position0 = airplane0?.node.position;
        for (let i = 0; i < data.f.length; i++) {
            let airplane = this.getAirplaneById(data.f[i]);
            if (!airplane || airplane.node == null || airplane.node.active == false) continue;
            if (airplane && playerHSR) {
                airplane.die(playerHSR, data.gor);
            }

            if (airplane?.type == Airplane.instance.BOSS_TYPE) {
                if (data.i == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    this.effectJackpotXK.active = true;
                    this.effectJackpotXK.getComponentInChildren(Label).string = '0';
                    // Tween.numberTo(this.effectJackpotXK.getComponentInChildren(Label), data.gor, 0.3, (value: number) => {
                    //     return value.toLocaleString("VN");
                    // });
                    let jackpotSpriteNode = this.effectJackpotXK.getChildByName('sprite_jp');
                    tween(jackpotSpriteNode)
                        .repeatForever(
                            tween()
                                .to(0.5, { scale: 1.2 })
                                .to(0.5, { scale: 1 })
                        )
                        .start();

                    this.scheduleOnce(() => {
                        this.effectJackpotXK.active = false;
                        Tween.stopAllByTarget(jackpotSpriteNode);
                    }, 3);
                } else {
                    playerHSR.showMiniJackpot(data.gor);
                }
            }

            if (airplane.type >= 17 && airplane?.type !== Airplane.instance.BOSS_TYPE) {
                let effectBigWinXK = instantiate(this.effectBigWinXK);
                effectBigWinXK.parent = this.mePlayer.node.parent;
                var subY = (playerHSR.serverPos == this.players[0].serverPos || playerHSR.serverPos == this.players[1].serverPos) ? -135 : 200;
                effectBigWinXK.setPosition(playerHSR.node.x, subY);
                effectBigWinXK.active = true;
                effectBigWinXK.getChildByName('text').getComponent(Label).string = Utils.formatNumber(data.gor);
                effectBigWinXK.getChildByName('anim').getComponent(Animation).play();

                this.scheduleOnce(() => {
                    effectBigWinXK.getChildByName('anim').getComponent(Animation).stop();
                    effectBigWinXK.destroy();
                }, 3);
            }
        }

        this.updateJackpotObject(data.j, null);

        if (data.i == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
            if (data.bi && Object.keys(data.bi).length > 0) {
                for (const key in data.bi) {
                    if (data.bi.hasOwnProperty(key)) {
                        let bonusItem = data.bi[key]; // Array of numbers
                        this.updateDataBonusItem(bonusItem[0], bonusItem[1], bonusItem[2], position0);
                    }
                }
            }
            this.updateProcessAccumulate(data.a);
        }
    }

    updateSoundMusic() {
        let playCount = parseInt(sys.localStorage.getItem("XK_play_count") || "0", 10);
        playCount++;
        sys.localStorage.setItem("XK_play_count", playCount.toString());

        let playSound = (playCount % 2 === 0) ? this.playSound1 : this.playSound2;
        this.backgrounds.forEach((bg: Node, index: number) => {
           bg.active = playCount % 5 == index;
        });
        let isPlaying = JSON.parse(sys.localStorage.getItem("XK_is_playing"));
        let isEffectOn = JSON.parse(sys.localStorage.getItem("XK_is_effect_on"));
        if (playSound && isPlaying) {
            // // audioEngine.playMusic(playSound, false);
        }

        // // audioEngine.setEffectsVolume(isEffectOn ? 1 : 0);
    }

    togglePlayMusic() {
        let isPlaying = JSON.parse(sys.localStorage.getItem("XK_is_playing") || "false");

        isPlaying = !isPlaying;
        sys.localStorage.setItem("XK_is_playing", JSON.stringify(isPlaying));

        if (isPlaying) {
            let playSound = (Math.random() < 0.5) ? this.playSound1 : this.playSound2;

            if (playSound) {
                // audioEngine.playMusic(playSound, true);
            }
        } else {
            // audioEngine.stopMusic();
        }
    }

    onDestroy() {
        // audioEngine.stopAll();
    }

    updateProcessAccumulate(value: number = null) {
        if (value !== null) {
            Lobby.instance.currentUserAccumulate = value;
        }
        this.processAccumulate.fillRange = Lobby.instance.currentUserAccumulate / Lobby.instance.clientParameterConfig.al;

        Tween.stopAllByTarget(this.processArrowNode);
        this.processArrowNode.active = this.processAccumulate.fillRange >= 1;
        if (this.processArrowNode.active) {
            this.allowUseItemBomb = true;
            tween(this.processArrowNode)
                .repeatForever(
                    tween()
                        .to(0.5, { y: 70 })
                        .to(0.5, { y: 50 })
                )
                .start();
        }
    }

    update(dt: number) {
        this.updateShoot(dt);

        if (this.currentTimeToUseTarget > 0) {
            this.currentTimeToUseTarget -= dt;
            this.targetTimeCounter.fillRange = 1 - (this.currentTimeToUseTarget / this.timeToUseTarget);
        }

        this.timeNoAction += dt;
        if (this.timeNoAction > 80) {
            this.timeNoAction = 0;
            App.instance.alertDialog.showMsg(App.instance.getTextLang('TLN_ONLINE_CONFIRM'));
        }

        if (this.shootCooldown > 0) {
            this.shootCooldown -= dt;
        }

        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            if (bullet.node == null || !bullet.node.active) {
                this.bullets.splice(i, 1);
            } else {
                bullet.updateRealTime(dt);
            }
        }

        for (let i = this.listAirplane.length - 1; i >= 0; i--) {
            const airplane = this.listAirplane[i];
            if (airplane.node == null || !airplane.node.active) {
                this.listAirplane.splice(i, 1);
            } else {
                airplane.updateRealTime(dt);
            }
        }

        if (this.isTargetAirplane && this.targetAirplane && this.targetAirplane.node.activeInHierarchy) {
            const worldPosA = this.mePlayer.fireEffect.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
            const worldPosB = this.targetAirplane.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);

            const start = this.node.getComponent(UITransform).convertToNodeSpaceAR(worldPosA);
            const end = this.node.getComponent(UITransform).convertToNodeSpaceAR(worldPosB);

            const direction = end.subtract(start);
            const distance = direction.length();
            const angle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;

            const segmentSpacing = 14;
            const segmentCount = Math.floor(distance / segmentSpacing);

            while (this.lineSegments.length < segmentCount) {
                const segment = instantiate(this.lineTemplate);
                this.node.addChild(segment);
                this.lineSegments.push(segment);
            }

            for (let i = 0; i < this.lineSegments.length; i++) {
                const segment = this.lineSegments[i];

                if (i < segmentCount) {
                    if (i % 2 === 0) {
                        segment.active = true;

                        const t = i * segmentSpacing / distance;
                        const pos = start.lerp(end, t);
                        segment.setPosition(pos);
                        segment.angle = angle;
                    } else {
                        segment.active = false;
                    }
                } else {
                    segment.active = false;
                }
            }
        } else {
            for (let segment of this.lineSegments) {
                segment.active = false;
            }
        }
    }

    private updateShoot(dt: number) {
        if (this.targetAirplane != null && this.targetAirplane.isDie) {
            this.targetAirplane = null;
            if (this.isTargetAirplane) {
                this.toggleAuto.isChecked = false;
                this.isShoot = false;
                this.isTargetAirplane = false;
            }
        }

        if (this.toggleAuto.isChecked) {
            if (this.targetAirplane != null) {
                var gunWorldPos = this.mePlayer.gunRotate.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                var airplaneWorldPos = this.targetAirplane.node.getComponent(UITransform).convertToWorldSpaceAR(v3(this.targetAirplane.node.getComponent(UITransform).width / 2, 0));
                var distance = Utils.v2Distance(v2(airplaneWorldPos.x, airplaneWorldPos.y), v2(gunWorldPos.x, gunWorldPos.y));
                var dAngle = airplaneWorldPos.subtract(gunWorldPos);

                if (this.isTargetAirplane) {
                    if (Math.abs(this.targetAirplane.node.x) > 960 || Math.abs(this.targetAirplane.node.y) > 540) {
                        this.isShoot = false;
                    } else {
                        this.isShoot = true;
                        this.mePlayer.gunRotate.angle = Math.atan2(dAngle.y, dAngle.x) * Utils.Rad2Deg;
                    }
                } else if (Math.abs(this.targetAirplane.node.x) > 960 * 0.8 || Math.abs(this.targetAirplane.node.y) > 540 * 0.8 || distance < 200) {
                    this.targetAirplane = null;
                    this.curIntervalFindTargetAirplane = 0;
                } else {
                    this.mePlayer.gunRotate.angle = Math.atan2(dAngle.y, dAngle.x) * Utils.Rad2Deg;
                }
            } else if (!this.isTargetAirplane) {
                this.curIntervalFindTargetAirplane = Math.max(0, this.curIntervalFindTargetAirplane - dt);
                if (this.curIntervalFindTargetAirplane == 0) {
                    this.findAirplaneInWorld();
                }
            }
        }

        if (this.isShoot && this.shootCooldown <= 0) {
            this.shootCooldown = this.shootInterval;

            let velocity = Utils.degreesToVec2(this.mePlayer.gunRotate.angle);
            this.shootingId = this.mePlayer.serverPos + "" + (new Date()).getTime() % 10000000;

            var data = {
                "id": this.shootingId,
                "gi": this.mePlayer.gunId,
                "p": this.mePlayer.serverPos,
                "vx": velocity.x,
                "vy": velocity.y,
                "t": (new Date()).toISOString(),
                "iu": 0
            }

            // audioEngine.playEffect(this.soundShootGun1, false);

            var params = [];
            params.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.PlayerShooting);
            params.push(SortiePhotonClient.EParameterCodes.ShootingMessage, JSON.stringify(data));

            SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
        }
    }

    private findAirplaneInWorld() {
        this.curIntervalFindTargetAirplane = this.intervalFindTargetAirplane;

        let listAirplaneActiveInWorld = [];

        var gunWorldPos = this.mePlayer.gunRotate.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
        for (let i = 0; i < this.listAirplane.length; i++) {
            var airplaneNode = this.listAirplane[i].node;
            if (airplaneNode.active && Math.abs(airplaneNode.position.x) <= 960 * 0.8 && Math.abs(airplaneNode.position.y) <= 540 * 0.8) {
                var fishWorldPos = airplaneNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                var distance = Utils.v2Distance(v2(gunWorldPos.x, gunWorldPos.y), v2(fishWorldPos.x, fishWorldPos.y));
                if (distance >= 135) {
                    listAirplaneActiveInWorld.push({
                        airplane: this.listAirplane[i],
                        distance: distance
                    });
                }
            }
        }
        if (listAirplaneActiveInWorld.length > 0) {
            this.targetAirplane = listAirplaneActiveInWorld[Utils.randomRangeInt(0, listAirplaneActiveInWorld.length)]["airplane"];
        }
    }

    private stopAutoShoot() {
        this.isShoot = false;
        this.toggleAuto.isChecked = false;
        // this.target.active = false;
        this.touchPad.active = true;
        this.curIntervalFindTargetAirplane = 0;
        this.targetAirplane = null;
    }

    private getBullet(pos: number, gunId: number, isMe = false): Bullet {
        let bullet: Bullet;
        let node = instantiate(this.bulletTemplate);
        node.parent = this.bulletTemplate.parent;
        var source = node.getChildByName(gunId + "").getComponent(PolygonCollider2D);
        const target = node.getComponent(PolygonCollider2D);
        target.points = source.points.map(p => v2(p.x, p.y));
        bullet = node.getComponent(Bullet);
        bullet.positionID = pos;
        bullet.gunId = gunId;
        this.bullets.push(bullet);
        bullet.node.active = false;
        // if (isMe && this.isTargetAirplane) {
        //     bullet.bullet.active = false;
        //     bullet.bulletAim.active = true;
        // } else {
            bullet.bullet.getComponent(Sprite).spriteFrame = this.sprFramesBullet[gunId - 1];
        // }

        return bullet;
    }

    private getAirplaneById(id: number): Airplane {
        for (let i = 0; i < this.listAirplane.length; i++) {
            if (this.listAirplane[i].id == id) return this.listAirplane[i];
        }
        return null;
    }

    getPlayerById(id: string): Player {
        if (id <= "") return null;
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].id != "" && this.players[i].id == id) return this.players[i];
        }
        return null;
    }

    private getPlayerByPos(pos: number): Player {
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].serverPos == pos) return this.players[i];
        }
        return null
    }

    private getPlayerByLocalPos(pos: number): Player {
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].localPos == pos) return this.players[i];
        }
        return null
    }

    public showBossComing() {
        this.bossComing.active = true;
        this.bossComing.getComponent(sp.Skeleton).setAnimation(0, "animation", true);
        this.scheduleOnce(() => {
            this.bossComing.getComponent(sp.Skeleton).clearTrack(0);
            this.bossComing.active = false;
        }, 3);
    }

    public getAirplaneAnimByType(type: number): Node {
        let name = `air${type}`;
        for (let i = 0; i < this.listAirplaneAnim.length; i++) {
            if (this.listAirplaneAnim[i].name != null && this.listAirplaneAnim[i].name != "" && this.listAirplaneAnim[i].name == name) {
                return this.listAirplaneAnim[i];
            }
        }
        return this.listAirplaneAnim[0];
    }

    public actBetUp() {
        const currentId = this.mePlayer.gunId;
        const newId = currentId >= 10 ? 1 : currentId + 1;
        this.actUpdateGun(newId);
    }

    public actBetDown() {
        const currentId = this.mePlayer.gunId;
        const newId = currentId <= 1 ? 10 : currentId - 1;
        this.actUpdateGun(newId);
    }

    private actUpdateGun(gunId: number) {
        var data = {
            i: gunId
        }

        var params = [];
        params.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.ChangeGun);
        params.push(SortiePhotonClient.EParameterCodes.ChangeGunMessage, JSON.stringify(data));

        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
    }

    public actBack() {
        App.instance.confirmDialog.showMsg(App.instance.getTextLang("fish_pu6"), (isConfirm) => {
            if (isConfirm) {
                this.back();
                Lobby.instance.playMusic();
            }
        });
    }

    private back() {
        this.stopAutoShoot();

        this.scheduleOnce(() => {
            XuatKichSyncTimeControl.instance.resetPingOffline();
            this.node.active = false;
            this.lobby.getComponent(Lobby).show(true);
        }, 5);

        var params = [];
        params.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.LeaveRoom);
        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
    }

    actShowPopupGuide() {
        let popupGuide = instantiate(this.popupGuidePrefab);
        this.popupContainer.addChild(popupGuide);
        // @ts-ignore
        popupGuide.getComponent("PopupGuideXK").show();
    }

    setDataBonusItem(target: number, radar: number, magnetic: number) {
        this.countItemTarget = target;
        this.countItemRadar = radar;
        this.countItemMagnetic = magnetic;

        this.lblCountTarget.string = target.toString();
        this.lblCountRadar.string = radar.toString();
        this.lblCountMagnetic.string = magnetic.toString();
    }

    updateDataBonusItem(target: number, radar: number, magnetic: number, position0: Vec3) {
        if (target < 0 || radar < 0 || magnetic < 0) {
            return;
        }

        const createBonusNode = (spriteFrame: SpriteFrame, targetNode: Node) => {
            const endWorldPos = targetNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
            const endLocalPos = this.listAirplaneNode.getComponent(UITransform).convertToNodeSpaceAR(endWorldPos);

            const bonusNode = new Node();
            bonusNode.addComponent(Sprite).spriteFrame = spriteFrame;
            bonusNode.setPosition(position0);
            this.listAirplaneNode.addChild(bonusNode);

            tween(bonusNode)
                .to(2, { position: endLocalPos })
                .call(() => bonusNode.destroy())
                .start();
        };

        if (target > 0) {
            createBonusNode(this.iconTarget, this.targetNode);
        }

        if (radar > 0) {
            createBonusNode(this.iconRadar, this.radarNode);
        }

        if (magnetic > 0) {
            createBonusNode(this.iconMagnetic, this.magneticNode);
        }

        this.setDataBonusItem(
            this.countItemTarget + target,
            this.countItemRadar + radar,
            this.countItemMagnetic + magnetic
        );
    }

    actUseTarget() {
        if (this.countItemTarget <= 0 || !this.allowUseItemTarget) {
            return;
        }
        this.allowUseItemTarget = false;
        this.stopAutoShoot();
        var data = [];
        data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.UseTarget);
        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);
    }

    actUseRadar() {
        if (this.countItemRadar <= 0 || !this.allowUseItemRadar) {
            return;
        }

        this.allowUseItemRadar = false;
        this.showToast(App.instance.getTextLang("xk_no7"), false);
        this.radarClickNode.active = true;
        this.radarClickNode.on(Node.EventType.TOUCH_END, (event: EventTouch) => {
            let touchPos: Vec2 = event.getLocation();
            const uiTransform = this.radarClickNode.getComponent(UITransform);
            const localPos: Vec3 = uiTransform.convertToNodeSpaceAR(new Vec3(touchPos.x, touchPos.y, 0));
            this.effectRadar.setPosition(localPos);

            const targetNode = this.radarClickNode.getChildByName('radar_target');
            targetNode.setPosition(localPos);
            targetNode.active = true;

            var payload = {
                i: `${Configs.Login.AccountID}:${Configs.Login.PortalID}`,
                x: touchPos.x,
                y: touchPos.y
            }

            var data = [];
            data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.UseRadar);
            data.push(SortiePhotonClient.EParameterCodes.DropRadarMessage, JSON.stringify(payload));

            SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);
            this.toast.node.active = false;
            this.countItemRadar -= 1;
            this.lblCountRadar.string = this.countItemRadar.toString();
            this.radarClickNode.active = false;

            this.scheduleOnce(() => {
                targetNode.active = false;
            }, 2);
        }, this.radarClickNode);
    }

    actUseMagnetic() {
        if (this.countItemMagnetic <= 0 || !this.allowUseItemMagnetic) {
            return;
        }

        this.allowUseItemMagnetic = false;
        this.mePlayer.setGun(12);
        this.magneticClickNode.active = true;
        this.magneticClickNode.on(Node.EventType.TOUCH_END, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.mePlayer.rotateGun(touchPos);
            let velocity = Utils.degreesToVec2(this.mePlayer.gunRotate.angle);

            var payload = {
                i: `${Configs.Login.AccountID}:${Configs.Login.PortalID}`,
                gx: this.mePlayer.gunRotate.x,
                gy: this.mePlayer.gunRotate.y,
                vx: velocity.x,
                vy: velocity.y,
                a: this.mePlayer.gunRotate.angle
            }

            var data = [];
            data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.ShotMagnetic);
            data.push(SortiePhotonClient.EParameterCodes.ShotMagneticMessage, JSON.stringify(payload));

            SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);

            this.magneticClickNode.active = false;
        }, this.magneticClickNode);
    }

    private bombClickPath: Vec2;
    actDropBomb() {
        if (!this.allowUseItemBomb) return;

        this.allowUseItemBomb = false;
        this.mePlayer.setGun(11);
        this.mePlayer.lblBet.string = "0";
        Tween.stopAllByTarget(this.processArrowNode);
        this.processArrowNode.active = false;
        this.bombClickNode.active = true;

        this.bombClickNode.on(Node.EventType.TOUCH_START, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.mePlayer.rotateGun(touchPos);
        }, this.bombClickNode);

        this.bombClickNode.on(Node.EventType.TOUCH_MOVE, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.mePlayer.rotateGun(touchPos);
        }, this.bombClickNode);

        this.bombClickNode.on(Node.EventType.TOUCH_END, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.bombClickPath = touchPos;
            this.mePlayer.rotateGun(touchPos);
            let velocity = Utils.degreesToVec2(this.mePlayer.gunRotate.angle);

            var payload = {
                i: `${Configs.Login.AccountID}:${Configs.Login.PortalID}`,
                gi: 101,
                vx: velocity.x,
                vy: velocity.y,
            }

            var data = [];
            data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.DropBomb);
            data.push(SortiePhotonClient.EParameterCodes.DropBombMessage, JSON.stringify(payload));

            SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);

            this.bombClickNode.active = false;
        }, this.bombClickNode);
    }

    showToast(message: string, autoHide: boolean = true) {
        this.toast.node.active = true;
        this.toast.string = message;
        if (autoHide) {
            this.scheduleOnce(() => {
                this.toast.node.active = false;
            }, 2);
        }
    }

    moveNodeToPlayer(node: Node, player: Player) {
        const worldPos = player.avatarNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);

        const parent = node.parent;
        const localPos = parent.getComponent(UITransform).convertToNodeSpaceAR(worldPos);

        tween(node)
            .to(1, { position: new Vec3(localPos.x, localPos.y, 0) })
            .call(() => {
                node.destroy();
            })
            .start();
    }

    toggleAutoPlay(event: any) {
        var target = event.target;
        target.getChildByName('on').active = !target.getChildByName('on').active;
        target.getChildByName('off').active = !target.getChildByName('off').active;
    }

    updateJackpotObject(jackpot: any, percent: any) {
        if (typeof jackpot === "number" && !isNaN(jackpot)) {
            this.currentJackpot = jackpot;
        }

        if (typeof percent === "number" && !isNaN(percent)) {
            this.currentJackpotPercent = percent;
        }

        // Tween.numberTo(this.lblJackpot, this.currentJackpot, 0.3);
        this.lblPercentJackpot.string = this.currentJackpotPercent * 100 + "%";
        var jackpotValue = Math.floor(this.currentJackpot * this.currentJackpotPercent);
        // Tween.numberTo(this.lblJackpotPerGun, jackpotValue, 0.3);
    }

    actChat() {
        let popupChat = instantiate(this.popupChatPrefab);
        this.popupContainer.addChild(popupChat);
        // @ts-ignore
        popupChat.getComponent("XuatKich.PopupChat").showDetail(this.dataChat);
    }

    @property(Node)
    private autoBtnNode: Node = null;
    @property(Node)
    private chatBtnNode: Node = null;
    toggleBtnChatAndAuto() {
        const duration = 0.5;

        const hideTween = (node: Node) => {
            const uiOpacity = node.getComponent(UIOpacity);
            if (!uiOpacity) return;

            tween(node)
                .to(duration, { position: v3(0, 0) })
                .to(0, {}, {
                    onUpdate: () => { uiOpacity.opacity = 0; },
                })
                .call(() => {
                    node.active = false;
                })
                .start();
        };

        const showTween = (node: Node, pos: Vec3) => {
            const uiOpacity = node.getComponent(UIOpacity);
            if (!uiOpacity) return;

            node.setPosition(v3(0, 0));
            uiOpacity.opacity = 0;
            node.active = true;

            tween(node)
                .to(duration, { position: pos }, {
                    onUpdate: () => {
                        uiOpacity.opacity = Math.min(uiOpacity.opacity + 10, 255);
                    },
                })
                .call(() => {
                    uiOpacity.opacity = 255;
                })
                .start();
        };

        const isActive = this.autoBtnNode.active && this.chatBtnNode.active;

        if (isActive) {
            hideTween(this.autoBtnNode);
            hideTween(this.chatBtnNode);
        } else {
            showTween(this.autoBtnNode, v3(-140, 190, 0));
            showTween(this.chatBtnNode, v3(140, 190, 0));
        }
    }

    @property([Node])
    emotionInGames: Node[] = [];
    toggleEmotionInGame(_event: any, data: number) {
        this.emotionInGames[data - 1].active = !this.emotionInGames[data - 1].active;
    }

    sendEmotionInGame(_event: any, dataStr: string) {
        const [localPos, emotion] = dataStr.split("__");
        var player = this.getPlayerByLocalPos(parseInt(localPos) + 1);
        var payload = {
            c: `__EIG__${player.id}__${emotion}`
        }

        var data = [];
        data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.Chat);
        data.push(SortiePhotonClient.EParameterCodes.ChatMessage, JSON.stringify(payload));
        SortiePhotonClient.getInstance().sendOperation(SortiePhotonClient.EOperationCodes.Game, data, true);
    }

    displayEmotion(formId: string, toId: string, emotionId: number) {
        const player = this.getPlayerById(formId);
        const toPlayer = this.getPlayerById(toId);
        if (!player || !toPlayer) return;

        const node = new Node();
        const sprite = node.addComponent(Sprite);
        sprite.spriteFrame = this.sprEmotionInGames[emotionId - 1];
        node.position = v3(0, 0, 0);
        node.setScale(2, 2, 2);
        node.active = true;

        player.avatarNode.addChild(node);
        this.moveNodeToPlayer(node, toPlayer);
    }
}

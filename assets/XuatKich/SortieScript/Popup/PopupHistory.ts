import { _decorator, Label, Node, instantiate, sys, Color, Button } from "cc";
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import {SortiePhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SortiePhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import Configs from "db://assets/Lobby/scripts/common/Config";

const { ccclass, property, menu } = _decorator;

enum EHistoryType {
    Shooting = 0,
    FighterDead = 1,
    SupplyChestDead = 2,
    AmmunitionChestDead = 3,
    BossDead = 4,
    Magnetic = 5,
    Bomb = 6
}

const EHistoryTypeText: { [key: number]: { vi: string; en: string; id: string; km: string; th: string; zh: string } } = {
    [EHistoryType.Shooting]: {
        vi: "Bắn súng",
        en: "Shooting",
        id: "<PERSON><PERSON><PERSON><PERSON>",
        km: "បាញ់ប្រហារ",
        th: "ยิงปืน",
        zh: "射击"
    },
    [EHistoryType.FighterDead]: {
        vi: "Máy bay bị tiêu diệt",
        en: "Fighter destroyed",
        id: "Pesawat tempur dihancurkan",
        km: "យន្តហោះត្រូវបានផ្ដាច់ជីវិត",
        th: "เครื่องบินถูกทำลาย",
        zh: "战斗机被摧毁"
    },
    [EHistoryType.SupplyChestDead]: {
        vi: "Hòm tiếp tế bị phá hủy",
        en: "Supply chest destroyed",
        id: "Peti suplai dihancurkan",
        km: "ប្រអប់បញ្ជូនត្រូវបានបំផ្លាញ",
        th: "กล่องเสบียงถูกทำลาย",
        zh: "补给箱被摧毁"
    },
    [EHistoryType.AmmunitionChestDead]: {
        vi: "Hòm đạn bị phá hủy",
        en: "Ammunition chest destroyed",
        id: "Peti amunisi dihancurkan",
        km: "ប្រអប់កាំជ្រួចត្រូវបានបំផ្លាញ",
        th: "กล่องกระสุนถูกทำลาย",
        zh: "弹药箱被摧毁"
    },
    [EHistoryType.BossDead]: {
        vi: "Boss bị tiêu diệt",
        en: "Boss defeated",
        id: "Boss dikalahkan",
        km: "មេឃ្លាំងត្រូវបានផ្ដាច់ជីវិត",
        th: "บอสถูกกำจัด",
        zh: "Boss 被击败"
    },
    [EHistoryType.Magnetic]: {
        vi: "Hút từ tính",
        en: "Magnet",
        id: "Magnet",
        km: "ម៉ាញេទិច",
        th: "แม่เหล็ก",
        zh: "磁力吸取"
    },
    [EHistoryType.Bomb]: {
        vi: "Nổ bom",
        en: "Bomb explosion",
        id: "Ledakan bom",
        km: "ផ្ទុះគ្រាប់បូម",
        th: "ระเบิด",
        zh: "炸弹爆炸"
    }
};

enum EHistoryTab {
    GamePlayHistory = 0,
    CashInHistory = 1,
    CashOutHistory = 2
}

enum EPaginationType {
    GamePlayHistory = 0,
    CashInHistory = 1,
    CashOutHistory = 2
}

@ccclass
@menu("Sortie/PopupHistory")
export default class PopupHistory extends Dialog {

    @property([Node])
    tabActiveIcons: Node[] = [];
    @property([Node])
    tabInactiveIcons: Node[] = [];
    @property([Node])
    tabContents: Node[] = [];
    @property([Label])
    tabLabels: Label[] = [];

    @property(Node)
    listItemGamePlayHistory: Node = null;
    @property(Node)
    itemGamePlayHistory: Node = null;
    @property(Node)
    pageButtonPrefab: Node = null;
    @property(Node)
    paginationContainer: Node = null;

    @property(Node)
    listItemCashInHistory: Node = null;
    @property(Node)
    itemCashInHistory: Node = null;
    @property(Node)
    pageButtonPrefabCashIn: Node = null;
    @property(Node)
    paginationContainerCashIn: Node = null;

    @property(Node)
    listItemCashOutHistory: Node = null;
    @property(Node)
    itemCashOutHistory: Node = null;
    @property(Node)
    pageButtonPrefabCashOut: Node = null;
    @property(Node)
    paginationContainerCashOut: Node = null;

    private currentIndex: number = 0;
    private currentPage: number = 1;
    private totalPages: number = 1;
    private historyData: any[] = [];
    private pageSize: number = 8;

    private currentPageCashIn: number = 1;
    private totalPagesCashIn: number = 1;
    private historyDataCashIn: any[] = [];

    private currentPageCashOut: number = 1;
    private totalPagesCashOut: number = 1;
    private historyDataCashOut: any[] = [];

    start() {
        this.listenerDataGamePlayHistory();
    }

    show() {
        super.show();
        this.sendDataGamePlayHistory();
    }

    private showHistory(data: any[], page: number, listItem: Node, itemPrefab: Node, paginationContainer: Node, currentPageRef: number, totalPagesRef: number, updatePaginationFunc: Function, historyType: EHistoryTab) {
        const startIdx = (page - 1) * this.pageSize;
        const endIdx = startIdx + this.pageSize;
        const paginatedData = data.slice(startIdx, endIdx);

        listItem.removeAllChildren();

        paginatedData.forEach((itemData) => {
            const item = instantiate(itemPrefab);
            item.parent = listItem;
            item.active = true;

            const lblTime = item.getChildByName("time")?.getComponent(Label);
            if (lblTime) lblTime.string = this.formatDateTime(itemData.CreatedTime || itemData.ct);

            var langCode = sys.localStorage.getItem("langCode") || "vi";
            switch (historyType) {
                case EHistoryTab.GamePlayHistory:
                    const lblRoomId = item.getChildByName("room")?.getComponent(Label);
                    const lblBetAmount = item.getChildByName("b")?.getComponent(Label);
                    const lblDes = item.getChildByName("des")?.getComponent(Label);

                    var amount = itemData.cr - itemData.b;
                    var amountText = amount.toLocaleString("vi-VN");
                    if (amount > 0) {
                        amountText = "+" + amountText
                    }
                    if (lblRoomId) lblRoomId.string = itemData.ri.toString();
                    if (lblBetAmount) lblBetAmount.string = amountText;
                    if (lblDes) lblDes.string = EHistoryTypeText[itemData.t][langCode] ?? "";
                    break;

                case EHistoryTab.CashInHistory:
                case EHistoryTab.CashOutHistory:
                    const lblAmount = item.getChildByName("amount")?.getComponent(Label);
                    const lblBalance = item.getChildByName("balance")?.getComponent(Label);
                    const lblDesCash = item.getChildByName("des")?.getComponent(Label);

                    if (lblAmount) lblAmount.string = (parseInt(itemData.Amount)).toLocaleString("vi-VN");
                    if (lblBalance) lblBalance.string = itemData.Balance.toLocaleString("vi-VN");
                    if (lblDesCash) lblDesCash.string = historyType == EHistoryTab.CashInHistory ? App.instance.getTextLang('BUY_GEM') : App.instance.getTextLang('BUY_GAM');
                    break;
            }
        });

        updatePaginationFunc.call(this);
    }

    showGamePlayHistory(data: any[], page: number = 1) {
        this.historyData = data;
        this.totalPages = Math.min(10, Math.ceil(data.length / this.pageSize));
        this.currentPage = page;
        this.showHistory(data, page, this.listItemGamePlayHistory, this.itemGamePlayHistory, this.paginationContainer, this.currentPage, this.totalPages, this.updatePaginationButtonsGamePlay.bind(this), EHistoryTab.GamePlayHistory);
    }

    showCashInHistory(data: any[], page: number = 1) {
        this.historyDataCashIn = data;
        this.totalPagesCashIn = Math.min(10, Math.ceil(data.length / this.pageSize));
        this.currentPageCashIn = page;
        this.showHistory(data, page, this.listItemCashInHistory, this.itemCashInHistory, this.paginationContainerCashIn, this.currentPageCashIn, this.totalPagesCashIn, this.updatePaginationButtonsCashIn.bind(this), EHistoryTab.CashInHistory);
    }

    showCashOutHistory(data: any[], page: number = 1) {
        this.historyDataCashOut = data;
        this.totalPagesCashOut = Math.min(10, Math.ceil(data.length / this.pageSize));
        this.currentPageCashOut = page;
        this.showHistory(data, page, this.listItemCashOutHistory, this.itemCashOutHistory, this.paginationContainerCashOut, this.currentPageCashOut, this.totalPagesCashOut, this.updatePaginationButtonsCashOut.bind(this), EHistoryTab.CashOutHistory);
    }

    formatDateTime(dateTimeStr: string): string {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = ("0" + (date.getMonth() + 1)).slice(-2);
        const day = ("0" + date.getDate()).slice(-2);
        const hours = ("0" + date.getHours()).slice(-2);
        const minutes = ("0" + date.getMinutes()).slice(-2);
        const seconds = ("0" + date.getSeconds()).slice(-2);
        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    private updatePaginationButtons(paginationContainer: Node, currentPage: number, totalPages: number, goToPageFunc: Function, paginationType: EPaginationType) {
        paginationContainer.removeAllChildren();

        const maxButtons = 5;
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            this.createPageButton(paginationContainer, 1, currentPage, goToPageFunc, paginationType);
            if (startPage > 2) this.createDots(paginationContainer);
        }

        for (let i = startPage; i <= endPage; i++) {
            this.createPageButton(paginationContainer, i, currentPage, goToPageFunc, paginationType);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) this.createDots(paginationContainer);
            this.createPageButton(paginationContainer, totalPages, currentPage, goToPageFunc, paginationType);
        }
    }

    private updatePaginationButtonsGamePlay() {
        this.updatePaginationButtons(this.paginationContainer, this.currentPage, this.totalPages, this.goToPage.bind(this), EPaginationType.GamePlayHistory);
    }

    private updatePaginationButtonsCashIn() {
        this.updatePaginationButtons(this.paginationContainerCashIn, this.currentPageCashIn, this.totalPagesCashIn, this.goToPageCashIn.bind(this), EPaginationType.CashInHistory);
    }

    private updatePaginationButtonsCashOut() {
        this.updatePaginationButtons(this.paginationContainerCashOut, this.currentPageCashOut, this.totalPagesCashOut, this.goToPageCashOut.bind(this), EPaginationType.CashOutHistory);
    }

    private createPageButton(paginationContainer: Node, page: number, currentPage: number, goToPageFunc: Function, paginationType: EPaginationType) {
        let pageButtonPrefab: Node;

        switch (paginationType) {
            case EPaginationType.GamePlayHistory:
                pageButtonPrefab = this.pageButtonPrefab;
                break;
            case EPaginationType.CashInHistory:
                pageButtonPrefab = this.pageButtonPrefabCashIn;
                break;
            case EPaginationType.CashOutHistory:
                pageButtonPrefab = this.pageButtonPrefabCashOut;
                break;
            default:
                return;
        }

        const pageButton = instantiate(pageButtonPrefab);
        if (!pageButton) return;

        pageButton.parent = paginationContainer;

        const label = pageButton.getChildByName("text")?.getComponent(Label);
        if (label) label.string = page.toString();

        const bgActive = pageButton.getChildByName("ButtonPageActive");
        const bgNotActive = pageButton.getChildByName("ButtonPageNotActive");

        const isActive = page === currentPage;
        if (bgActive) bgActive.active = isActive;
        if (bgNotActive) bgNotActive.active = !isActive;

        if (!pageButton.getComponent(Button)) {
            pageButton.addComponent(Button);
        }

        pageButton.on("click", () => goToPageFunc(page), this);
    }

    private createDots(paginationContainer: Node) {
        const dots = new Node("Dots");
        const label = dots.addComponent(Label);
        label.string = "...";
        dots.parent = paginationContainer;
    }

    goToPage(page: number) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;
        this.currentPage = page;
        this.showGamePlayHistory(this.historyData, this.currentPage);
    }

    goToPageCashIn(page: number) {
        if (page < 1 || page > this.totalPagesCashIn || page === this.currentPageCashIn) return;
        this.currentPageCashIn = page;
        this.showCashInHistory(this.historyDataCashIn, this.currentPageCashIn);
    }

    goToPageCashOut(page: number) {
        if (page < 1 || page > this.totalPagesCashOut || page === this.currentPageCashOut) return;
        this.currentPageCashOut = page;
        this.showCashOutHistory(this.historyDataCashOut, this.currentPageCashOut);
    }

    listenerDataGamePlayHistory() {
        SortiePhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Statistic, (res) => {
            if (res.errCode < 0) {
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            const keys = Object.keys(res.vals);

            keys.forEach(key => {
                const code = parseInt(key);
                let data = res.vals[key];

                if (code !== SortiePhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (error) {
                        return;
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.PlayHistoryResponse) {
                    this.currentPage = 1;
                    this.showGamePlayHistory(data, this.currentPage);
                }

                if (code === SortiePhotonClient.EParameterCodes.ExChangeHistoryResponse) {
                    this.showCashInHistory(data, this.currentPageCashIn);
                }
            });
        });
    }

    listenerDataCashOutHistory() {
        SortiePhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Statistic, (res) => {
            if (res.errCode < 0) {
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            const keys = Object.keys(res.vals);

            keys.forEach(key => {
                const code = parseInt(key);
                let data = res.vals[key];

                if (code !== SortiePhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (error) {
                        return;
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.ExChangeHistoryResponse) {
                    this.showCashOutHistory(data, this.currentPageCashOut);
                }
            });
        });
    }

    sendDataCashOutHistory() {
        this.listItemCashOutHistory.removeAllChildren();
        this.paginationContainerCashOut.removeAllChildren();
        const data = [
            SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.CashOutHistory,
            SortiePhotonClient.EParameterCodes.AccountId, Configs.Login.AccountID,
            SortiePhotonClient.EParameterCodes.PortalId, Configs.Login.PortalID,
            SortiePhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID
        ];
        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Statistic, data, true);
    }

    sendDataCashInHistory() {
        this.listItemCashInHistory.removeAllChildren();
        this.paginationContainerCashIn.removeAllChildren();
        const data = [
            SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.CashInHistory,
            SortiePhotonClient.EParameterCodes.AccountId, Configs.Login.AccountID,
            SortiePhotonClient.EParameterCodes.PortalId, Configs.Login.PortalID,
            SortiePhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID
        ];
        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Statistic, data, true);
    }

    sendDataGamePlayHistory() {
        this.listItemGamePlayHistory.removeAllChildren();
        this.paginationContainer.removeAllChildren();
        const data = [
            SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.PlayGameHistory,
            SortiePhotonClient.EParameterCodes.AccountId, Configs.Login.AccountID,
            SortiePhotonClient.EParameterCodes.PortalId, Configs.Login.PortalID,
            SortiePhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID
        ];
        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Statistic, data, true);
    }

    onTabSelected(event: Event, customIndex: string) {
        const index = parseInt(customIndex);
        if (this.currentIndex === index) return;

        if (index === EHistoryTab.CashInHistory) {
            this.sendDataCashInHistory();
            this.listenerDataGamePlayHistory();
        } else if (index === EHistoryTab.CashOutHistory) {
            this.sendDataCashOutHistory();
            this.listenerDataCashOutHistory();
        }

        this.updateTabState(index);
    }

    updateTabState(selectedIndex: number) {
        this.tabActiveIcons.forEach((icon, index) => icon.active = index === selectedIndex);
        this.tabInactiveIcons.forEach((icon, index) => icon.active = index !== selectedIndex);
        this.tabContents.forEach((content, index) => content.active = index === selectedIndex);
        this.tabLabels.forEach((label, index) => label.color = index === selectedIndex ? Color.WHITE : new Color(144, 141, 190));

        this.currentIndex = selectedIndex;
    }
}
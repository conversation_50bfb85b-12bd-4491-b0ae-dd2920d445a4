import { _decorator, Node, EditBox, instantiate, Label, RichText } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {SortiePhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SortiePhotonClient";
import App from "db://assets/Lobby/scripts/common/App";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import Play from "db://assets/XuatKich/SortieScript/Play";
import Configs from "db://assets/Lobby/scripts/common/Config";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Sortie/PopupChat")
export default class XuatKichPopupChat extends Dialog {

    @property(Node)
    listMessage: Node = null;
    @property(Node)
    templateMessage: Node = null;
    @property(Node)
    listFast: Node = null;
    @property(Node)
    templateFast: Node = null;
    @property(EditBox)
    edbMessage: EditBox = null;
    @property(Node)
    listEmotion: Node = null;

    fastMessages = [
        "TLN_BTN_FASTCHAT_1",
        "TLN_BTN_FASTCHAT_2",
        "TLN_BTN_FASTCHAT_3",
        "TLN_BTN_FASTCHAT_4",
        "TLN_BTN_FASTCHAT_5",
        "TLN_BTN_FASTCHAT_6",
    ];

    start () {
        SortiePhotonClient.getInstance().addResponseListener(SortiePhotonClient.EOperationCodes.Game, (res: any) => {
           if (res.errCode < 0) {
               App.instance.alertDialog.showMsg(PhotonClient.getErrMsg(res.errCode));
               return;
           }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code != SortiePhotonClient.EParameterCodes.OperationSubCode) {
                    data = JSON.parse(data);
                }

                if (code == SortiePhotonClient.EParameterCodes.ChatResponse) {
                    this.handleData(data);
                }
            });
        });

        this.listFast.removeAllChildren();
        this.fastMessages.forEach((msgKey) => {
            var item = instantiate(this.templateFast);
            item.active = true;
            item.getComponentInChildren(Label).string = App.instance.getTextLang(msgKey);
            item.on(Node.EventType.TOUCH_END, () => {
                this.edbMessage.string = App.instance.getTextLang(msgKey);
            });
            this.listFast.addChild(item);
        });
    }

    handleData(data: any) {
        if (data.c.includes("__E__") || data.c.includes("__EIG__")) return;

        var player = Play.instance.getPlayerById(data.f);
        if (player) {
            var msgNode = instantiate(this.templateMessage);
            msgNode.active = true;
            if (data.f === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                msgNode.getComponent(RichText).string = `<color=#fff600>${player.nickname}: </c><color=#ffffff>${data.c}</color>`;
            } else {
                msgNode.getComponent(RichText).string = `<color=#3c91e6>${player.nickname}: </c><color=#ffffff>${data.c}</color>`;
            }
            msgNode.parent = this.listMessage;
        }
    }

    actSubmit() {
        if (this.edbMessage.string.trim() === "") return;
        this.sendMessage(this.edbMessage.string);
        this.edbMessage.string = "";
    }

    sendMessage(message: string) {
        var payload = {
            c: message,
        }

        var data = [];
        data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.Chat);
        data.push(SortiePhotonClient.EParameterCodes.ChatMessage, JSON.stringify(payload));
        SortiePhotonClient.getInstance().sendOperation(SortiePhotonClient.EOperationCodes.Game, data, true);
    }

    showDetail(data: any) {
        data.forEach((item: any) => {
            this.handleData(item);
        })
        super.show();
    }

    toggleListEmotion() {
        this.listEmotion.active = !this.listEmotion.active;
    }

    sendEmotion(_event: any, data: string) {
        this.sendMessage(data);
        this.listEmotion.active = false;
        this.dismiss();
    }
}

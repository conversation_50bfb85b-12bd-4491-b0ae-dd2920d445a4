import { _decorator, Node, Sprite, instantiate, Label, EditBox, UIOpacity } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {SortiePhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SortiePhotonClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import Lobby from "db://assets/XuatKich/SortieScript/Lobby";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Sortie/PopupExchangeCoin")
export default class PopupExchangeCoin extends Dialog {

    @property(Sprite)
    sprCaptcha: Sprite = null;
    @property(Node)
    listItems: Node = null;
    @property(Node)
    itemTemplate: Node = null;
    @property(EditBox)
    edbCaptcha: EditBox = null;
    @property(EditBox)
    edbAmount: EditBox = null;

    isCashOut = false;
    verifyCode = "";
    cashOutMin = 0;
    cashInMin = 0;

    start () {
        var data = [
            SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.CashoutMin,
            SortiePhotonClient.EParameterCodes.PortalId, Configs.Login.PortalID,
        ];
        SortiePhotonClient.getInstance().sendOperation(SortiePhotonClient.EOperationCodes.Shop, data, true);

        SortiePhotonClient.getInstance().addResponseListener(SortiePhotonClient.EOperationCodes.Shop, (res: any) => {
           if (res.errCode < 0) {
               App.instance.alertDialog.showMsg(PhotonClient.getErrMsg(res.errCode));
               return;
           }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code != SortiePhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (e) {}
                }

                if (code == SortiePhotonClient.EParameterCodes.CaptchaResponse) {
                    this.verifyCode = data[0];
                    Utils.loadSpriteFrameFromBase64(data[1], (sprFrame) => {
                        this.sprCaptcha.spriteFrame = sprFrame;
                    });
                }

                if (code == SortiePhotonClient.EParameterCodes.CashInResponse) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_success'));
                    Lobby.instance.updateBalance(data.go, data.ge);
                    this.dismiss();
                }

                if (code == SortiePhotonClient.EParameterCodes.CashOutResponse) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_success'));
                    Lobby.instance.updateBalance(data.go, data.ge);
                    this.dismiss();
                }

                if (code == SortiePhotonClient.EParameterCodes.CashoutMinResponse) {
                    this.cashOutMin = JSON.parse(data.value);
                }
            });
        });
    }

    show() {
        this.updateSA();
    }

    clearEdb() {
        this.edbAmount.string = "";
        this.edbCaptcha.string = "";
    }

    updateSA() {
        this.isCashOut = false;
        var dataSA = Lobby.instance.clientParameterConfig.sa;
        this.cashInMin = dataSA[0];
        this.listItems.removeAllChildren();
        for (let i = 0; i < dataSA.length; i++) {
            const item = instantiate(this.itemTemplate);
            item.active = true;
            item.getChildByName('text').getComponent(Label).string = Utils.formatNumber(dataSA[i]);
            item.parent = this.listItems;

            item.on(Node.EventType.TOUCH_END, () => {
                this.edbAmount.string = dataSA[i].toString();
            });
        }

        this.edbAmount.string = "";
        this.edbCaptcha.string = "";
    }

    updateSAO() {
        this.isCashOut = true;
        this.listItems.removeAllChildren();
        var dataSAO = Lobby.instance.clientParameterConfig.sao;
        for (let i = 0; i < dataSAO.length; i++) {
            const item = instantiate(this.itemTemplate);
            item.active = true;
            item.getChildByName('text').getComponent(Label).string = Utils.formatNumber(dataSAO[i]);
            item.parent = this.listItems;

            if (dataSAO[i] < this.cashOutMin) {
                item.getComponent(UIOpacity).opacity = 100;
            } else {
                item.on(Node.EventType.TOUCH_END, () => {
                    this.edbAmount.string = dataSAO[i].toString();
                });
            }
        }

        this.edbAmount.string = "";
        this.edbCaptcha.string = "";
    }

    actSubmit() {
        var amount = parseInt(this.edbAmount.string);
        if (this.isCashOut && amount < this.cashOutMin) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('fish_err999001').replace("{0}", Utils.formatNumber(this.cashOutMin)));
            return;
        } else if (amount < this.cashInMin) {
            App.instance.alertDialog.showMsg(App.instance.getTextLang('TLN_MIN_GOLD_TO_CHANGE').replace("{0}", Utils.formatNumber(this.cashInMin)));
            return;
        }

        var payload = {
            a: amount,
            c: this.edbCaptcha.string,
            v: this.verifyCode
        }

        var data = [];
        data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, this.isCashOut ? SortiePhotonClient.EOperationSubCodes.CashOutGold : SortiePhotonClient.EOperationSubCodes.CashInGold);
        data.push(SortiePhotonClient.EParameterCodes.ExchangeMessage, JSON.stringify(payload));
        SortiePhotonClient.getInstance().sendOperation(SortiePhotonClient.EOperationCodes.Shop, data, true);
    }
}

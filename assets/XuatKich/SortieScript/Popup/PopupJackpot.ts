import { _decorator, Node, Label, instantiate, Button, Color, Event } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {SortiePhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/SortiePhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";

const { ccclass, property } = _decorator;

enum EHistoryTab {
    TopPlayer = 0,
    TopJackpot = 1
}

enum EPaginationType {
    TopPlayer = 0,
    TopJackpot = 1
}

@ccclass
export default class PopupJackpot extends Dialog {
    @property([Node])
    tabActiveIcons: Node[] = [];
    @property([Node])
    tabInactiveIcons: Node[] = [];
    @property([Node])
    tabContents: Node[] = [];
    @property([Label])
    tabLabels: Label[] = [];

    @property(Node)
    listItemTopPlayer: Node = null;
    @property(Node)
    itemTopPlayer: Node = null;
    @property(Node)
    pageButtonPrefabTopPlayer: Node = null;
    @property(Node)
    paginationContainerTopPlayer: Node = null;

    @property(Node)
    listItemTopJackpot: Node = null;
    @property(Node)
    itemTopJackpot: Node = null;
    @property(Node)
    pageButtonPrefabTopJackpot: Node = null;
    @property(Node)
    paginationContainerTopJackpot: Node = null;

    private currentIndex: number = 0;

    private currentPage: number = 1;
    private totalPages: number = 1;
    private topPlayerData: any[] = [];

    private currentPageTopJackpot: number = 1;
    private totalPagesTopJackpot: number = 1;
    private topJackpotData: any[] = [];
    private pageSize: number = 8;

    start() {
        this.listenerDataTopPlayer();
        this.listenerDataTopJackpot();
    }

    listenerDataTopPlayer() {
        SortiePhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Statistic, (res) => {
            if (res.errCode < 0) {
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            const keys = Object.keys(res.vals);

            keys.forEach(key => {
                const code = parseInt(key);
                let data = res.vals[key];

                if (code !== SortiePhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (error) {
                        return;
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.TopJackpotResponse) {
                    this.currentPage = 1;
                    this.showTopPlayer(data, this.currentPage);
                }
            });
        });
    }

    listenerDataTopJackpot() {
        SortiePhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Statistic, (res) => {
            if (res.errCode < 0) {
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            const keys = Object.keys(res.vals);

            keys.forEach(key => {
                const code = parseInt(key);
                let data = res.vals[key];

                if (code !== SortiePhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (error) {
                        return;
                    }
                }

                if (code === SortiePhotonClient.EParameterCodes.TopJackpotResponse) {
                    this.currentPageTopJackpot = 1;
                    this.showTopJackpot(data, this.currentPageTopJackpot);
                }
            });
        });
    }

    private showHistory(data: any[], page: number, listItem: Node, itemPrefab: Node, paginationContainer: Node, currentPageRef: number, totalPagesRef: number, updatePaginationFunc: Function, historyType: EHistoryTab) {
        const startIdx = (page - 1) * this.pageSize;
        const endIdx = startIdx + this.pageSize;
        const paginatedData = data.slice(startIdx, endIdx);

        listItem.removeAllChildren();

        paginatedData.forEach((itemData, index) => {
            const item = instantiate(itemPrefab);
            item.parent = listItem;
            item.active = true;

            switch (historyType) {
                case EHistoryTab.TopPlayer:
                    const lblRank = item.getChildByName("rank")?.getComponent(Label);
                    const lblName = item.getChildByName("userName")?.getComponent(Label);
                    const lblAmount = item.getChildByName("win")?.getComponent(Label);

                    if (lblRank) lblRank.string = `${startIdx + index + 1}`;
                    if (lblName) lblName.string = itemData.Username || "Unknown";
                    if (lblAmount) {
                        const amount = parseFloat(itemData.PrizeValue);
                        if (!isNaN(amount)) {
                            lblAmount.string = amount.toLocaleString("vi-VN");
                        } else {
                            lblAmount.string = "0";
                        }
                    }
                    break;

                case EHistoryTab.TopJackpot:
                    const lblTime = item.getChildByName("time")?.getComponent(Label);
                    const lblJackpotName = item.getChildByName("userName")?.getComponent(Label);
                    const lblJackpotAmount = item.getChildByName("win")?.getComponent(Label);
                    const lblJackpotDes = item.getChildByName("des")?.getComponent(Label);


                    if (lblTime) lblTime.string = this.formatDateTime(itemData.CreatedTime);
                    if (lblJackpotName) lblJackpotName.string = itemData.Nickname || "Unknown";
                    if (lblJackpotAmount) {
                        const amount = parseFloat(itemData.PrizeValue);
                        if (!isNaN(amount)) {
                            lblJackpotAmount.string = amount.toLocaleString("vi-VN");
                        } else {
                            lblJackpotAmount.string = "0";
                        }
                    }
                    if (lblJackpotDes) lblJackpotDes.string = "Jackpot";
                    break;
            }
        });

        updatePaginationFunc.call(this);
    }

    formatDateTime(dateTimeStr: string): string {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = ("0" + (date.getMonth() + 1)).slice(-2);
        const day = ("0" + date.getDate()).slice(-2);
        const hours = ("0" + date.getHours()).slice(-2);
        const minutes = ("0" + date.getMinutes()).slice(-2);
        const seconds = ("0" + date.getSeconds()).slice(-2);
        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    showTopPlayer(data: any[], page: number = 1) {
        this.topPlayerData = data;
        this.totalPages = Math.min(10, Math.ceil(data.length / this.pageSize));
        this.currentPage = page;
        this.showHistory(data, page, this.listItemTopPlayer, this.itemTopPlayer, this.paginationContainerTopPlayer, this.currentPage, this.totalPages, this.updatePaginationButtonsTopPlayer.bind(this), EHistoryTab.TopPlayer);
    }

    showTopJackpot(data: any[], page: number = 1) {
        this.topJackpotData = data;
        this.totalPagesTopJackpot = Math.min(10, Math.ceil(data.length / this.pageSize));
        this.currentPageTopJackpot = page;
        this.showHistory(data, page, this.listItemTopJackpot, this.itemTopJackpot, this.paginationContainerTopJackpot, this.currentPageTopJackpot, this.totalPagesTopJackpot, this.updatePaginationButtonsTopJackpot.bind(this), EHistoryTab.TopJackpot);
    }

    private updatePaginationButtonsTopPlayer() {
        this.updatePaginationButtons(this.paginationContainerTopPlayer, this.currentPage, this.totalPages, this.goToPage.bind(this), EPaginationType.TopPlayer);
    }

    private updatePaginationButtonsTopJackpot() {
        this.updatePaginationButtons(this.paginationContainerTopJackpot, this.currentPageTopJackpot, this.totalPagesTopJackpot, this.goToPageTopJackpot.bind(this), EPaginationType.TopJackpot);
    }

    private updatePaginationButtons(paginationContainer: Node, currentPage: number, totalPages: number, goToPageFunc: Function, paginationType: EPaginationType) {
        paginationContainer.removeAllChildren();

        const maxButtons = 5;
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            this.createPageButton(paginationContainer, 1, currentPage, goToPageFunc, paginationType);
            if (startPage > 2) this.createDots(paginationContainer);
        }

        for (let i = startPage; i <= endPage; i++) {
            this.createPageButton(paginationContainer, i, currentPage, goToPageFunc, paginationType);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) this.createDots(paginationContainer);
            this.createPageButton(paginationContainer, totalPages, currentPage, goToPageFunc, paginationType);
        }
    }

    goToPage(page: number) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;
        this.currentPage = page;
        this.showTopPlayer(this.topPlayerData, this.currentPage);
    }

    goToPageTopJackpot(page: number) {
        if (page < 1 || page > this.totalPagesTopJackpot || page === this.currentPageTopJackpot) return;
        this.currentPageTopJackpot = page;
        this.showTopJackpot(this.topJackpotData, this.currentPageTopJackpot);
    }

    private createPageButton(paginationContainer: Node, page: number, currentPage: number, goToPageFunc: Function, paginationType: EPaginationType) {
        let pageButtonPrefab: Node;

        switch (paginationType) {
            case EPaginationType.TopPlayer:
                pageButtonPrefab = this.pageButtonPrefabTopPlayer;
                break;
            case EPaginationType.TopJackpot:
                pageButtonPrefab = this.pageButtonPrefabTopJackpot;
                break;
            default:
                return;
        }

        const pageButton = instantiate(pageButtonPrefab);
        if (!pageButton) return;

        pageButton.parent = paginationContainer;

        const label = pageButton.getChildByName("text")?.getComponent(Label);
        if (label) label.string = page.toString();

        const bgActive = pageButton.getChildByName("ButtonPageActive");
        const bgNotActive = pageButton.getChildByName("ButtonPageNotActive");

        const isActive = page === currentPage;
        if (bgActive) bgActive.active = isActive;
        if (bgNotActive) bgNotActive.active = !isActive;

        if (!pageButton.getComponent(Button)) {
            pageButton.addComponent(Button);
        }

        pageButton.on("click", () => goToPageFunc(page), this);
    }

    private createDots(paginationContainer: Node) {
        const dots = new Node("Dots");
        const label = dots.addComponent(Label);
        label.string = "...";
        dots.parent = paginationContainer;
    }

    sendDataTopPlayer() {
        this.listItemTopPlayer.removeAllChildren();
        this.paginationContainerTopPlayer.removeAllChildren();
        const data = [
            SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.GetTopPlayer,
            SortiePhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID
        ];
        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Statistic, data, true);
    }

    sendDataTopJackpot() {
        this.listItemTopJackpot.removeAllChildren();
        this.paginationContainerTopJackpot.removeAllChildren();
        const data = [
            SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.GetTopJackpot,
            SortiePhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID
        ];
        SortiePhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Statistic, data, true);
    }

    onTabSelected(event: Event, customIndex: string) {
        const index = parseInt(customIndex);
        if (this.currentIndex === index) return;

        if (index === EHistoryTab.TopPlayer) {
            this.sendDataTopPlayer();
        } else if (index === EHistoryTab.TopJackpot) {
            this.sendDataTopJackpot();
        }

        this.updateTabState(index);
    }

    updateTabState(selectedIndex: number) {
        this.tabActiveIcons.forEach((icon, index) => icon.active = index === selectedIndex);
        this.tabInactiveIcons.forEach((icon, index) => icon.active = index !== selectedIndex);
        this.tabContents.forEach((content, index) => content.active = index === selectedIndex);
        this.tabLabels.forEach((label, index) => label.color = index === selectedIndex ? Color.WHITE : new Color(144, 141, 190));

        this.currentIndex = selectedIndex;
    }

    dismiss() {
        super.dismiss();
    }

    show() {
        super.show();
        this.sendDataTopPlayer();
    }
}
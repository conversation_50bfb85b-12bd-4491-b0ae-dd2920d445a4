import { _decorator, Component, Node, Label, SpriteFrame, tween, v3, Button, Sprite } from "cc";
import App from "../../Lobby/scripts/common/App";
import { Utils } from "../../Lobby/scripts/common/Utils";
import AudioManager, { AUDIO_CLIP } from "./AudioManager";
import { TweenUtils } from "../../Lobby/scripts/common/TweenUtils";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("Ocean/OceanBonus")
export class OceanBonus extends Component {
    @property(Node) items: Node = null;
    @property(Node) miniGameItems: Node = null;

    @property(Label) lblTurn: Label = null;
    @property(Label) lblPoint: Label = null;
    @property(Label) lblCountDownTime: Label = null;

    @property(Node) miniGame: Node = null;
    @property(Node) btnQuickPlay: Node = null;

    @property(Node) completeScene: Node = null;

    @property(SpriteFrame) box: SpriteFrame = null;
    @property(SpriteFrame) boxGold: SpriteFrame = null;
    @property(SpriteFrame) boxMiniGame: SpriteFrame = null;
    @property(SpriteFrame) boxPoint: SpriteFrame = null;

    @property(SpriteFrame) miniGameBox: SpriteFrame = null;
    @property(SpriteFrame) miniGameBoxGold: SpriteFrame = null;

    private _countdownInterval: number;
    bonusData: Array<[number, number, number, number]> = [];
    onFinished: Function = null;
    totalTurn: number;
    startBonus: number;
    accumulate = 0;
    left = 0;
    time = 0;
    lockInput = false;
    lockInputMiniGame = false;
    protected onEnable(): void {
        for (let i = 0; i < this.items.children.length; i++) {
            let node = this.items.children[i];
            node["btn"] = node.getComponent(Button);
            node["label"] = node.getChildByName("label").getComponent(Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(Sprite);

            node.off("click");
            node.once("click", () => {
                if (this.lockInput) return;
                AudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                this.btnQuickPlay.active = false;
                this._clearTimer();
                const [step, rewardID, multiplier, price] = this.bonusData[this.bonusData.length - this.left];
                switch (rewardID) {
                    case 201:
                    case 202:
                    case 203:
                        this.lockInput = true;
                        this.scheduleOnce(() => {
                            this.showMiniGame(price, () => {
                                this.lockInput = false;
                                this.left--;
                                this.totalTurn--;
                                this.updateGame();
                                node['label'].node.active = true;
                                node['label'].string = Utils.formatNumber(price);
                            })
                        }, 0.5)
                        node["sprite"].spriteFrame = this.boxMiniGame;
                        break;
                    case 210:
                        this.left--;
                        this.accumulate++;
                        this.updateGame();
                        node["sprite"].spriteFrame = this.boxPoint;
                        break;
                    case 220:
                        node['label'].node.active = true;
                        node['label'].string = "0";
                        TweenUtils.numberTo(node['label'], price, 0.5);
                        this.left--;
                        this.totalTurn--;
                        this.updateGame();
                        node["sprite"].spriteFrame = this.boxGold;
                        break;
                }
            })
        }
    }

    showBonus(data, startBonus: number) {

        this.node.active = true;
        this.items.active = true;
        this.miniGame.active = false;
        this.completeScene.active = false;

        for (let i = 0; i < this.items.children.length; i++) {
            let node = this.items.children[i];

            node["btn"] = node.getComponent(Button);
            node["label"] = node.getChildByName("label").getComponent(Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(Sprite);

            node["btn"].interactable = true;
            node["label"].node.active = false;
            node["sprite"].spriteFrame = this.box;

        }

        this.bonusData = [];
        for (let d of data.split(";")) {
            this.bonusData.push(d.split(",").map(Number));
        }
        this.totalTurn = 10;
        this.lockInput = false;
        this.accumulate = 1;
        this.startBonus = startBonus;
        this.btnQuickPlay.active = true;
        this.lblPoint.node.parent.active = true;
        this.lblTurn.node.parent.active = true;
        this.left = this.bonusData.length;

        this.startCountdown(15);
        this.updateGame();
    }

    showMiniGame(prize: number, cb: Function) {
        this.miniGame.active = true;
        this.lockInputMiniGame = false;

        for (let i = 0; i < this.miniGameItems.children.length; i++) {
            let node = this.miniGameItems.children[i];

            node["btn"] = node.getComponent(Button);
            node["label"] = node.getChildByName("label").getComponent(Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(Sprite);

            node["btn"].interactable = true;
            node["label"].node.active = false;
            node["sprite"].spriteFrame = this.miniGameBox;
        }

        for (let i = 0; i < this.miniGameItems.children.length; i++) {
            let node = this.miniGameItems.children[i];
            node.off('click');
            node.once('click', () => {
                if (this.lockInputMiniGame) return;
                this.lockInputMiniGame = true;
                AudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                node["btn"].interactable = false;
                node["label"].node.active = true;
                node["label"].string = "0";
                TweenUtils.numberTo(node["label"], prize, 0.5);
                node["sprite"].spriteFrame = this.miniGameBoxGold;

                this.scheduleOnce(() => {
                    this.miniGame.active = false;
                    cb && cb();
                }, 1)
            });
        }
    }

    private startCountdown(seconds: number): void {
        this.time = seconds;
        this.lblCountDownTime.string = this.getTimeString(seconds);
        this._countdownInterval = setInterval(() => {
            this.time--;
            this.lblCountDownTime.string = this.getTimeString(this.time);
            if (this.time <= 0) {
                clearInterval(this._countdownInterval);
                this.totalTurn = 0;
                this.updateGame();
            }
        }, 1000);
    }

    private _clearTimer() {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    private getTimeString(remainTime: number): string {
        let text = App.instance.getTextLang("sl78");
        return text.replace("15", remainTime.toString());
    }

    private updateGame() {
        this.lblTurn.string = this.totalTurn.toString();
        this.lblPoint.string = this.accumulate.toString();

        if (this.totalTurn <= 0) {
            this.lockInput = true;
            this._clearTimer();
            this.scheduleOnce(() => {
                this.showCompleteBonusGame();
            }, 1)
            this.scheduleOnce(() => {
                this.node.active = false;
                this.onFinished && this.onFinished();
            }, 3)
        }
    }

    private showCompleteBonusGame() {
        AudioManager.Instance.playEffect(AUDIO_CLIP.REWARD_BONUS);
        const finalWinPrize = this.bonusData.reduce((sum, item) => sum + item[3], 0);
        this.lblPoint.node.parent.active = false;
        this.lblTurn.node.parent.active = false;
        this.completeScene.active = true;
        this.miniGame.active = false;
        this.items.active = false;

        let label = this.completeScene.getChildByName("WinBonus");
        label.getComponent(Label).string = "0";
        TweenUtils.numberTo(label.getComponent(Label), finalWinPrize, 0.5);
    }

    private onClickQuickPlay() {
        AudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
        this.btnQuickPlay.active = false;
        this._clearTimer();
        this.startCountdown(1);
    }

}

import { _decorator, Component, Node } from "cc";
import { OceanController } from "./OceanController";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Ocean/OceanGuide")
export class OceanGuide extends Component {
    @property(Node)
    private paytable1: Node = null;

    @property(Node)
    private paytable2: Node = null;

    @property(Node)
    private paytable3: Node = null;


    onLoad(){
        OceanController.getInstance().addObserver(this.node.uuid, {
            onChangeRoom: (sender: OceanController, roomID: number) => {
                this.onRoomChange(roomID);
            }
        });
    }

    onRoomChange(roomID: number): void {
        this.paytable1.active = roomID == 1;
        this.paytable2.active = roomID == 2;
        this.paytable3.active = roomID == 3;
    }

    protected onDestroy(): void {
        OceanController.getInstance().removeObserver(this.node.uuid);
    }

    //show and dismiss
    show() {
        this.node.active = true;
    }

    dismiss() {
        this.node.active = false;
    }
}

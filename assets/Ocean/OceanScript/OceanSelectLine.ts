
import { _decorator, Component, Node, Toggle } from "cc";
import AudioManager, { AUDIO_CLIP } from "./AudioManager";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("Ocean/OceanSelectLine")
export class OceanSelectLine extends Component {
    @property(Node)
    private buttonLines: Node = null;

    @property(Toggle)
    private room1: Toggle = null;

    @property(Toggle)
    private room2: Toggle = null;

    @property(Toggle)
    private room3: Toggle = null;

    private _onSelectedChanged: (selectedLines: number[]) => void = null;

    public setOnSelectedChanged(fn: (selectedLines: number[]) => void) {
        this._onSelectedChanged = fn;
    }

    protected onLoad() {
        this.buttonLines.children.forEach((btn, index) => {
            btn.on("toggle", () => {
                AudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                this.updateSelectedLines();
            });
        });
        this.updateSelectedLines();
    }

    private updateSelectedLines() {
        const selectedLines = this.getSelectedLines();
        this._onSelectedChanged?.(selectedLines);
    }

    public getSelectedLines(): number[] {
        const selectedLines: number[] = [];
        this.buttonLines.children.forEach((btn, index) => {
            if (btn.getComponent(Toggle).isChecked) {
                selectedLines.push(index + 1);
            }
        });
        return selectedLines;
    }

    private toggleLines(condition: (index: number) => boolean) {
        this.buttonLines.children.forEach((btn, index) => {
            btn.getComponent(Toggle).isChecked = condition(index + 1);
        });
        this.updateSelectedLines();
    }

    activeAllLines() {
        this.toggleLines(() => true);
    }

    deactiveAllLines() {
        this.toggleLines(() => false);
    }

    activeEvenLines() {
        this.toggleLines(index => index % 2 === 0);
    }

    activeOddLines() {
        this.toggleLines(index => index % 2 !== 0);
    }

    chooseRoom(roomID: number){
        this.room1.isChecked = roomID === 1;
        this.room2.isChecked = roomID === 2;
        this.room3.isChecked = roomID === 3;
    }

    show() {
        this.node.active = true;
    }

    dismiss() {
        this.node.active = false;
    }

}

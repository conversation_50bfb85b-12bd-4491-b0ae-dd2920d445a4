import { Prefab, _decorator, assetManager, director, log, sys, AssetManager, Component, instantiate } from "cc";

export default class BundleControl {
  static serverVersion: Record<string, any> = {};
  static BundleLobby: AssetManager.Bundle;

  static init(data: Record<string, any>): void {
    this.serverVersion = data;
  }

  static loadSceneGame(
      bundleName: string,
      sceneName: string,
      callbackProgress: (finish: number, total: number) => void,
      bundleCallback: () => void
  ): void {
    this.loadBundle(bundleName, (bundle) => {
      bundle.loadScene(
          sceneName,
          (finish, total) => callbackProgress(finish, total),
          (err, scene) => {
            director.preloadScene(
                sceneName,
                (c, t) => callbackProgress(c, t),
                () => {
                  director.loadScene(sceneName);
                  bundleCallback();
                }
            );
          }
      );
    });
  }

  static loadPrefabGame(
      bundleName: string,
      prefabName: string,
      callbackProgress: (finish: number, total: number) => void,
      bundleCallback: (prefab: Prefab, bundle: AssetManager.Bundle) => void
  ): void {
    this.loadBundle(bundleName, (bundle) => {
      bundle.load(
          prefabName,
          Prefab,
          (finish, total) => callbackProgress(finish, total),
          (err, prefab: Prefab) => {
            bundleCallback(prefab, bundle);
          }
      );
    });
  }

  static loadPrefabFromBundle(
      bundle: AssetManager.Bundle,
      prefabName: string,
      callbackProgress: (finish: number, total: number) => void,
      bundleCallback: (prefab: Prefab, bundle: AssetManager.Bundle) => void
  ): void {
    bundle.load(
        prefabName,
        Prefab,
        (finish, total) => callbackProgress(finish, total),
        (err, prefab: Prefab) => {
          bundleCallback(prefab, bundle);
        }
    );
  }

  static removeBundle(bundle: AssetManager.Bundle): void {
    // Uncomment if needed
    // bundle.releaseAll();
    // assetManager.removeBundle(bundle);
  }

  static loadBundle(
      bundleName: string,
      bundleCallback: (bundle: AssetManager.Bundle) => void
  ): void {
    const bundleVersion = this.serverVersion[bundleName];
    let url = bundleName;

    if (sys.isBrowser) {
      assetManager.loadBundle(url, (err, bundle) => {
        if (err) {
          log("Error Download bundle: " + JSON.stringify(err));
          return;
        }
        bundleCallback(bundle);
      });
    } else {
      if (sys.isNative && bundleVersion?.url) {
        url = bundleVersion.url;
      }

      assetManager.loadBundle(
          url,
          { version: bundleVersion?.hash },
          (err, bundle) => {
            if (err) {
              log("Error Download native bundle: " + JSON.stringify(err));
              return;
            }
            bundleCallback(bundle);
          }
      );
    }
  }

  static loadPrefabPopup(prefabPath: string, cb: (prefab: Prefab) => void): void {
    if (!this.BundleLobby) {
      log("BundleLobby is not set.");
      return;
    }

    this.BundleLobby.load(prefabPath, (err, prefab: Prefab) => {
      if (err) {
        log("Error loading popup prefab: " + err.message);
        return;
      }
      cb(prefab);
    });
  }

  static async createPrefab<T extends Component>(type: { prototype: T }, path: string): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.BundleLobby.load(path, (err, prefab: Prefab) => {
        if (err) {
          reject(err);
          return;
        }
        const node = instantiate(prefab);
        const component = node.getComponent(type);
        if (!component) {
          reject(new Error(`Prefab at path ${path} does not have component of type ${type.name}`));
          return;
        }
        resolve(component);
      });
    });
  }
}
import { _decorator, Component, Node, Sprite, SpriteFrame, Label, Prefab, Animation, tween, v3, size, Color, instantiate, sys, UITransform, UIOpacity, Tween, Vec3 } from 'cc';
import CasinoPlayer from "db://assets/Lobby/scripts/common/casino/Casino.Player";
import App from "db://assets/Lobby/scripts/common/App";
import DragonTigerSignalRClient from "db://assets/Lobby/scripts/common/networks/DragonTigerSignalRClient";
import CasinoLobby from "db://assets/Lobby/scripts/common/casino/Casino.Lobby";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import ChatInGame from "db://assets/Lobby/scripts/common/ChatInGame";
import BundleControl from "db://assets/Loading/scripts/BundleControl";

const { ccclass, property, menu } = _decorator;

enum PHASE {
    NONE = 0,
    BET = 1,
    DEAL = 2,
    RESULT = 3
}

@ccclass
@menu("RongHo/Play")
export default class RongHoPlay extends Component {
    static instance: RongHoPlay = null;

    @property(Node)
    roomDetail: Node = null;
    @property([SpriteFrame])
    listTableSpr: SpriteFrame[] = [];
    @property(Sprite)
    tableSpr: Sprite = null;
    @property(Node)
    chipNodes: Node = null;
    @property(Node)
    nodeMenu: Node = null;
    @property([Sprite])
    spriteCards: Sprite[] = [];
    @property(SpriteFrame)
    cardBack: SpriteFrame = null
    @property([SpriteFrame])
    cardFronts: SpriteFrame[] = [];
    @property(Node)
    dragonTigerMachine: Node = null;

    // JOIN ROOM
    @property(Label)
    lblDeck: Label = null;
    @property(Label)
    lblToast: Label = null;
    @property([CasinoPlayer])
    players: CasinoPlayer[] = [];
    @property(CasinoPlayer)
    mePlayer: CasinoPlayer = null;

    // BET
    flagLog: number = -1;
    betLogs: any[] = [];
    @property(Node)
    nodeDisableClearBet: Node = null;
    @property(Node)
    nodeDisableRebet: Node = null;
    @property(Node)
    nodeDisableBetX2: Node = null;
    @property(Node)
    nodeDisableFinishBet: Node = null;
    @property(Node)
    chipContainer: Node = null;
    @property([Node])
    betPositions: Node[] = [];
    @property([Label])
    lblBetPositions: Label[] = [];
    betValues = [];
    @property(Label)
    countdownSecond: Label = null;
    @property(Sprite)
    progressSprite: Sprite = null;
    @property(SpriteFrame)
    progressSpriteGreen: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteYellow: SpriteFrame = null;
    @property(SpriteFrame)
    progressSpriteRed: SpriteFrame = null;
    amounts = [];
    amounts_1 = [
        1,
        5,
        10,
        50,
        100
    ];
    amounts_2 = [
        1,
        2,
        10,
        20,
        100
    ]

    minBet: number = 0;
    amountSelected: number = 0;

    sessionId: number = 0;
    roomId: number;
    roomValue: number;
    currency: number;

    @property(Node)
    boxSoiCau: Node = null;
    @property(Prefab)
    popupRank: Prefab = null;
    @property(Node)
    popupContainer: Node = null;

    // CAU
    @property(Node)
    listCauMini: Node = null;
    @property(Node)
    itemCauMini: Node = null;
    @property(Node)
    listCauBig: Node = null;
    @property(Node)
    itemCauBig: Node = null;
    @property(Node)
    listCauBigDetail: Node = null;
    @property(Node)
    itemCauBigDetail: Node = null;
    @property([SpriteFrame])
    cauSpr: SpriteFrame[] = [];
    @property(Label)
    lblSessionCau: Label = null;
    @property([Label])
    listLabelCau: Label[] = [];
    LABEL_CAU: string[] = ["ca226", "ca227", "ca42", "ca228", "ca229", "ca230", "ca231"];

    //BOX DEALER
    @property(Node)
    dealerNode: Node = null;
    @property(Label)
    labelDealerNotify: Label = null;

    //BET TABLE
    @property(Node)
    betTableVip: Node = null;
    @property(Node)
    betTableNormal: Node = null;

    @property(Node)
    guideBG: Node = null;
    @property(Node)
    guide: Node = null;

    init(roomValue: number, currency: number) {
        this.roomValue = roomValue;
        this.currency = currency;
    }

    protected start() {
        RongHoPlay.instance = this;
        App.instance.showLoading(true);
        DragonTigerSignalRClient.getInstance().send('EnterRoom', [this.roomValue, this.currency], (data) => {
            if (data.c < 0) {
                App.instance.alertDialog.showMsg(DragonTigerSignalRClient.getErrMsg(data.c));
                this.node.destroy();
                return;
            }

            this.joinRoom(data.r);
            App.instance.showLoading(false);

            if (sys.localStorage.getItem("CA_RH_first_time") === null) {
                sys.localStorage.setItem("CA_RH_first_time", "1");
                this.showGuide();
            }
        });

        this.betTableNormal.active = !CasinoLobby.instance.isTableVip;
        this.betTableVip.active = CasinoLobby.instance.isTableVip;
        this.flagLog = -1;
    }

    onLoad() {
        this.spriteCards.forEach(card => {
            card.spriteFrame = this.cardBack;
        });

        this.chipNodes.children.forEach((button, index) => {
            const chip = button.getChildByName("chip");
            const text = button.getChildByName("text");

            chip.on(Node.EventType.TOUCH_END, () => {
                const isSelected = chip["_isSelected"];

                if (isSelected) {
                    Tween.stopAllByTarget(chip);
                    chip.y = 0;
                    text.getComponent(Label).color = Color.WHITE;
                    chip["_isSelected"] = false;
                    return;
                }

                this.chipNodes.children.forEach((otherButton) => {
                    const otherChip = otherButton.getChildByName("chip");
                    const otherText = otherButton.getChildByName("text");
                    Tween.stopAllByTarget(otherChip);
                    otherChip.y = 0;
                    otherText.getComponent(Label).color = Color.WHITE;
                    otherChip["_isSelected"] = false;
                });

                text.getComponent(Label).color = new Color(252, 255, 0);
                chip["_isSelected"] = true;
                this.amountSelected = this.amounts[index] * this.minBet;

                tween(chip)
                    .repeatForever(
                        tween()
                            .to(0.3, { y: 15 })
                            .to(0.3, { y: 0 })
                    )
                    .start();
            });
        });

        this.spriteCards.forEach(card => {
            card.spriteFrame = this.cardBack;
        });

        this.betPositions.forEach((betNode, p: number) => {
            betNode.on(Node.EventType.TOUCH_END, () => {
                if (this.amountSelected == 0) {
                    return;
                }

                this.actBet(this.amountSelected, p + 1);
            });
        });

        DragonTigerSignalRClient.getInstance().receive('roomData', (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            this.lblDeck.string = data.r.Deck + "";
            this.sessionId = data.r.SessionId;
            this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${this.sessionId}`;
            this.lblSessionCau.string = `#${this.sessionId}`;
            this.handleSession(data.r.Session);
            data.r.Players.forEach((player: any) => {
                var playerObj = this.getAllPlayersById(player.AccountId);
                if (playerObj) {
                    playerObj.setCoin(player.Balance);
                }
            });
        });

        DragonTigerSignalRClient.getInstance().receive('joinRoom', (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            if (data.r.AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                this.randomFreePlayer().set(data.r);
            }
        });

        DragonTigerSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
            var playerRM = this.getAllPlayersById(accountId);
            playerRM.showChatMsg(content);
        });

        DragonTigerSignalRClient.getInstance().receive('leaveRoom', (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            for (var i = 0; i < data.r.length; i++) {
                var item = data.r[i];
                if (item.id == `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    if (item.reason < 0) {
                        App.instance.alertDialog.showMsg(App.instance.getTextLang(`ca${item.reason}`));
                    }
                    DragonTigerSignalRClient.getInstance().dontReceive();
                    BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                    this.node.destroy();
                } else {
                    this.getPlayerById(item.id).leave();
                }
            }
        })

        DragonTigerSignalRClient.getInstance().receive('registerLeavingRoom', (data) => {
            var playerRLR = this.getAllPlayersById(data.r.id);
            if (data.r.status) {
                playerRLR.showRegisterQuit();
            } else {
                playerRLR.hideRegisterQuit();
            }
        });

        DragonTigerSignalRClient.getInstance().receive('rejectBetting', (data) => {
            let rejectedPlayers = data.map((id: string) => this.getPlayerById(id).lblNickname.string).join(", ");
            this.showToast(`Reject betting from ${rejectedPlayers}`);
        });

        DragonTigerSignalRClient.getInstance().receive('clearBetting', (data) => {
            var playerCC_Id = data.r.id;
            var playerCC = this.getPlayerById(playerCC_Id);
            this.chipContainer.children.filter(child => child.name.includes(playerCC_Id)).forEach(chip => {
                this.moveChipToPlayer(chip, playerCC);
            });

            this.cancelBet(playerCC_Id);
            this.getPlayerById(playerCC_Id).setCoin(data.r.balance);
        });

        DragonTigerSignalRClient.getInstance().receive('confirmBetting', (data) => {
            this.getAllPlayersById(data.r.id).showReady();
        });

        DragonTigerSignalRClient.getInstance().receive('playerOtherDevice', (data) => {
            this.showToast(DragonTigerSignalRClient.getErrMsg(data));
        });

        DragonTigerSignalRClient.getInstance().receive('connectionChanged', (data) => {
            var playerCC = this.getPlayerById(data.r.id);
            this.showToast(`${playerCC.lblNickname.string} ${data.status ? "connected" : "disconnected"}`);

            if (data.status) {
                playerCC.leave();
            }
        });

        DragonTigerSignalRClient.getInstance().receive('playerBet', (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            var player = this.getPlayerById(data.r.id);
            player.setCoin(data.r.balance);
            this.placeBet(player, data.r.amount, data.r.gate);

            this.saveBetValues(data.r.id, data.r.amount, data.r.gate);
        })
    }

    saveBetValues(id: string, amount: number, gate: number) {
        if (!Array.isArray(this.betValues[gate - 1])) {
            this.betValues[gate - 1] = [];
        }

        this.betValues[gate - 1].push({ id, amount });
        let totalBet = this.betValues[gate - 1].reduce((sum: any, bet: any) => sum + bet.amount, 0);
        this.lblBetPositions[gate - 1].string = Utils.formatMoney(totalBet, true);
        this.lblBetPositions[gate - 1].node.active = totalBet > 0;
    }

    cancelBet(accountId: string) {
        this.betValues.forEach((bets, index) => {
            if (Array.isArray(bets)) {
                this.betValues[index] = bets.filter(bet => bet.id !== accountId);

                let totalBet = this.betValues[index].reduce((sum: any, bet: any) => sum + bet.amount, 0);
                this.lblBetPositions[index].string = Utils.formatMoney(totalBet, true);
                this.lblBetPositions[index].node.active = totalBet > 0;
            }
        });
    }

    joinRoom(data: any) {
        this.roomId = data.Id;
        this.sessionId = data.SessionId;
        this.lblDeck.string = data.Deck + "";
        this.minBet = data.Value;
        this.tableSpr.spriteFrame = this.listTableSpr[data.Currency];
        this.lblSessionCau.string = `#${this.sessionId}`;
        this.roomDetail.getChildByName('RoomTable').getComponent(Label).string = (data.Currency == 0 ? App.instance.getTextLang('tb113') : App.instance.getTextLang('tb112')) + `: ${data.Id}`;
        this.roomDetail.getChildByName('RoomValue').getComponent(Label).string = App.instance.getTextLang('iap38') + ': ' + Utils.formatMoney(data.Value) + ' ' + (data.Currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo');
        this.roomDetail.getChildByName('RoomSession').getComponent(Label).string = App.instance.getTextLang('txt_session') + `: #${data.SessionId}`;

        if ([500, 5000, 50000].includes(this.minBet)) {
            this.amounts = this.amounts_2;
        } else {
            this.amounts = this.amounts_1;
        }

        this.chipNodes.children.forEach((button, index) => {
            const text = button.getChildByName("text");
            text.getComponent(Label).string = Utils.formatMoney(this.amounts[index] * this.minBet, true);
            if (index == 0) {
                button.getChildByName("chip").emit(Node.EventType.TOUCH_END);
            }
        });

        this.betValues = [];
        for (var iBet = 0; iBet < this.lblBetPositions.length; iBet++) {
            this.lblBetPositions[iBet].node.active = false;
            this.lblBetPositions[iBet].string = '0';
        }

        var players = data.Players;
        if (players && players.length > 0) {
            for (var i = 0; i < players.length; i++) {
                if (players[i].AccountId != `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                    this.randomFreePlayer().set(players[i]);
                } else {
                    this.mePlayer.set(players[i])
                }
            }
        }

        this.updateStatistic();

        if (data.Session) {
            this.handleSession(data.Session);
        }
    }

    handleSession(session: any) {
        this.unscheduleAllCallbacks();
        var dealerNormal = this.dealerNode.getChildByName('NORMAL');
        var dealerBET = this.dealerNode.getChildByName('BET');
        dealerBET.active = false;
        dealerNormal.active = false;
        this.countdownSecond.node.parent.active = false;
        if (session.Phrase == PHASE.NONE) {
            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();
            return;
        }

        if (session.Phrase == PHASE.BET) {
            this.spriteCards.forEach(card => card.node.active = false);
            this.flagLog++;
            this.betLogs = this.betLogs.filter(log => log.flag == this.flagLog - 1);

            dealerBET.active = true;
            dealerBET.getComponent(Animation).play();
            const currency = this.currency == 0 ? App.instance.getTextLang('txt_coin') : 'Tipzo';
            const messages = [
                App.instance.getTextLang('ca157'),
                App.instance.getTextLang('ca150') + ` ${Utils.formatNumber(this.minBet)} ${currency}`,
                App.instance.getTextLang('ca161'),
                App.instance.getTextLang('ca162')
            ];

            let indexMsg = 0;

            this.schedule(() => {
                const parentNode = this.labelDealerNotify.node.parent;
                const opacityComp = parentNode.getComponent(UIOpacity) || parentNode.addComponent(UIOpacity);

                tween(opacityComp)
                    .to(0.5, { opacity: 0 })
                    .call(() => {
                        this.labelDealerNotify.string = messages[indexMsg];
                        indexMsg++;
                        if (indexMsg >= messages.length) indexMsg = 0;
                    })
                    .to(0.2, { opacity: 255 })
                    .start();
            }, 2.5);
            this.updateTimeout(session.Timeout);
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = this.betLogs.length == 0;
            this.nodeDisableBetX2.active = this.betLogs.length == 0;
            this.nodeDisableFinishBet.active = true;
            for (var i = 0; i < this.betPositions.length; i++) {
                this.betPositions[i].getChildByName("WIN").active = false;
            }

            for (var iBet = 0; iBet < this.lblBetPositions.length; iBet++) {
                this.lblBetPositions[iBet].node.active = false;
            }

            [this.mePlayer, ...this.players].forEach(player => {
                player.boxWin.active = false;
                player.hideReady();
                player.hideRegisterQuit();
                player.hideWinAnimation();
            });

            this.chipContainer.removeAllChildren();
            return;
        }

        if (session.Phrase == PHASE.DEAL) {
            [this.mePlayer, ...this.players].forEach(player => {
                player.hideReady();
            });
            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();
            this.labelDealerNotify.string = App.instance.getTextLang('ca110');
            // this.updateTimeout(session.Timeout);
            this.hideCards();
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = true;
        }

        if (session.Phrase == PHASE.RESULT) {
            dealerNormal.active = true;
            dealerNormal.getComponent(Animation).play();
            this.labelDealerNotify.string = App.instance.getTextLang('me18');
            // this.updateTimeout(session.Timeout);
            this.betValues = [0, 0, 0, 0, 0, 0, 0];
            for (var iBetR = 0; iBetR < this.lblBetPositions.length; iBetR++) {
                this.lblBetPositions[iBetR].node.active = false;
                this.lblBetPositions[iBetR].string = '0';
            }
            this.revealCards(session.DragonHand.Cards[0], session.TigerHand.Cards[0]);
            this.scheduleOnce(() => {
                this.showResult(session.Result);

                session.Prizes.forEach((item: any) => {
                    const player = this.getAllPlayersById(item.AccountId);
                    player.boxWin.active = true;
                    player.boxWin.getChildByName('win').active = true;
                    player.boxWin.getChildByName('lose').active = false;
                    player.isWin = true;
                    player.showWinAnimation();
                    this.chipContainer.children.filter(child => child.name.includes(item.AccountId)).forEach(chip => {
                        var gate = parseInt(chip.name.replace(item.AccountId + "__", ""));
                        if (this.isWinGate(gate)) {
                            this.moveChipToPlayer(chip, player, true);
                        } else {
                            this.moveChipToDealer(chip);
                        }
                    });
                });

                const playerPrizeIds = session.Prizes.map((item: any) => item.AccountId);
                [this.mePlayer, ...this.players]
                    .filter(player => player.id !== "" && !playerPrizeIds.includes(player.id))
                    .forEach(player => {
                        var isBet = false;
                        this.chipContainer.children.filter(child => child.name.includes(player.id)).forEach(chip => {
                            isBet = true;
                            this.moveChipToDealer(chip);
                        });

                        this.scheduleOnce(() => {
                            if (isBet) {
                                player.boxWin.active = true;
                                player.boxWin.getChildByName('win').active = false;
                                player.boxWin.getChildByName('lose').active = true;
                            }
                        }, 0.5);
                    });

                this.updateStatistic();
            }, 1);
        }
    }

    showResult(result: any) {
        for (let iR = 0; iR < this.betPositions.length; iR++) {
            if (result.WinGates.includes(iR + 1)) {
                this.betPositions[iR].getChildByName("WIN").active = true;
            }
        }
    }

    isWinGate(gate: number): boolean {
        return this.betPositions[gate - 1].getChildByName("WIN").active;
    }

    updateTimeout(timeout: number) {
        const totalTime = timeout;
        let elapsed = 0;
        this.countdownSecond.node.parent.active = true;
        this.schedule(() => {
            elapsed += 0.05;
            const percent = Math.min(elapsed / totalTime, 1);
            this.progressSprite.fillRange = percent;
            this.progressSprite.spriteFrame = percent < 0.5 ? this.progressSpriteGreen : (percent < 0.8 ? this.progressSpriteYellow : this.progressSpriteRed);
            this.progressSprite.node.setScale(-1, 1, 1);
        }, 0.05);

        this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
        this.schedule(() => {
            if (timeout < 0) {
                this.unscheduleAllCallbacks();
                return;
            }
            this.countdownSecond.string = timeout < 10 ? `0${timeout}` : timeout + '';
            timeout--;
        }, 1);
    }

    randomFreePlayer() {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == "") {
                return this.players[i];
            }
        }

        return null;
    }

    getPlayerById(id: string) {
        for (var i = 0; i < this.players.length; i++) {
            if (this.players[i].id == id) {
                return this.players[i];
            }
        }

        return null;
    }

    getChipByAmount(amount: number) {
        return this.chipNodes.children.find((_chip, index) => {
            return this.amounts[index] === amount / this.minBet;
        }).getChildByName("chip");
    }

    getAllPlayersById(id: string) {
        return [this.mePlayer, ...this.players].find(player => player.id === id);
    }

    toggleMenu() {
        this.nodeMenu.active = !this.nodeMenu.active;
    }

    private revealCards(dragonIdx: number = -1, tigerIdx: number = -1) {
        this.flipCard(this.spriteCards[0], dragonIdx);
        this.flipCard(this.spriteCards[1], tigerIdx);
    }

    private hideCards() {
        this.spriteCards[0].node.active = true;
        this.moveCardFromMachine(this.spriteCards[0].node);
        this.scheduleOnce(() => {
            this.spriteCards[1].node.active = true;
            this.moveCardFromMachine(this.spriteCards[1].node);
        }, 1);
    }

    private flipCard(card: Sprite, cardIndex: number) {
        let newSize = size(78, 110);

        tween(card.node)
            .to(0.3, { scale: v3(0, 1, 1) })
            .call(() => {
                card.spriteFrame = this.cardFronts[cardIndex];
                card.node.getComponent(UITransform).setContentSize(newSize);
            })
            .to(0.3, { scale: v3(1, 1, 1) })
            .start();
    }

    clearBetting() {
        if (this.nodeDisableClearBet.active) {
            return;
        }

        DragonTigerSignalRClient.getInstance().send('ClearBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            this.chipContainer.children.filter(child => child.name.includes(this.mePlayer.id)).forEach(chip => {
                this.moveChipToPlayer(chip, this.mePlayer);
            });
            this.nodeDisableClearBet.active = true;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = true;
            this.betLogs = [];
            this.mePlayer.setCoin(data.r.balance);
            this.cancelBet(data.r.id);
        });
    }

    x2Betting() {
        if (this.nodeDisableBetX2.active) {
            return;
        }

        this.actBetFromBetLogs();
        setTimeout(() => {
            this.actBetFromBetLogs();
        }, 500);
    }

    reLastBet() {
        if (this.nodeDisableRebet.active) {
            return;
        }

        this.actBetFromBetLogs();
    }

    actBetFromBetLogs() {
        this.betLogs.forEach(log => {
            if (log.flag == this.flagLog - 1) {
                this.actBet(log.amount, log.gate);
            }
        });
    }

    actBet(amount: number, gate: number) {
        DragonTigerSignalRClient.getInstance().send('Bet', [amount, gate], (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            this.nodeDisableClearBet.active = false;
            this.nodeDisableRebet.active = true;
            this.nodeDisableBetX2.active = true;
            this.nodeDisableFinishBet.active = false;

            this.placeBet(this.mePlayer, data.r.amount, data.r.gate);
            this.mePlayer.setCoin(data.r.balance);
            this.saveBetValues(data.r.id, data.r.amount, data.r.gate);

            this.betLogs.push({amount: data.r.amount, gate: data.r.gate, flag: this.flagLog});
        });
    }

    finishBetting() {
        if (this.nodeDisableFinishBet.active) {
            return;
        }

        DragonTigerSignalRClient.getInstance().send('FinishBetting', [], (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            this.mePlayer.showReady();
        });
    }

    updateStatistic() {
        this.listCauMini.removeAllChildren();
        DragonTigerSignalRClient.getInstance().send('GetGameHistory', [this.roomId], (data) => {
            if (data.c < 0) {
                this.showToast(DragonTigerSignalRClient.getErrMsg(data.c));
                return;
            }

            this.drawCau(data.r);
        });
    }

    closePlay() {
        DragonTigerSignalRClient.getInstance().send('ExitRoom', [], (data) => {
            if (data.c == 0) {
                DragonTigerSignalRClient.getInstance().dontReceive();
                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                this.node.destroy();
                return;
            }

            if (data.r.status) {
                this.mePlayer.showRegisterQuit();
                this.showToast(App.instance.getTextLang('me8'));
            } else {
                this.mePlayer.hideRegisterQuit();
                this.showToast(App.instance.getTextLang('me9'));
            }
        })
    }

    placeBet(player: CasinoPlayer, amount: number, position: number) {
        let chip = instantiate(this.getChipByAmount(amount));
        chip.setScale(0.5, 0.5, 0.5);
        this.chipContainer.addChild(chip);

        let startPos2D = player.avatarNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
        let endPos2D = this.betPositions[position - 1].getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);

        let startPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(startPos2D.x, startPos2D.y, 0));
        let endPos = this.node.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(endPos2D.x, endPos2D.y, 0));

        const tolerance = 10;
        const existingChips = this.chipContainer.children.filter(child =>
            Vec3.distance(child.getPosition(), endPos) <= tolerance
        );

        if (existingChips.length > 0) {
            let offsetX = 10 * (existingChips.length % 5);
            let offsetY = -10 * Math.floor(existingChips.length / 5);
            endPos.x += offsetX;
            endPos.y += offsetY;
        }

        chip.position = startPos;
        chip.name = `${player.id}__${position}`;

        tween(chip)
            .to(0.5, { position: endPos }, { easing: "sineOut" })
            .start();
    }

    showToast(msg: string) {
        this.lblToast.string = msg;
        this.lblToast.node.active = true;

        setTimeout(() => {
            if (this.lblToast.node) {
                this.lblToast.node.active = false;
            }
        }, 2000);
    }

    showBoxSoiCau() {
        this.boxSoiCau.active = true;
    }

    hiddenBoxSoiCau(){
        this.boxSoiCau.active = false;
    }

    chatInGame: ChatInGame = null;

    showBoxChat() {
        App.instance.inactivityTimer = 0;
        if (this.chatInGame == null) {
            BundleControl.loadPrefabPopup("prefabs/ChatInGame", (prefab: any) => {
                this.chatInGame = instantiate(prefab).getComponent("ChatInGame");
                this.node.addChild(this.chatInGame.node);
                this.chatInGame.show(Configs.InGameIds.RongHo);
            });
        } else {
            this.chatInGame.show(Configs.InGameIds.RongHo);
        }
    }

    actShowPopupRank() {
        let popupRank = instantiate(this.popupRank);
        this.popupContainer.addChild(popupRank);
        // @ts-ignore
        popupRank.getComponent("Casino.PopupRank").showDetail(this.currency, Configs.InGameIds.RongHo);
    }

    drawCau(data: any) {
        this.listCauBig.removeAllChildren();
        this.listCauBigDetail.removeAllChildren();
        this.listCauMini.removeAllChildren();
        var listCau = [0, 0, 0, 0, 0, 0, 0];
        for (var i = 0; i < data.length; i++) {
            for (var j = 0; j < data[i].WinGates.length; j++) {
                var winGate = data[i].WinGates[j];
                listCau[winGate - 1]++;

                var itemBigDetail = instantiate(this.itemCauBigDetail);
                itemBigDetail.getComponent(Sprite).spriteFrame = this.cauSpr[winGate - 1];
                this.listCauBigDetail.addChild(itemBigDetail);
            }

            var result = data[i];
            var itemMini = instantiate(this.itemCauMini);
            var itemBig = instantiate(this.itemCauBig);
            if (result.Dragon > result.Tiger) {
                itemMini.getComponent(Sprite).spriteFrame = this.cauSpr[0];
                itemBig.getComponent(Sprite).spriteFrame = this.cauSpr[0];
            } else if (result.Dragon < result.Tiger) {
                itemMini.getComponent(Sprite).spriteFrame = this.cauSpr[1];
                itemBig.getComponent(Sprite).spriteFrame = this.cauSpr[1];
            } else {
                itemMini.getComponent(Sprite).spriteFrame = this.cauSpr[2];
                itemBig.getComponent(Sprite).spriteFrame = this.cauSpr[2];
            }

            this.listCauMini.addChild(itemMini);
            this.listCauBig.addChild(itemBig);
        }

        for (var iCau = 0; iCau < this.listLabelCau.length; iCau++) {
            this.listLabelCau[iCau].string = App.instance.getTextLang(this.LABEL_CAU[iCau]) + " " + listCau[iCau];
        }
    }

    private moveChipToPlayer(chip: Node, player: CasinoPlayer, moveFromDealerFirst: boolean = false) {
        const targetPos = player.node.position.clone();
        const middlePos = chip.position.clone();

        if (moveFromDealerFirst) {
            const delayStep = 0.05;
            for (let i = 0; i < 10; i++) {
                const cloneChip = instantiate(chip);
                cloneChip.position = this.dealerNode.position;
                this.chipContainer.addChild(cloneChip);

                tween(cloneChip)
                    .delay(i * delayStep)
                    .to(0.3, { position: middlePos })
                    .call(() => cloneChip.destroy())
                    .start();
            }

            const totalDelay = 10 * delayStep;
            tween(chip)
                .delay(totalDelay + 0.1)
                .to(0.4, { position: targetPos })
                .call(() => chip.destroy())
                .start();

            return;
        }

        tween(chip)
            .to(0.5, { position: targetPos })
            .call(() => chip.destroy())
            .start();
    }

    private moveChipToDealer(chip: Node) {
        tween(chip)
            .to(0.5, { position: this.dealerNode.position })
            .call(() => chip.destroy())
            .start();
    }

    showGuide() {
        var table = this.guideBG.children[0];
        table.getChildByName("VIP").active = this.currency == 1;
        table.getChildByName("NORMAL").active = this.currency == 0;
        table.children.forEach(child => {
            if (child.getChildByName("VIP") == null || child.getChildByName("NORMAL") == null) return;
            child.getChildByName("VIP").active = this.currency == 1;
            child.getChildByName("NORMAL").active = this.currency == 0;
        });
        this.guideBG.active = true;
        this.guide.active = true;
    }

    hideGuide() {
        this.guideBG.active = false;
        this.guide.active = false;
    }

    private moveCardFromMachine(card: Node) {
        const worldPos = this.dragonTigerMachine.getComponent(UITransform).convertToWorldSpaceAR(v3(0, 0, 0));
        const startPos = card.parent.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
        const endPos = card.position;
        card.getComponent(Sprite).spriteFrame = this.cardBack;
        card.position = startPos;
        card.active = true;

        tween(card)
            .to(0.5, {position: endPos})
            .start();
    }
}

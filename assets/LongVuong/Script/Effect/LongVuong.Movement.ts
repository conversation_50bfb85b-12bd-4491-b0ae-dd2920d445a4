import { _decorator, Component, Vec3, CCInteger } from 'cc';
const { ccclass, property, menu } = _decorator;

@ccclass('EllipticalMotion')
@menu('Motion/EllipticalMotion')
export class EllipticalMotion extends Component {

    @property(CCInteger)
    radiusX: number = 30;
    @property(CCInteger)
    radiusY: number = 15;
    @property(CCInteger)
    speed: number = 2;

    private elapsedTime: number = 0;
    private originPos: Vec3 = new Vec3();

    onLoad() {
        this.originPos = this.node.getPosition();
    }

    update(dt: number) {
        this.elapsedTime += dt * this.speed;

        const offsetX = Math.cos(this.elapsedTime) * this.radiusX;
        const offsetY = Math.sin(this.elapsedTime) * this.radiusY;

        const newPos = new Vec3(
            this.originPos.x + offsetX,
            this.originPos.y + offsetY,
            this.originPos.z
        );

        this.node.setPosition(newPos);
    }
}
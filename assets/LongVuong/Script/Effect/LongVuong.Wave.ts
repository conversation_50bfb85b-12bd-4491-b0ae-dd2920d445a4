import {_decorator, Component, CCInteger} from 'cc';

const {ccclass, property} = _decorator;

@ccclass
export default class FloatingWave extends Component {
    @property(CCInteger)
    amplitudeY: number = 10;
    @property(CCInteger)
    amplitudeRotation: number = 5;
    @property(CCInteger)
    waveSpeed: number = 1.5;

    private startY: number = 0;
    private time: number = 0;

    onLoad() {
        this.startY = this.node.y;
    }

    update(dt: number) {
        this.time += dt;

        const sinVal = Math.sin(this.time * this.waveSpeed * Math.PI * 2);
        this.node.y = this.startY + sinVal * this.amplitudeY;

        this.node.angle = sinVal * this.amplitudeRotation;
    }
}

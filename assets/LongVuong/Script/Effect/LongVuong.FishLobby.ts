import { _decorator, Component, CCInteger, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('FishMovement')
export class FishMovement extends Component {
    @property(CCInteger)
    baseSpeed: number = 100;
    @property(CCInteger)
    pauseTime: number = 1.0;
    @property(CCInteger)
    moveDistance: number = 300;
    @property(CCInteger)
    startFacingRight: boolean = true;
    @property(CCInteger)
    initialSpriteFacingRight: boolean = true;

    private speed: number = 0;
    private direction: number = 1;
    private moving: boolean = true;
    private distanceMoved: number = 0;
    private shouldFlipNext: boolean = false;

    onLoad() {
        this.speed = this.baseSpeed;
        this.direction = this.startFacingRight ? 1 : -1;
        this.updateFishFlip();
        this.moving = true;
    }

    update(dt: number) {
        if (!this.moving) return;

        const moveStep = this.speed * dt * this.direction;
        const currentPos = this.node.getPosition();
        const newX = currentPos.x + moveStep;
        this.node.setPosition(new Vec3(newX, currentPos.y, currentPos.z));
        this.distanceMoved += Math.abs(moveStep);

        if (this.distanceMoved >= this.moveDistance) {
            this.distanceMoved = 0;
            this.moving = false;

            this.scheduleOnce(() => {
                if (this.shouldFlipNext) {
                    this.direction *= -1;
                    this.updateFishFlip();
                    this.speed = this.baseSpeed;
                } else {
                    this.speed = this.baseSpeed * 3;
                }

                this.shouldFlipNext = !this.shouldFlipNext;
                this.moving = true;
            }, this.pauseTime);
        }
    }

    private updateFishFlip() {
        const faceMultiplier = this.initialSpriteFacingRight ? 1 : -1;
        const currentScale = this.node.getScale();
        const scaleX = Math.abs(currentScale.x) * faceMultiplier * this.direction;
        this.node.setScale(new Vec3(scaleX, currentScale.y, currentScale.z));
    }
}
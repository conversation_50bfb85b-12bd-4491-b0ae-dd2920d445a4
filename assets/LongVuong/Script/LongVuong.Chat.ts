import { _decorator, Component, Node, instantiate, EditBox, Label, RichText } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import LongVuongPlay from "db://assets/LongVuong/Script/LongVuong.Play";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/LongVuongPhotonClient";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("LongVuong/Chat")
export default class LongVuongChat extends Component {
    static instance : LongVuongChat = null;

    @property(Node)
    listMessage: Node = null;
    @property(Node)
    templateMessage: Node = null;
    @property(Node)
    listFast: Node = null;
    @property(Node)
    templateFast: Node = null;
    @property(EditBox)
    edbMessage: EditBox = null;

    fastMessages = [
        "TLN_BTN_FASTCHAT_1",
        "TLN_BTN_FASTCHAT_2",
        "TLN_BTN_FASTCHAT_3",
        "TLN_BTN_FASTCHAT_4",
        "TLN_BTN_FASTCHAT_5",
        "TLN_BTN_FASTCHAT_6",
    ];

    onLoad() {
        this.listFast.removeAllChildren();
        LongVuongChat.instance = this;
    }

    start () {
        this.fastMessages.forEach((msgKey) => {
            var item = instantiate(this.templateFast);
            item.active = true;
            item.getComponentInChildren(Label).string = App.instance.getTextLang(msgKey);
            item.on("click", () => {
                this.edbMessage.string = App.instance.getTextLang(msgKey);
            });
            this.listFast.addChild(item);
        });
    }

    appendMessage(data: any) {
        var player = LongVuongPlay.instance.getPlayerById(data.f);
        if (player) {
            var msgNode = instantiate(this.templateMessage);
            msgNode.active = true;
            if (data.f === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                msgNode.getComponent(RichText).string = `<color=#fff600>${player.nickname}: </c><color=#ffffff>${data.c}</color>`;
            } else {
                msgNode.getComponent(RichText).string = `<color=#3c91e6>${player.nickname}: </c><color=#ffffff>${data.c}</color>`;
            }
            msgNode.parent = this.listMessage;
        }
    }

    actSubmit() {
        if (this.edbMessage.string.trim() === "") return;

        var payload = {
            c: this.edbMessage.string,
        }

        this.edbMessage.string = "";

        var data = [];
        data.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.Chat);
        data.push(LongVuongPhotonClient.EParameterCodes.ChatMessage, JSON.stringify(payload));
        LongVuongPhotonClient.getInstance().sendOperation(LongVuongPhotonClient.EOperationCodes.Game, data, true);
    }
}

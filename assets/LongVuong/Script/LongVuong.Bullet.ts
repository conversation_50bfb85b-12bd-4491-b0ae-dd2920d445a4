import { _decorator, Component, Node, Sprite, <PERSON>p<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, director, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, size } from 'cc';
import LongVuongPlay from "db://assets/LongVuong/Script/LongVuong.Play";
import LongVuongFish from "db://assets/LongVuong/Script/LongVuong.Fish";
import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/LongVuongPhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/PhotonClient";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu('LongVuong/Bullet')
export default class LongVuongBullet extends Component {

    @property(Node)
    bullet: Node = null;
    @property(Node)
    fire: Node = null;
    @property([SpriteFrame])
    bulletSprFrames: SpriteFrame[] = [];
    @property([Sprite<PERSON>ram<PERSON>])
    fireSprFrames: Sprite<PERSON><PERSON><PERSON>[] = [];
    @property([AudioClip])
    sounds: AudioClip[] = [];

    public gunId: number = 1;
    public positionId: number = -1;
    private worldSize: Size = size(1920, 1080);
    private exploreDuration: number = 0.8;
    private vX = 0;
    private vY = 0;
    private collisionCount = 2;
    private curExplore = 0;
    private play: LongVuongPlay = null;
    public isExplored = false;
    public isExploring = false;

    protected onLoad() {
        // director.getCollisionManager().enabled = true;
        this.play = LongVuongPlay.instance;
    }

    onCollisionEnter(other: Collider, _self: Collider) {
        if (this.isExploring || this.isExplored) return;
        var fish = other.node.parent.parent.parent.getComponent(LongVuongFish) || other.node.parent.parent.parent.parent.getComponent(LongVuongFish);
        if ((fish.node.active === false) || (this.play.isTargetFish && this.play.targetFish !== fish)) return;

        this.explore();
        fish.hurt();
        fish.explodeByGun(this.gunId);
        if (this.positionId !== this.play.mePlayer.serverPos) return;

        let data = {
            f: fish.id,
            si: this.play.shootingId,
            gi: this.play.mePlayer.gunId,
        };

        let params = [];
        params.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.PlayerShotFish);
        params.push(LongVuongPhotonClient.EParameterCodes.FishShotMessage, JSON.stringify(data));

        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
    }

    public run() {
        this.node.active = true;
        let speed = 3000;
        let dir = Utils.degreesToVec2(this.node.angle);
        this.vX = dir.x * speed;
        this.vY = dir.y * speed;
        this.collisionCount = 2;
        this.isExplored = false;
        this.isExploring = false;
        this.bullet.active = true;
        this.fire.active = false;
        if ([1, 2, 3].includes(this.gunId)) {
            // audioEngine.playEffect(this.sounds[0], false);
        } else if ([4, 5, 6].includes(this.gunId)) {
            // audioEngine.playEffect(this.sounds[1], false);
        } else if (this.gunId == 7) {
            // audioEngine.playEffect(this.sounds[2], false);
        } else if (this.gunId == 8) {
            // audioEngine.playEffect(this.sounds[3], false);
        } else {
            // audioEngine.playEffect(this.sounds[4], false);
        }
    }

    public updateBulletByGunId(gunId: number) {
        this.bullet.getComponent(Sprite).spriteFrame = this.bulletSprFrames[gunId - 1];
        this.fire.getComponent(Sprite).spriteFrame = this.fireSprFrames[gunId - 1];
    }

    public updateRealTime(dt: number) {
        if (this.isExplored) return;

        if (this.isExploring) {
            this.curExplore -= dt;
            if (this.curExplore <= 0) {
                this.isExplored = true;
                this.node.destroy();
            }
            return;
        }

        const stepCount = 3;
        const stepDt = dt / stepCount;

        for (let i = 0; i < stepCount; i++) {
            let newPos = this.node.position.clone();
            newPos.x += this.vX * stepDt;
            newPos.y += this.vY * stepDt;
            this.node.setPosition(newPos);

            if (Math.abs(newPos.x) > this.worldSize.width / 2) {
                this.vX *= -1;
                this.node.angle = Math.atan2(this.vY, this.vX) * Utils.Rad2Deg;
                newPos.x = (newPos.x < 0 ? -1 : 1) * this.worldSize.width / 2;
                this.node.setPosition(newPos);
                this.collisionCount--;
            } else if (Math.abs(newPos.y) > this.worldSize.height / 2) {
                this.vY *= -1;
                this.node.angle = Math.atan2(this.vY, this.vX) * Utils.Rad2Deg;
                newPos.y = (newPos.y < 0 ? -1 : 1) * this.worldSize.height / 2;
                this.node.setPosition(newPos);
                this.collisionCount--;
            }

            if (this.collisionCount < 0) {
                this.node.destroy();
                return;
            }
        }
    }

    public explore() {
        this.isExploring = true;
        this.curExplore = this.exploreDuration;
        this.bullet.active = false;
    }
}

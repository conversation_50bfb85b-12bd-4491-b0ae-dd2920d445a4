import { _decorator, Node, instantiate, Label } from 'cc';
import LongVuongLobby from "db://assets/LongVuong/Script/LongVuong.Lobby";
import Dialog from "db://assets/Lobby/scripts/common/Dialog";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("LongVuong/PopupGuide")
export default class LongVuongPopupGuide extends Dialog {

    @property([Node])
    contents: Node[] = [];
    @property([Node])
    pages: Node[] = [];
    @property(Node)
    content2List: Node = null;
    @property(Node)
    content2Template: Node = null;

    private currentPage = 1;

    start () {
        this.content2List.removeAllChildren();
        LongVuongLobby.instance.listGunConfig.forEach((gunConfig) => {
            var itemNode = instantiate(this.content2Template);
            itemNode.getChildByName('BET').getComponent(Label).string = gunConfig.v.toLocaleString("vi-VN");
            itemNode.getChildByName('PERCENT').getComponent(Label).string = gunConfig.j * 100 + "%";
            this.content2List.addChild(itemNode);
        });
        this.togglePage(null, this.currentPage);
    }

    togglePage(target: any, index: number) {
        this.currentPage = index;
        for (let i = 0; i < this.contents.length; i++) {
            this.contents[i].active = (i == index - 1);
            this.pages[i].getChildByName('active').active = (i == index - 1);
        }
    }

    nextPage() {
        if (this.currentPage == 5) return;
        this.currentPage++;
        this.togglePage(null, this.currentPage);
    }

    prevPage() {
        if (this.currentPage == 1) return;
        this.currentPage--;
        this.togglePage(null, this.currentPage);
    }
}

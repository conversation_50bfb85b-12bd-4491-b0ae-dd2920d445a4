import { _decorator, Label } from "cc";
import Dialog from "db://assets/Lobby/scripts/common/Dialog";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("LongVuong/PopupConfirm")
export default class LongVuongPopupConfirm extends Dialog {

    @property(Label)
    message: Label = null;

    private onDismissed: (isConfirm: boolean) => void = null;
    private isClickedConfirm: boolean = false;

    showConfirm(message: string, onDismissed: (isConfirm: boolean) => void) {
        this.message.string = message;
        this.onDismissed = onDismissed;
        this.isClickedConfirm = false;
        super.show();
    }

    actConfirm() {
        this.isClickedConfirm = true;
        this.dismiss();
    }

    _onShowed() {
        super._onShowed();
    }

    _onDismissed() {
        super._onDismissed();
        if (typeof this.onDismissed === "function") {
            this.onDismissed(this.isClickedConfirm);
        }
    }
}
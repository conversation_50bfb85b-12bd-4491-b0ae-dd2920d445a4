import { _decorator, Node, instantiate, Label } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/LongVuongPhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/PhotonClient";
import LongVuongLobby from "db://assets/LongVuong/Script/LongVuong.Lobby";
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("LongVuong/PopupHistory")
export default class LongVuongPopupHistory extends Dialog {

    @property([Node])
    activeButtons: Node[] = [];
    @property(Node)
    listContentNode: Node = null;
    @property(Node)
    itemContentNode: Node = null;
    @property(Node)
    listPageNode: Node = null;
    @property(Node)
    itemPageNode: Node = null;
    @property(Node)
    header3Play: Node = null;
    @property(Node)
    header3Exchange: Node = null;

    private currentPage: number = 1;
    private totalPages: number = 1;
    private historyData: any[] = [];
    private pageSize: number = 8;
    private isPlayTab: boolean = false;
    private isCashOutTab: boolean = false;
    private playTypes = [
        'CK_HISTORY_SHOT',
        'fish_pu27',
        'TLN_REWARD_ELECTRIC_FISH',
        'TLN_WIN_GOD_OF_WEALTH',
        'TLN_REWARD_JACKPOT',
        'TLN_LOSE_GOD_OF_WEALTH'
    ]

    start() {
        LongVuongPhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Statistic, (res) => {
            if (res.errCode < 0) {
                LongVuongLobby.instance.actShowDialog(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            const keys = Object.keys(res.vals);

            keys.forEach(key => {
                const code = parseInt(key);
                let data = res.vals[key];

                if (code === LongVuongPhotonClient.EParameterCodes.PlayHistoryResponse || code === LongVuongPhotonClient.EParameterCodes.ExChangeHistoryResponse) {
                    this.historyData = JSON.parse(data);
                    this.listPageNode.removeAllChildren();
                    this.currentPage = 1;
                    this.totalPages = 1;
                    if ( this.historyData.length === 0) return;
                    this.totalPages = Math.min(10, Math.ceil(this.historyData.length / this.pageSize));
                    for (let idx = 1; idx <= this.totalPages; idx++) {
                        var pageNode = instantiate(this.itemPageNode);
                        pageNode.getComponentInChildren(Label).string = idx.toString();
                        pageNode.parent = this.listPageNode;
                        pageNode.on("click", () => {
                            this.currentPage = idx;
                            this.showContent(this.currentPage);
                        }, this);
                    }
                    this.showContent(this.currentPage);
                }
            });
        });
    }

    show() {
        super.show();
        this.toggleTab(null, "0");
    }

    toggleTab(_event: any, index: string) {
        this.activeButtons.forEach((button, i) => {
            button.active = i == parseInt(index);
        });
        this.isPlayTab = false;
        this.isCashOutTab = false;
        var data: any[] = [LongVuongPhotonClient.EParameterCodes.OperationSubCode];
        switch (index) {
            case "0":
                this.isPlayTab = true;
                this.header3Play.active = true;
                this.header3Exchange.active = false;
                data.push(LongVuongPhotonClient.EOperationSubCodes.PlayGameHistory);
                break;
            case "1":
                this.header3Play.active = false;
                this.header3Exchange.active = true;
                data.push(LongVuongPhotonClient.EOperationSubCodes.CashInHistory);
                break;
            case "2":
                this.isCashOutTab = true;
                this.header3Play.active = false;
                this.header3Exchange.active = true;
                data.push(LongVuongPhotonClient.EOperationSubCodes.CashOutHistory);
                break;
        }
        this.listContentNode.removeAllChildren();
        data.push(LongVuongPhotonClient.EParameterCodes.AccountId, Configs.Login.AccountID);
        data.push(LongVuongPhotonClient.EParameterCodes.PortalId, Configs.Login.PortalID);
        data.push(LongVuongPhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID);

        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Statistic, data, true);
    }

    nextPage() {
        if (this.currentPage == this.totalPages) return;
        this.currentPage++;
        this.showContent(this.currentPage);
    }

    prevPage() {
        if (this.currentPage == 1) return;
        this.currentPage--;
        this.showContent(this.currentPage);
    }

    private showContent(page: number) {
        this.listPageNode.children.forEach((node, index) => {
           node.getChildByName('active').active = (index + 1 == page);
        });

        this.listContentNode.removeAllChildren();
        const startIdx = (page - 1) * this.pageSize;
        const endIdx = startIdx + this.pageSize;
        const paginatedData = this.historyData.slice(startIdx, endIdx);
        paginatedData.forEach((data: any) => {
            var itemNode = instantiate(this.itemContentNode);
            itemNode.parent = this.listContentNode;
            itemNode.active = true;

            var item1 = itemNode.getChildByName("1").getComponent(Label);
            var item2 = itemNode.getChildByName("2").getComponent(Label);
            var item3 = itemNode.getChildByName("3").getComponent(Label);
            var item4 = itemNode.getChildByName("4").getComponent(Label);

            item1.string = (new Date(data.CreatedTime || data.ct))?.toLocaleString("en-GB") || "Unknown";
            item3.string = this.isPlayTab ? `#${data.ri}` : data.Balance?.toLocaleString("vi-VN") || "Unknown";
            item4.string = this.isPlayTab ? App.instance.getTextLang(this.playTypes[data.t]) : (this.isCashOutTab ? App.instance.getTextLang('BUY_GAM') : App.instance.getTextLang('BUY_GEM'));
            if (this.isPlayTab) {
                var amount = data.cr - data.b;
                var amountText = amount.toLocaleString("vi-VN");
                if (amount > 0) {
                    amountText = "+" + amountText
                }

                item2.string = amountText;
            } else {
                item2.string = (parseInt(data.Amount)).toLocaleString("vi-VN");
            }
        });
    }
}
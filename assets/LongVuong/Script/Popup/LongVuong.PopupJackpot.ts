import { _decorator, Node, instantiate, Label } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/LongVuongPhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";
import LongVuongLobby from "db://assets/LongVuong/Script/LongVuong.Lobby";
import Configs from "db://assets/Lobby/scripts/common/Config";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("LongVuong/PopupJackpot")
export default class LongVuongPopupJackpot extends Dialog {
    @property([Node])
    activeButtons: Node[] = [];
    @property(Node)
    listContentNode: Node = null;
    @property(Node)
    itemContentNode: Node = null;
    @property(Node)
    listPageNode: Node = null;
    @property(Node)
    itemPageNode: Node = null;
    @property(Node)
    header1: Node = null;
    @property(Node)
    header2: Node = null;

    private currentPage: number = 1;
    private totalPages: number = 1;
    private historyData: any[] = [];
    private pageSize: number = 8;
    private isTopPlayer: boolean = false;

    start() {
        LongVuongPhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Statistic, (res) => {
            if (res.errCode < 0) {
                LongVuongLobby.instance.actShowDialog(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            const keys = Object.keys(res.vals);

            keys.forEach(key => {
                const code = parseInt(key);
                let data = res.vals[key];

                if (code === LongVuongPhotonClient.EParameterCodes.TopJackpotResponse) {
                    this.historyData = JSON.parse(data);
                    this.listPageNode.removeAllChildren();
                    this.currentPage = 1;
                    this.totalPages = 1;
                    if (this.historyData.length === 0) return;
                    this.totalPages = Math.min(10, Math.ceil(this.historyData.length / this.pageSize));
                    this.listPageNode.removeAllChildren();
                    for (let idx = 1; idx <= this.totalPages; idx++) {
                        var pageNode = instantiate(this.itemPageNode);
                        pageNode.getComponentInChildren(Label).string = idx.toString();
                        pageNode.parent = this.listPageNode;
                        pageNode.on("click", () => {
                            this.currentPage = idx;
                            this.showContent(this.currentPage);
                        }, this);
                    }
                    this.showContent(this.currentPage);
                }
            });
        });
    }

    show() {
        super.show();
        this.toggleTab(null, "0");
    }

    toggleTab(_event: any, index: string) {
        this.activeButtons.forEach((button, i) => {
            button.active = i == parseInt(index);
        });
        this.isTopPlayer = false;
        var data: any[] = [LongVuongPhotonClient.EParameterCodes.OperationSubCode];
        switch (index) {
            case "0":
                this.isTopPlayer = true;
                this.header1.active = true;
                this.header2.active = false;
                data.push(LongVuongPhotonClient.EOperationSubCodes.GetTopPlayer);
                break;
            case "1":
                this.header1.active = false;
                this.header2.active = true;
                data.push(LongVuongPhotonClient.EOperationSubCodes.GetTopJackpot);
                break;
        }
        this.listContentNode.removeAllChildren();
        data.push(LongVuongPhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID);
        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Statistic, data, true);
    }

    nextPage() {
        if (this.currentPage == this.totalPages) return;
        this.currentPage++;
        this.showContent(this.currentPage);
    }

    prevPage() {
        if (this.currentPage == 1) return;
        this.currentPage--;
        this.showContent(this.currentPage);
    }

    private showContent(page: number) {
        this.listPageNode.children.forEach((node, index) => {
            node.getChildByName('active').active = (index + 1 == page);
        });

        this.listContentNode.removeAllChildren();
        const startIdx = (page - 1) * this.pageSize;
        const endIdx = startIdx + this.pageSize;
        const paginatedData = this.historyData.slice(startIdx, endIdx);
        paginatedData.forEach((data: any, index: number) => {
            const item = instantiate(this.itemContentNode);
            item.parent = this.listContentNode;
            item.active = true;

            const item1 = item.getChildByName("1").getComponent(Label);
            const item2 = item.getChildByName("2").getComponent(Label);
            const item3 = item.getChildByName("3").getComponent(Label);
            const item4 = item.getChildByName("4");
            item4.active = !this.isTopPlayer;

            const amount = parseFloat(data.PrizeValue);
            item1.string = this.isTopPlayer ? `${startIdx + index + 1}` : (new Date(data.CreatedTime)).toLocaleString("en-GB");
            item2.string = data.Username || data.Nickname || "Unknown";
            item3.string = isNaN(amount) ? "0" : amount.toLocaleString("vi-VN");
            item4.getComponent(Label).string = "Jackpot";
        });
    }
}
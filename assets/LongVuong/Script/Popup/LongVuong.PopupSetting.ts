import { _decorator, Node, sys } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import LongVuongLobby from "db://assets/LongVuong/Script/LongVuong.Lobby";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("LongVuong/PopupSetting")
export default class LongVuongPopupSetting extends Dialog {

    @property(Node)
    iconEffect: Node = null;
    @property(Node)
    btnEffect: Node = null;
    @property(Node)
    iconMusic: Node = null;
    @property(Node)
    btnMusic: Node = null;

    showDialog() {
        var onMusic = sys.localStorage.getItem("SKILL_LV_is_playing") === "true";
        var onEffect = sys.localStorage.getItem("SKILL_LV_is_effect_on") === "true";
        this.iconMusic.getChildByName('on').active = onMusic;
        this.iconMusic.getChildByName('off').active = !onMusic;
        this.btnMusic.getChildByName('on').active = onMusic;
        this.btnMusic.getChildByName('off').active = !onMusic;
        this.iconEffect.getChildByName('on').active = onEffect;
        this.iconEffect.getChildByName('off').active = !onEffect;
        this.btnEffect.getChildByName('on').active = onEffect;
        this.btnEffect.getChildByName('off').active = !onEffect;
    }

    toggleMusic() {
        var isOn = sys.localStorage.getItem("SKILL_LV_is_playing") === "true";
        isOn = !isOn;
        sys.localStorage.setItem("SKILL_LV_is_playing", isOn.toString());

        this.iconMusic.getChildByName('on').active = isOn;
        this.iconMusic.getChildByName('off').active = !isOn;
        this.btnMusic.getChildByName('on').active = isOn;
        this.btnMusic.getChildByName('off').active = !isOn;
        if (isOn) {
            LongVuongLobby.instance.playMusic();
        } else {
            LongVuongLobby.instance.turnOffMusic();
        }
    }

    toggleEffect() {
        var isOn = sys.localStorage.getItem("SKILL_LV_is_effect_on") === "true";
        isOn = !isOn;
        sys.localStorage.setItem("SKILL_LV_is_effect_on", isOn);
        this.iconEffect.getChildByName('on').active = isOn;
        this.iconEffect.getChildByName('off').active = !isOn;
        this.btnEffect.getChildByName('on').active = isOn;
        this.btnEffect.getChildByName('off').active = !isOn;
        // audioEngine.setEffectsVolume(!isOn ? 1 : 0);
    }
}

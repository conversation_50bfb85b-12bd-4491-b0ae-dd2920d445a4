import { _decorator, Node, instantiate, EditBox, Label, UIOpacity } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/LongVuongPhotonClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import LongVuongLobby from "db://assets/LongVuong/Script/LongVuong.Lobby";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/PhotonClient";
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("LongVuong/PopupExchangeCoin")
export default class LongVuongPopupExchangeCoin extends Dialog {

    @property(Node)
    listItems: Node = null;
    @property(Node)
    itemTemplate: Node = null;
    @property(EditBox)
    edbAmount: EditBox = null;
    @property(Node)
    btnSA: Node = null;
    @property(Node)
    btnSAO: Node = null;
    @property(Label)
    lblNotification: Label = null;

    isCashOut = false;
    verifyCode = "";
    cashOutMin = 0;
    cashInMin = 0;

    start () {
        var data = [
            LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.CashoutMin,
            LongVuongPhotonClient.EParameterCodes.PortalId, Configs.Login.PortalID,
        ];
        LongVuongPhotonClient.getInstance().sendOperation(LongVuongPhotonClient.EOperationCodes.Shop, data, true);

        LongVuongPhotonClient.getInstance().addResponseListener(LongVuongPhotonClient.EOperationCodes.Shop, (res: any) => {
           if (res.errCode < 0) {
               LongVuongLobby.instance.actShowDialog(PhotonClient.getErrMsg(res.errCode));
               return;
           }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code != LongVuongPhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (e) {}
                }

                if (code == LongVuongPhotonClient.EParameterCodes.CashInResponse) {
                    LongVuongLobby.instance.actShowDialog(App.instance.getTextLang('fish_pu14'));
                    LongVuongLobby.instance.updateBalance(data.go, data.ge);
                    this.dismiss();
                }

                if (code == LongVuongPhotonClient.EParameterCodes.CashOutResponse) {
                    LongVuongLobby.instance.actShowDialog(App.instance.getTextLang('fish_pu14'));
                    LongVuongLobby.instance.updateBalance(data.go, data.ge);
                    this.dismiss();
                }

                if (code == LongVuongPhotonClient.EParameterCodes.CashoutMinResponse) {
                    this.cashOutMin = JSON.parse(data.value);
                    if (this.isCashOut) {
                        this.updateSAO();

                    }
                }
            });
        });
    }

    showSAO() {
        this.updateSAO();
        super.show();
    }

    showSA() {
        this.updateSA();
        super.show();
    }

    clearEdb() {
        this.edbAmount.string = "";
    }

    updateSA() {
        this.btnSA.active = true;
        this.btnSAO.active = false;
        this.isCashOut = false;
        this.listItems.removeAllChildren();
        var dataSA = LongVuongLobby.instance.clientParameterConfig.sa;
        this.cashInMin = dataSA[0] ?? 1000;
        for (let i = 0; i < dataSA.length; i++) {
            const item = instantiate(this.itemTemplate);
            item.active = true;
            item.getComponentsInChildren(Label).forEach(label => {
                label.string = Utils.formatNumber(dataSA[i]);
            })
            item.parent = this.listItems;

            item.on(Node.EventType.TOUCH_END, () => {
                this.edbAmount.string = dataSA[i].toString();
            });
        }

        this.edbAmount.string = "";
        this.lblNotification.string = App.instance.getTextLang('TLN_MIN_GOLD_TO_CHANGE').replace("{0}", Utils.formatNumber(this.cashInMin));
    }

    updateSAO() {
        this.btnSA.active = false;
        this.btnSAO.active = true;
        this.isCashOut = true;
        this.listItems.removeAllChildren();
        var dataSAO = LongVuongLobby.instance.clientParameterConfig.sao;
        for (let i = 0; i < dataSAO.length; i++) {
            const item = instantiate(this.itemTemplate);
            item.active = true;
            item.getComponentsInChildren(Label).forEach(label => {
                label.string = Utils.formatNumber(dataSAO[i]);
            })
            item.parent = this.listItems;

            if (dataSAO[i] < this.cashOutMin) {
                item.getComponent(UIOpacity).opacity = 100;
            } else {
                item.on(Node.EventType.TOUCH_END, () => {
                    this.edbAmount.string = dataSAO[i].toString();
                });
            }
        }

        this.edbAmount.string = "";
        this.lblNotification.string = App.instance.getTextLang('fish_err999001').replace("{0}", Utils.formatNumber(this.cashOutMin));
    }

    actSubmit() {
        var amount = parseInt(this.edbAmount.string);
        if (this.isCashOut && amount < this.cashOutMin) {
            return;
        } else if (amount < this.cashInMin) {
            return;
        }

        var payload = {
            a: amount,
        }

        var data = [];
        data.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, this.isCashOut ? LongVuongPhotonClient.EOperationSubCodes.CashOutGold : LongVuongPhotonClient.EOperationSubCodes.CashInGold);
        data.push(LongVuongPhotonClient.EParameterCodes.ExchangeMessage, JSON.stringify(payload));
        LongVuongPhotonClient.getInstance().sendOperation(LongVuongPhotonClient.EOperationCodes.Shop, data, true);
    }
}

import { _decorator, Component, Node, Label, Sprite, AudioSource, Prefab, instantiate, sys, log, Tween } from 'cc';
import LongVuongPlay from "db://assets/LongVuong/Script/LongVuong.Play";
import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/LongVuongPhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/PhotonClient";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import AudioManager from "db://assets/Lobby/scripts/common/AudioManager";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("LongVuong/Lobby")
export default class LongVuongLobby extends Component {

    public static instance: LongVuongLobby = null;

    @property(Node)
    playNode: Node = null;

    @property(Label)
    lblNickname: Label = null;
    @property(Sprite)
    avatar: Sprite = null;
    @property(Label)
    lblGemBalanceInfo: Label = null;
    @property(Label)
    lblGoldBalance: Label = null;
    @property(Label)
    lblGemBalance: Label = null;
    @property(Label)
    lblCurrentJackpot: Label = null;

    @property(AudioSource)
    BGMusic1: AudioSource = null;
    @property(AudioSource)
    BGMusic2: AudioSource = null;

    @property(Node)
    popupContainer: Node = null;
    @property(Prefab)
    exchangeCoinPrefab: Prefab = null;
    @property(Prefab)
    popupGuidePrefab: Prefab = null;
    @property(Prefab)
    popupHistoryPrefab: Prefab = null;
    @property(Prefab)
    popupJackpotPrefab: Prefab = null;
    @property(Prefab)
    popupSettingPrefab: Prefab = null;

    @property(Node)
    popupDialog: Node = null;
    @property(Node)
    popupConfirm: Node = null;

    private play: LongVuongPlay = null;
    private photonClient: LongVuongPhotonClient = null;
    private currentMusicIndex: number = 0;
    private visitCount: number = 0;
    listGunConfig = [];
    clientParameterConfig = null;
    doubleConfigs = null;
    currentX2: number = 0;

    onLoad() {
        LongVuongLobby.instance = this;

        var visitCount = parseInt(sys.localStorage.getItem("SKILL_LV_visit_count") || "0", 10);
        visitCount++;
        sys.localStorage.setItem("SKILL_LV_visit_count", visitCount.toString());
        this.visitCount = visitCount;
        this.lblGemBalanceInfo.string = "0";
        this.lblGoldBalance.string = "0";
        this.lblGemBalance.string = "0";
        this.lblCurrentJackpot.string = "0";

        this.play = this.playNode.getComponent(LongVuongPlay);
        this.play.node.active = false;

        if (sys.localStorage.getItem("SKILL_LV_is_playing") === null) {
            sys.localStorage.setItem("SKILL_LV_is_playing", "true");
        }

        if (sys.localStorage.getItem("SKILL_LV_is_effect_on") === null) {
            sys.localStorage.setItem("SKILL_LV_is_effect_on", "true");
        }
    }

    onDestroy() {
        LongVuongPhotonClient.getInstance().peer.disconnect();
    }

    protected start() {
        this.playMusic();
        this.photonClient = LongVuongPhotonClient.getInstance();
        this.photonClient.connect();
        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Account, (res: any) => {
            if (res.errCode < 0) {
                this.actShowDialog(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            log("Logged in successfully");
            App.instance.showLoading(false);
            this.photonClient.isLoggedIn = true;

            this.listGunConfig = JSON.parse(res.vals[LongVuongPhotonClient.EParameterCodes.GunConfigsResponse]);
            this.listGunConfig.sort((a, b) => a.v - b.v);
            this.clientParameterConfig = JSON.parse(res.vals[LongVuongPhotonClient.EParameterCodes.ClientParameterConfig]);
            this.doubleConfigs = JSON.parse(res.vals[LongVuongPhotonClient.EParameterCodes.DoubleConfigsResponse]);

            var IAccountModel = JSON.parse(res.vals[LongVuongPhotonClient.EParameterCodes.LoginResponse]);
            this.currentX2 = IAccountModel.ua;
            this.lblGoldBalance.string = Utils.formatNumber(IAccountModel.go);
            this.lblGemBalance.string = Utils.formatNumber(IAccountModel.ge);
            this.avatar.spriteFrame = App.instance.getAvatarSpriteFrame(IAccountModel.ai);
            this.lblNickname.string = IAccountModel.n;
            this.lblGemBalanceInfo.string = Utils.formatNumber(IAccountModel.go);

            var data = [];
            data.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.GetCurrentJackpot);
            data.push(LongVuongPhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID);
            this.photonClient.sendOperation(PhotonClient.EOperationCodes.Game, data, true);
        });

        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Game, (res: any) => {
            if (res.errCode < 0) {
                this.actShowDialog(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code == LongVuongPhotonClient.EParameterCodes.CurrentJackpot) {
                    // Tween.numberTo(this.lblCurrentJackpot, data.value, 0.3);
                }
            });
        });
    }

    actBack() {
        this.BGMusic1.stop();
        this.BGMusic2.stop();
        App.instance.gotoLobby();
        LongVuongPhotonClient.getInstance().peer.disconnect();
    }

    actPlay() {
        if (!this.photonClient || !this.photonClient.isLoggedIn) {
            return;
        }

        var params = [];
        params.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.JoinRoom);
        this.photonClient.sendOperation(PhotonClient.EOperationCodes.Game, params, true);

        this.playNode.active = true;
        this.show(false);
        // audioEngine.stopMusic();
    }

    show(isShow: boolean) {
        this.node.active = isShow;
        this.playNode.active = !isShow
        if (isShow) {
            // audioEngine.stopAll();
            this.playMusic();
        }
    }

    updateBalance(gold: number, gem: number) {
        this.lblGemBalance.string = Utils.formatNumber(gem);
        this.lblGoldBalance.string = Utils.formatNumber(gold);
        this.lblGemBalanceInfo.string = Utils.formatNumber(gold);
    }

    playMusic() {
        AudioManager.getInstance().turnOffMusic();
        this.currentMusicIndex = this.visitCount % 2;

        var musicToPlay = this.currentMusicIndex === 0 ? this.BGMusic1 : this.BGMusic2;
        var isPlaying = sys.localStorage.getItem("SKILL_LV_is_playing") === "true";

        if (musicToPlay && isPlaying) {
            musicToPlay.play();
        }
    }

    turnOffMusic() {
        sys.localStorage.setItem("SKILL_LV_is_playing", "false");

        // if (this.BGMusic1 && this.BGMusic1.isPlaying) {
        //     this.BGMusic1.stop();
        // }
        //
        // if (this.BGMusic2 && this.BGMusic2.isPlaying) {
        //     this.BGMusic2.stop();
        // }
        //
        // audioEngine.pauseMusic();
    }


    actExchangeCoin(_event: any, flagSAO: string) {
        let exchangeCoin = instantiate(this.exchangeCoinPrefab);
        this.popupContainer.addChild(exchangeCoin);
        if (flagSAO == 'true') {
            // @ts-ignore
            exchangeCoin.getComponent("LongVuong.PopupExchangeCoin").showSAO();
        } else {
            // @ts-ignore
            exchangeCoin.getComponent("LongVuong.PopupExchangeCoin").showSA();
        }
    }

    actShowDialog(message: string) {
        this.popupDialog.active = true;
        // @ts-ignore
        this.popupDialog.getComponent("LongVuong.PopupDialog").showMessage(message);
    }

    hideDialog() {
        if (this.popupDialog.active) {
            this.popupDialog.active = false;
        }
    }

    actShowConfirm(message: string, callback: (isConfirm: boolean) => void) {
        this.popupConfirm.active = true;
        // @ts-ignore
        this.popupConfirm.getComponent("LongVuong.PopupConfirm").showConfirm(message, callback);
    }

    actShowPopupSetting() {
        let popup = instantiate(this.popupSettingPrefab);
        this.popupContainer.addChild(popup);
        // @ts-ignore
        popup.getComponent("LongVuong.PopupSetting").showDialog();
    }

    actShowPopupGuide() {
        let popup = instantiate(this.popupGuidePrefab);
        this.popupContainer.addChild(popup);
        // @ts-ignore
        popup.getComponent("LongVuong.PopupGuide").show();
    }

    actShowPopupHistory() {
        let popup = instantiate(this.popupHistoryPrefab);
        this.popupContainer.addChild(popup);
        // @ts-ignore
        popup.getComponent("LongVuong.PopupHistory").show();
    }

    actShowPopupJackpot() {
        let popup = instantiate(this.popupJackpotPrefab);
        this.popupContainer.addChild(popup);
        // @ts-ignore
        popup.getComponent("LongVuong.PopupJackpot").show();
    }
}

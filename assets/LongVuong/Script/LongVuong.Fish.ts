import {
    _decorator, Component, Node, Vec2, Vec3, instantiate, tween, SpriteFrame,
    Sprite, Animation, AudioClip, AudioSource, Label, Color, UITransform, Tween, UIOpacity
} from 'cc';
import LongVuongPlayer from "db://assets/LongVuong/Script/LongVuong.Player";
import { Utils } from "db://assets/Lobby/scripts/common/Utils";
import LongVuongPlay from "db://assets/LongVuong/Script/LongVuong.Play";
import LongVuongSyncTimeControl from "db://assets/LongVuong/Script/LongVuong.SyncTimeControl";

const { ccclass, property, menu } = _decorator;

export class PathElement {
    public startPath: Vec2;
    public endPath: Vec2;
    public radius: number;
    public duration: number;
    public controlPoint: Vec2;

    constructor(startPath: Vec2, endPath: Vec2, radius: number, duration: number) {
        this.startPath = startPath.clone();
        this.endPath = endPath.clone();
        this.radius = radius;
        this.duration = duration;

        const midPoint = new Vec2();
        Vec2.lerp(midPoint, this.startPath, this.endPath, 0.5);

        const direction = this.endPath.clone().subtract(this.startPath);
        const perpendicular = direction.rotate(Math.PI / 2).normalize().multiplyScalar(this.radius);

        this.controlPoint = midPoint.add(perpendicular);
    }

    public GetCurrentPosition(time: number): Vec2 {
        const t = Math.min(Math.max(time / this.duration, 0), 1);
        const a = this.startPath.clone().multiplyScalar((1 - t) * (1 - t));
        const b = this.controlPoint.clone().multiplyScalar(2 * (1 - t) * t);
        const c = this.endPath.clone().multiplyScalar(t * t);
        return a.add(b).add(c);
    }
}

@ccclass('LongVuongFish')
@menu('LongVuong/Fish')
export default class LongVuongFish extends Component {
    private static instance: LongVuongFish = null;
    public static getInstance(): LongVuongFish {
        return this.instance;
    }

    BOSS_FISH_ID = 1000;
    ELECTRIC_FISH_ID = 101;
    DOUBLE_FISH_ID = 102;

    @property(Node) anim: Node = null;
    @property(Node) explosion: Node = null;
    @property([AudioClip]) engineSounds: AudioClip[] = [];
    @property([SpriteFrame]) sprFramesExplode: SpriteFrame[] = [];
    @property(Node) coinEffect: Node = null;
    @property(Node) prize: Node = null;

    public id: number;
    public isDie = false;
    public type = -1;
    private pathElements: PathElement[] = [];
    private currentMoveTime = 0;
    private isExploring = false;
    private startServerTime: number = 0;
    private elapsedTime = 0;

    onLoad() {
        LongVuongFish.instance = this;
    }

    onDestroy() {
        if (LongVuongFish.instance === this) {
            LongVuongFish.instance = null;
        }
    }

    public die(player: LongVuongPlayer, prize: number) {
        if (this.isDie) return;
        this.isDie = true;

        if (!player) {
            this.node.destroy();
            return;
        }

        this.explodeByGun(player.gunId, () => {
            const prizeNode = instantiate(this.prize);
            prizeNode.setPosition(this.node.getPosition());
            this.node.parent.addChild(prizeNode);
            prizeNode.active = true;
            prizeNode.getComponent(Label).string = Utils.formatNumber(prize);
            tween(prizeNode)
                .to(0.5, { position: new Vec3(prizeNode.position.x, prizeNode.position.y + 100) })
                .call(() => {
                    prizeNode.getComponentInChildren(AudioSource)?.play();
                    prizeNode.destroy();
                })
                .start();

            const coinEffect = instantiate(this.coinEffect);
            coinEffect.setPosition(this.node.getPosition());
            this.node.parent.addChild(coinEffect);
            coinEffect.active = true;

            coinEffect.children.forEach((coin: Node) => {
                const anim = coin.getComponent(Animation);
                anim.play();
                anim.once(Animation.EventType.FINISHED, () => {
                    const worldPos = player.avatarNode.getWorldPosition();
                    const localTarget = coin.parent.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
                    tween(coin)
                        .to(0.5, { position: new Vec3(localTarget.x, localTarget.y, 0) })
                        .call(() => {
                            coin.destroy();
                            this.node.destroy();
                        })
                        .start();
                });
            });
        });

        if (this.type > 12 && this.type !== this.BOSS_FISH_ID && this.type !== this.DOUBLE_FISH_ID) {
            const winEffect = this.type === this.ELECTRIC_FISH_ID
                ? instantiate(LongVuongPlay.instance.winElectric)
                : instantiate(LongVuongPlay.instance.bigWinFish);
            this.node.parent.addChild(winEffect);
            winEffect.setPosition(this.node.getPosition());
            winEffect.getComponentInChildren(Label).string = prize.toLocaleString("vi-VN");
            winEffect.active = true;
            setTimeout(() => winEffect.destroy(), 2000);
        } else if (this.type === this.BOSS_FISH_ID && LongVuongPlay.instance.isMePlayer(player.id)) {
            const jackpotNode = LongVuongPlay.instance.jackpotNode;
            jackpotNode.active = true;
            const labelJP = jackpotNode.getComponentInChildren(Label);
            labelJP.string = '0';
            // Tween.numberTo(labelJP, prize, 0.3);
            tween(jackpotNode)
                .repeatForever(
                    tween().to(0.5, { scale: new Vec3(1.2, 1.2, 1) }).to(0.5, { scale: new Vec3(1, 1, 1) })
                ).start();
            setTimeout(() => {
                jackpotNode.active = false;
                Tween.stopAllByTarget(jackpotNode);
            }, 2000);
        } else if (this.type === this.BOSS_FISH_ID) {
            player.showJackpotMini();
            setTimeout(() => player.hideJackpotMini(), 2000);
        }
    }

    public explodeByGun(gunId: number, callback?: () => void) {
        if (this.isExploring) return callback?.();
        this.isExploring = true;

        const explosionNode = instantiate(this.explosion);
        explosionNode.setPosition(this.node.getPosition());
        this.node.parent.addChild(explosionNode);
        explosionNode.getComponent(Sprite).spriteFrame = this.getExplosionSprite(gunId);
        explosionNode.active = true;
        explosionNode.setScale(new Vec3(0.8, 0.8, 1));

        let opacityComp = explosionNode.getComponent(UIOpacity);
        if (!opacityComp) {
            opacityComp = explosionNode.addComponent(UIOpacity);
        }
        opacityComp.opacity = 0;

        tween(opacityComp)
            .to(0.1, { opacity: 255 })
            .start();

        tween(explosionNode)
            .delay(0.07)
            .to(0.2, { scale: new Vec3(1, 1, 1) })
            .start();

        tween(opacityComp)
            .delay(0.4)
            .to(0.3, { opacity: 0 })
            .call(() => {
                explosionNode.destroy();
                this.isExploring = false;
                callback?.();
            })
            .start();
    }

    private getExplosionSprite(gunId: number): SpriteFrame {
        return this.sprFramesExplode[gunId - 1] || this.sprFramesExplode[0];
    }

    public setData(data: any) {
        this.id = data.i;
        this.isDie = false;
        this.node.active = true;
        this.type = data.t;
        this.anim.removeAllChildren();
        this.isExploring = false;

        const animNode = instantiate(LongVuongPlay.instance.getFishAnimByType(this.type));
        animNode.setPosition(Vec3.ZERO);
        animNode.parent = this.anim;

        this.pathElements = data.p.p.map((p: any, index: number) => {
            if (index < data.p.p.length - 1) {
                return new PathElement(
                    new Vec2(p.x, p.y),
                    new Vec2(data.p.p[index + 1].x, data.p.p[index + 1].y),
                    data.p.r[index],
                    data.p.d[index]
                );
            }
        }).filter(Boolean);

        this.currentMoveTime = 0;
        this.startServerTime = new Date(data.ct).getTime();
        const serverNow = LongVuongSyncTimeControl.instance.getCurrentServerTime();
        this.elapsedTime = Math.max((serverNow - this.startServerTime) / 1000, 0);

        if (serverNow - this.startServerTime >= 0) {
            this.updateRealTime(0);
        }
    }

    public updateRealTime(dt: number) {
        if (!this.node.active || this.isDie || this.pathElements.length < 1) return;

        this.elapsedTime += dt;
        const totalDuration = this.pathElements.reduce((sum, p) => sum + p.duration, 0);

        if ([this.BOSS_FISH_ID, this.DOUBLE_FISH_ID, this.ELECTRIC_FISH_ID].includes(this.type) && this.elapsedTime >= totalDuration) {
            this.node.destroy();
            return;
        }

        this.currentMoveTime = [this.BOSS_FISH_ID, this.DOUBLE_FISH_ID, this.ELECTRIC_FISH_ID].includes(this.type)
            ? this.elapsedTime : this.elapsedTime % totalDuration;

        let totalTime = 0;
        let currentPath = null;

        for (const path of this.pathElements) {
            if (this.currentMoveTime < totalTime + path.duration) {
                currentPath = path;
                break;
            }
            totalTime += path.duration;
        }

        if (currentPath) {
            const timeInPath = this.currentMoveTime - totalTime;
            const pos = currentPath.GetCurrentPosition(timeInPath);
            let direction = currentPath.endPath.clone().subtract(currentPath.startPath);
            let angle = Math.atan2(direction.y, direction.x);

            let scaleX = 1, scaleY = 1;
            if (direction.x < 0) {
                scaleX = -1;
                angle += Math.PI;
            }

            let posX = pos.x, posY = pos.y;
            const serverPos = LongVuongPlay.instance.mePlayer.serverPos;
            switch (serverPos) {
                case 2:
                    posX = -posX;
                    scaleX = -scaleX;
                    angle = -angle;
                    break;
                case 3:
                    posX = -posX;
                    posY = -posY;
                    scaleY = -scaleY;
                    angle += Math.PI;
                    break;
                case 4:
                    posY = -posY;
                    scaleY = -scaleY;
                    scaleX = -scaleX;
                    angle = -(angle + Math.PI);
                    break;
            }

            if ([101, 102].includes(this.type) && [3, 4].includes(serverPos)) {
                angle = -angle;
            }

            angle = (angle * 180) / Math.PI;
            this.node.setPosition(new Vec3(posX, posY, 0));
            this.node.setRotationFromEuler(0, 0, angle);
            this.node.setScale(new Vec3(scaleX, scaleY, 1));
        }
    }

    public hurt() {
        const hurtNode = this.anim.children[0]?.children[0];
        if (!hurtNode) return;

        tween(hurtNode.getComponent(Sprite))
            .to(0.05, { color: new Color(255, 54, 54) })
            .delay(0.1)
            .to(0.05, { color: new Color(255, 255, 255) })
            .start();
    }

    public startEngineSound() {
        // Use AudioSource component if applicable
    }

    public stopEngineSound() {
        // Stop playing sound if using AudioSource
    }
}
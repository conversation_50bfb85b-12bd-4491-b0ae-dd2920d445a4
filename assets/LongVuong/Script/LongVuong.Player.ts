import { _decorator, Component, Label, Node, Sprite, Vec2, Vec3, tween, Tween, UIOpacity } from 'cc';
import Configs from 'db://assets/Lobby/scripts/common/Config';
import App from 'db://assets/Lobby/scripts/common/App';
import { Utils } from 'db://assets/Lobby/scripts/common/Utils';
import LongVuongPlay from 'db://assets/LongVuong/Script/LongVuong.Play';
import LongVuongLobby from 'db://assets/LongVuong/Script/LongVuong.Lobby';

const { ccclass, property, menu } = _decorator;

@ccclass('LongVuongPlayer')
@menu('LongVuong/Player')
export default class LongVuongPlayer extends Component {

    @property(Label)
    lblNickname: Label = null;

    @property(Label)
    lblCoin: Label = null;

    @property(Label)
    lblBet: Label = null;

    @property(Node)
    avatarNode: Node = null;

    @property(Sprite)
    avatarSprite: Sprite = null;

    @property(Node)
    circle: Node = null;

    @property(Node)
    gunContainer: Node = null;

    @property(Node)
    fireEffect: Node = null;

    @property(Node)
    jackpotMiniNode: Node = null;

    public id = '';
    public username = '';
    public nickname = '';
    public coin = 0;
    public avatar = '';
    public serverPos = -1;
    public localPos = -1;

    gunId = 1;

    isMePlayer(): boolean {
        return this.id === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`;
    }

    setData(data: any) {
        this.id = data.id;
        this.nickname = data.n;
        this.coin = data.go;
        this.avatar = data.a;
        this.gunContainer.setRotationFromEuler(0, 0, 90); // replaces angle = 90
        this.node.active = true;
        this.serverPos = data.p;
        this.gunId = data.gi;
        this.lblNickname.string = this.nickname;
        this.avatarSprite.spriteFrame = App.instance.getAvatarSpriteFrame(this.avatar);
        // Tween.numberTo(this.lblCoin, this.coin, 0.2);
        this.setGun(this.gunId);

        if (!this.circle) return;

        Tween.stopAllByTarget(this.circle);
        this.circle.setScale(new Vec3(1, 1, 1));
        this.circle.getComponent(UIOpacity).opacity = 255;

        let tweenSequence: Tween<any> | Tween<Node>;

        if (this.isMePlayer()) {
            tweenSequence = tween()
                .to(0.5, { scale: new Vec3(0.9, 0.9, 1) })
                .to(0.5, { scale: new Vec3(1, 1, 1) });
        } else {
            tweenSequence = tween()
                .to(0.5, { opacity: 150 })
                .to(0.5, { opacity: 255 });
        }

        tween(this.circle)
            .repeatForever(tweenSequence)
            .start();
    }

    updateGold(coin: number) {
        this.coin = coin;
        // Tween.numberTo(this.lblCoin, this.coin, 0.2);
    }

    leave() {
        this.id = '';
        this.nickname = '';
        this.coin = 0;
        this.avatar = '';
        this.node.active = false;
        this.serverPos = -1;
        this.localPos = -1;
        this.gunId = 0;
        if (this.circle) {
            Tween.stopAllByTarget(this.circle);
        }
    }

    setGun(gunId: number) {
        const gunChildren = this.gunContainer.children[0].children;
        gunChildren.forEach((gun, index) => {
            gun.active = index === gunId - 1;
        });

        this.gunId = gunId;

        const obj = LongVuongLobby.instance.doubleConfigs.find((config: any) => config.Limit === gunId);
        if (obj) {
            LongVuongPlay.instance.updateX2Object(obj.Amount);
        }

        const configGun = LongVuongLobby.instance.listGunConfig.find(gun => gun.i === gunId);
        if (configGun) {
            this.lblBet.string = Utils.formatNumber(configGun.v);

            if (this.id === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
                LongVuongPlay.instance.updateJackpotObject(null, configGun.j);
            }
        }
    }

    rotateGun(touchPos: Vec2) {
        const gunWorldPos = this.gunContainer.getWorldPosition();
        const diff = new Vec2(touchPos.x - gunWorldPos.x, touchPos.y - gunWorldPos.y);
        let angle = Math.atan2(diff.y, diff.x) * Utils.Rad2Deg;

        if (angle < -90) {
            angle = 180;
        } else if (angle < 0 && angle > -90) {
            angle = 0;
        }

        this.gunContainer.setRotationFromEuler(0, 0, angle);
    }

    rotateGunByAngle(angle: number) {
        this.gunContainer.setRotationFromEuler(0, 0, angle);
    }

    startAnimFireGun() {
        const originalPos = this.gunContainer.getPosition();
        const upY = originalPos.y + 6;
        const downY = originalPos.y - 8;

        tween(this.gunContainer)
            .to(0.06, {
                position: new Vec3(originalPos.x, upY, originalPos.z),
                scale: new Vec3(1.05, 1.05, 1),
            }, { easing: 'quadOut' })
            .to(0.08, {
                position: new Vec3(originalPos.x, downY, originalPos.z),
                scale: new Vec3(1.2, 1.2, 1),
            }, { easing: 'quadIn' })
            .to(0.12, {
                position: originalPos,
                scale: new Vec3(1, 1, 1),
            }, { easing: 'quadOut' })
            .start();
    }

    showJackpotMini() {
        if (this.jackpotMiniNode) {
            this.jackpotMiniNode.active = true;
            tween(this.jackpotMiniNode)
                .repeatForever(
                    tween()
                        .to(0.5, { scale: new Vec3(1.2, 1.2, 1), opacity: 150 })
                        .to(0.5, { scale: new Vec3(1, 1, 1), opacity: 255 })
                )
                .start();
        }
    }

    hideJackpotMini() {
        if (this.jackpotMiniNode) {
            Tween.stopAllByTarget(this.jackpotMiniNode);
            this.jackpotMiniNode.active = false;
        }
    }
}
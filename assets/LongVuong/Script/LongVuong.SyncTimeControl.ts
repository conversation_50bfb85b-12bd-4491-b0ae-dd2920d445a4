import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/LongVuongPhotonClient";
import {PhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/PhotonClient";

export default class LongVuongSyncTimeControl {
    private static _instance: LongVuongSyncTimeControl;
    public static get instance(): LongVuongSyncTimeControl {
        if (!this._instance) {
            this._instance = new LongVuongSyncTimeControl();
        }
        return this._instance;
    }

    private _differenceTimeClientServer: number = 0;
    private _deltaPingTime: number = 0;
    private _pingIntervalId: number = null;
    private _sendPingTime: number = 0;

    public setReceivePingTime(receiveServerTime: string): void {
        const nowClient = Date.now();
        const serverTime = new Date(receiveServerTime).getTime();
        this._deltaPingTime = (nowClient - this._sendPingTime) / 2;
        this._differenceTimeClientServer = serverTime - nowClient - this._deltaPingTime;

        this.sendPong(this._deltaPingTime);
    }

    public getCurrentServerTime(): number {
        return Date.now() + this._differenceTimeClientServer;
    }

    public resetPingOffline(): void {
        this._differenceTimeClientServer = 0;
        if (this._pingIntervalId !== null) {
            clearInterval(this._pingIntervalId);
            this._pingIntervalId = null;
        }
    }

    public startPingServer(): void {
        this.sendPing();
        this._pingIntervalId = setInterval(() => this.sendPing(), 1000);
    }

    private sendPing(): void {
        var data = [];
        data.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.Ping);
        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);
        this._sendPingTime = Date.now();
    }

    private sendPong(pingTime: number): void {
        var data = [];
        data.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.Pong);
        data.push(LongVuongPhotonClient.EParameterCodes.PongMessage, pingTime);
        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);
    }
}
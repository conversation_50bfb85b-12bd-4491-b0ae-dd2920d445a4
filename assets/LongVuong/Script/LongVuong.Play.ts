import { _decorator, Component, Node, Label, AudioClip, Toggle, Prefab, instantiate, sys, EventTouch, Vec3, v2, v3, SpriteFrame, Sprite, RichText, tween, Button, UITransform, UIOpacity, PolygonCollider2D } from 'cc';
import LongVuongPlayer from "db://assets/LongVuong/Script/LongVuong.Player";
import LongVuongLobby from './LongVuong.Lobby';
import LongVuongBullet from "db://assets/LongVuong/Script/LongVuong.Bullet";
import LongVuongFish from "db://assets/LongVuong/Script/LongVuong.Fish";
import {LongVuongPhotonClient} from "db://assets/Lobby/scripts/common/networks/skills/LongVuongPhotonClient";
import { PhotonClient } from '../../Lobby/scripts/common/networks/skills/PhotonClient';
import LongVuongSyncTimeControl from "db://assets/LongVuong/Script/LongVuong.SyncTimeControl";
import App from "db://assets/Lobby/scripts/common/App";
import LongVuongChat from "db://assets/LongVuong/Script/LongVuong.Chat";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import Configs from "db://assets/Lobby/scripts/common/Config";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu('LongVuong/Play')
export default class LongVuongPlay extends Component {

    public static instance: LongVuongPlay = null;

    @property(Node)
    BG1: Node = null;
    @property(Node)
    BG2: Node = null;
    @property(Label)
    lblRoomId: Label = null;
    @property(Node)
    lobbyNode: Node = null;
    @property(Label)
    lblJackpot: Label = null;
    @property(Label)
    lblJackpotPerGun: Label = null;
    @property(Label)
    lblPercentJackpot: Label = null;
    @property(Node)
    touchPad: Node = null;
    @property([LongVuongPlayer])
    players: LongVuongPlayer[] = [];
    @property(Node)
    containerFish: Node = null;
    @property(Node)
    containerFishTemplate: Node = null;
    @property(Node)
    fishTemplate: Node = null;
    @property(Node)
    bulletTemplate: Node = null;
    @property(Toggle)
    toggleAuto: Toggle = null;

    @property(AudioClip)
    changeGunSound: AudioClip = null;
    @property([AudioClip])
    playSounds: AudioClip[] = [];

    @property(Node)
    chatNode: Node = null;
    @property(Node)
    panelMenu: Node = null;
    @property(Prefab)
    popupGuidePrefab: Prefab = null;
    @property(Prefab)
    popupSettingPrefab: Prefab = null;
    @property(Node)
    popupContainer: Node = null;
    @property(RichText)
    toast: RichText = null;
    @property(Node)
    bigWinFish: Node = null;
    @property(Node)
    winElectric: Node = null;

    @property(Node)
    targetNode: Node = null;
    @property(SpriteFrame)
    iconTarget: SpriteFrame = null;
    @property(Label)
    lblCountItemTarget: Label = null;
    @property(Sprite)
    targetTimeCounter: Sprite = null;
    @property(Label)
    lblCurrentX2: Label = null;
    @property(Sprite)
    x2BarSprite: Sprite = null;
    @property(Node)
    containerX2: Node = null;
    @property(Node)
    listX2Value: Node = null;
    @property(Node)
    templateX2Value: Node = null;

    @property(Node)
    jackpotNode: Node = null;

    public lobby: LongVuongLobby = null;
    public mePlayer: LongVuongPlayer = null;
    private bullets: LongVuongBullet[] = [];
    private listFish: LongVuongFish[] = [];
    private isShoot = false;
    public isTargetFish = false;
    public targetFish: LongVuongFish = null;
    private intervalFindTargetFish = 2;
    private curIntervalFindTargetFish = 0;
    private shootCooldown: number = 0;
    private shootInterval: number = 0.5;
    public shootingId = "";
    private timeNoAction = 0;
    private playCount = 0;
    private currentJackpot = 0;
    private currentJackpotPercent = 0;
    private countItemTarget = 0;
    private timeToUseTarget = 0;
    private allowUseItemTarget = false;
    private currentTimeToUseTarget = 0;
    private x2LimitAmount = 1;
    private x2CurrentAmount = 0;
    private enableX2 = false;

    onLoad() {
        LongVuongPlay.instance = this;
        this.lobby = this.lobbyNode.getComponent(LongVuongLobby);
        this.lblRoomId.string = "#0";
        this.lblCountItemTarget.string = "0";
        this.lblJackpotPerGun.string = "0";
        this.lblJackpot.string = "0";
        this.lblPercentJackpot.string = "0%";
        this.players.forEach((player: LongVuongPlayer) => {
            player.leave();
        });

    }

    protected onEnable() {
        this.containerFish.removeAllChildren();
        this.listFish = [];
        this.bullets = [];
        for (let i = 0; i < this.players.length; i++) {
            this.players[i].leave();
        }
        this.mePlayer = this.players[0];
        this.allowUseItemTarget = true;
    }

    updateX2Object(x2Limit: number = null) {
        if (x2Limit != null) {
            this.x2LimitAmount = x2Limit;
        }
        var range = Math.min(100, Math.round(this.x2CurrentAmount / this.x2LimitAmount * 100));
        this.lblCurrentX2.string = range.toString();
        this.x2BarSprite.fillRange = range / 100;
    }

    start() {
        this.jackpotNode.active = false;
        this.hidePanel();
        this.containerX2.active = false;
        this.lblCountItemTarget.string = "0";
        this.countItemTarget = 0;
        var playCount = parseInt(sys.localStorage.getItem("SKILL_LV_play_count") || "0", 10);
        playCount++;
        sys.localStorage.setItem("SKILL_LV_play_count", playCount.toString());
        this.playCount = playCount;
        this.toast.node.active = false;
        this.timeNoAction = 0;
        this.panelMenu.x = 120;
        this.panelMenu.getChildByName("MenuArrow").setScale(-1, -1);

        this.touchPad.on(Node.EventType.TOUCH_START, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.mePlayer.rotateGun(touchPos);
            this.isShoot = true;
        }, this.touchPad);

        this.touchPad.on(Node.EventType.TOUCH_MOVE, (event: EventTouch) => {
            var touchPos = event.getLocation();
            this.mePlayer.rotateGun(touchPos);
        }, this.touchPad);

        this.touchPad.on(Node.EventType.TOUCH_END, (_event: EventTouch) => {
            this.isShoot = false;
        }, this.touchPad);

        this.touchPad.on(Node.EventType.TOUCH_CANCEL, (_event: EventTouch) => {
            this.isShoot = false;
        }, this.touchPad);

        this.toggleAuto.node.on("toggle", () => {
            if (this.toggleAuto.isChecked) {
                this.touchPad.active = false;
                this.curIntervalFindTargetFish = this.intervalFindTargetFish;
                this.isShoot = true;
                this.findFishInWorld();
            } else {
                this.stopAutoShoot();
            }
        });

        LongVuongPhotonClient.getInstance().handleErrorResponse(() => {
            LongVuongSyncTimeControl.instance.resetPingOffline();
            this.node.active = false;
            this.lobby.show(true);
        });

        LongVuongPhotonClient.getInstance().handleTimeoutResponse(() => {
            LongVuongSyncTimeControl.instance.resetPingOffline();
            this.node.active = false;
            this.lobby.show(true);
        });

        LongVuongPhotonClient.getInstance().addResponseListener(PhotonClient.EOperationCodes.Game, (res: any) => {
            if (res.errCode < 0) {
                this.lobby.actShowDialog(PhotonClient.getErrMsg(res.errCode));
                this.back();
                return;
            }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code != LongVuongPhotonClient.EParameterCodes.OperationSubCode) {
                    try {
                        data = JSON.parse(data);
                    } catch (e) {}
                }

                if (code == LongVuongPhotonClient.EParameterCodes.PingResponse) {
                    LongVuongSyncTimeControl.instance.setReceivePingTime(data);
                }

                if (code == LongVuongPhotonClient.EParameterCodes.LeaveRoomResponse) {
                    if (this.isMePlayer(data.id)) {
                        for (let i = 0; i < this.players.length; i++) {
                            this.players[i].leave();
                        }

                        LongVuongSyncTimeControl.instance.resetPingOffline();
                        this.node.active = false;
                        this.lobby.show(true);
                        this.lobby.updateBalance(data.go, data.ge);
                        this.unscheduleAllCallbacks();
                    } else {
                        var leavePlayer = this.getPlayerById(data.id);
                        if (leavePlayer) {
                            this.showToast(`<color=#ffff00>${leavePlayer.nickname}</c><color=#ffffff>${App.instance.getTextLang('vc19').replace('{0}', '')}</color>`);
                            leavePlayer.leave();
                        }
                    }
                }

                if (code == LongVuongPhotonClient.EParameterCodes.JoinRoomResponse) {
                    LongVuongSyncTimeControl.instance.startPingServer();

                    this.lblRoomId.string = `#${data.r}`
                    var mePlayerData = data.a.find((player: any) => this.isMePlayer(player.id));
                    this.mePlayer.setData(mePlayerData);
                    this.mePlayer.localPos = 1;
                    this.countItemTarget = mePlayerData.Items[0];
                    this.lblCountItemTarget.string = this.countItemTarget.toString();
                    this.x2CurrentAmount = this.lobby.currentX2;
                    this.updateX2Object();

                    var posMe = mePlayerData.p;

                    var playerData: any;
                    for (let pos = 1; pos <= 4; pos++) {
                        if (pos != posMe && pos != 1) {
                            playerData = data.a.find((player: any) => player.p == pos);
                            if (playerData) {
                                this.players[pos - 1].setData(playerData);
                                this.players[pos - 1].localPos = pos;
                            }
                        }
                    }

                    if (posMe != 1) {
                        playerData = data.a.find((player: any) => player.p == 1);
                        if (playerData) {
                            this.players[posMe - 1].setData(playerData);
                            this.players[posMe - 1].localPos = posMe;
                        }
                    }

                    for (let i = 0; i < data.f.length; i++) {
                        let fishNode = instantiate(this.fishTemplate);
                        let fish = fishNode.getComponent(LongVuongFish);
                        fish.node.parent = this.containerFish;
                        fish.setData(data.f[i]);
                        this.listFish.push(fish);
                    }

                    this.updateJackpotObject(data.j, null);
                }

                if (code == LongVuongPhotonClient.EParameterCodes.OtherJoinRoomResponse) {
                    var playerOJR: LongVuongPlayer;
                    if (data.p === 1) {
                        playerOJR = this.players[this.mePlayer.serverPos - 1];
                        playerOJR.localPos = this.mePlayer.serverPos;
                    } else {
                        playerOJR = this.players[data.p - 1];
                        playerOJR.localPos = data.p;
                    }
                    playerOJR.setData(data);
                    this.showToast(`<color=#ffff00>${data.n}</c><color=#ffffff>${App.instance.getTextLang('vc18').replace('{0}', '')}</color>`);
                }

                if (code == LongVuongPhotonClient.EParameterCodes.ChatResponse) {
                    LongVuongChat.instance.appendMessage(data);
                }

                if (code == LongVuongPhotonClient.EParameterCodes.ChangeGunResponse) {
                    var playerChangeGun = this.getPlayerById(data.i);
                    if (playerChangeGun) {
                        playerChangeGun.setGun(data.gi);
                        // audioEngine.playEffect(this.changeGunSound, false);
                    }
                }

                if (code === LongVuongPhotonClient.EParameterCodes.CreateFishResponse) {
                    for (let i = 0; i < data.length; i++) {
                        let fishNode = instantiate(this.fishTemplate);
                        let fish = fishNode.getComponent(LongVuongFish);
                        fish.node.parent = this.containerFish;
                        fish.setData(data[i]);
                        this.listFish.push(fish);
                    }
                }

                if (code === LongVuongPhotonClient.EParameterCodes.RemoveSingleFishResponse) {
                    var fishRSF = this.getFishById(data);
                    if (fishRSF && fishRSF.node) {
                        fishRSF.node.destroy();
                    }
                }

                if (code === LongVuongPhotonClient.EParameterCodes.ShootingResponse) {
                    if (this.isMePlayer(data.i)) {
                        this.timeNoAction = 0;
                    }
                    var playerSR = this.getPlayerById(data.i);
                    if (playerSR) {
                        playerSR.updateGold(data.g);

                        // update bullet
                        var shootingRes = data.s;
                        var shootingPos = parseInt(shootingRes.id.charAt(0));
                        var shootingPlayer = this.getPlayerByPos(shootingPos);
                        if (shootingPlayer) {
                            let bullet = this.getBullet(shootingPos, shootingRes.gi);

                            var vx = shootingRes.vx;
                            var vy = shootingRes.vy;

                            const magnitude = Math.sqrt(vx * vx + vy * vy);
                            if (magnitude > 1.5) {
                                vx /= magnitude;
                                vy /= magnitude;
                            }
                            let rad = Math.atan2(vy, vx);
                            let radByMe = rad;
                            bullet.node.angle = radByMe * Utils.Rad2Deg;

                            switch (shootingPlayer.localPos) {
                                case 1:
                                    radByMe = rad;
                                    break;
                                case 2:
                                    radByMe = Math.PI - rad;
                                    bullet.node.angle = radByMe * Utils.Rad2Deg;
                                    break;
                                case 3:
                                    radByMe = rad;
                                    bullet.node.angle = (radByMe + Math.PI) * Utils.Rad2Deg;
                                    break;
                                case 4:
                                    radByMe = Math.PI - rad;
                                    bullet.node.angle = (radByMe + Math.PI) * Utils.Rad2Deg;
                                    break;
                            }

                            shootingPlayer.rotateGunByAngle(radByMe * Utils.Rad2Deg);

                            var pos = bullet.node.parent.getComponent(UITransform).convertToNodeSpaceAR(shootingPlayer.fireEffect.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO));
                            bullet.node.setPosition(pos);
                            bullet.run();
                            shootingPlayer.startAnimFireGun();
                        }
                    }

                    this.updateJackpotObject(data.j, null);
                }

                if (code === LongVuongPhotonClient.EParameterCodes.TimeToUse) {
                    this.countItemTarget -= 1;
                    this.lblCountItemTarget.string = this.countItemTarget.toString();
                    this.isShoot = false;
                    const useFireAuto = this.toggleAuto.isChecked;
                    this.toggleAuto.isChecked = false;
                    this.toggleAuto.enabled = false;
                    this.touchPad.active = false;
                    this.mePlayer.node.getChildByName('lock').active = true;
                    this.listFish.forEach((fish: LongVuongFish) => {
                        var fishBtn = fish.getComponentInChildren(Button).node;
                        fishBtn.parent.getChildByName('target').active = false;
                        fishBtn.off("click");
                        fishBtn.on("click", () => {
                            this.listFish.forEach((fish: LongVuongFish) => {
                                fish.getComponentInChildren(Button).node.parent.getChildByName('target').active = false;
                            });
                            fishBtn.parent.getChildByName('target').active = true;
                            this.targetFish = fish;
                            this.toggleAuto.isChecked = true;
                            this.isShoot = true;
                            this.isTargetFish = true;
                        });
                    });

                    this.targetTimeCounter.node.active = true;
                    this.timeToUseTarget = data;
                    this.currentTimeToUseTarget = data;
                    this.scheduleOnce(() => {
                        this.allowUseItemTarget = true;
                        this.timeToUseTarget = 0;
                        this.currentTimeToUseTarget = 0;
                        this.targetTimeCounter.node.active = false;
                        this.touchPad.active = true;
                        this.toggleAuto.enabled = true;
                        this.isShoot = false;
                        this.isTargetFish = false;
                        this.targetFish = null;
                        this.listFish.forEach((fish: LongVuongFish) => {
                            fish.getComponentInChildren(Button).node.parent.getChildByName('target').active = false;
                        });
                        this.mePlayer.node.getChildByName('lock').active = false;
                        this.toggleAuto.isChecked = useFireAuto;
                        if (useFireAuto) {
                            this.touchPad.active = false;
                            this.curIntervalFindTargetFish = this.intervalFindTargetFish;
                            this.isShoot = true;
                            this.findFishInWorld();
                        }
                    }, data);
                }

                if (code === LongVuongPhotonClient.EParameterCodes.HeadShotResponse) {
                    this.headShotResponseHandle(data);
                }

                if (code === LongVuongPhotonClient.EParameterCodes.DoubleValuesCanPlay) {
                    this.listX2Value.removeAllChildren();
                    data.forEach((value: any) => {
                        var valueX2Node = instantiate(this.templateX2Value);
                        valueX2Node.getComponentInChildren(Label).string = value.toLocaleString("vi-VN");
                        valueX2Node.active = true;
                        valueX2Node.on("click", () => {
                            var params = [];
                            params.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.PlayDouble);
                            params.push(LongVuongPhotonClient.EParameterCodes.DoubleMessage, value);
                            LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
                        });
                        this.listX2Value.addChild(valueX2Node);
                    });
                    this.containerX2.active = true;
                }

                if (code === LongVuongPhotonClient.EParameterCodes.DoubleMessageResponse) {
                    this.x2CurrentAmount = data.a;
                    this.updateX2Object();
                    if (data.r > 0) {
                        this.showToast(App.instance.getTextLang('txt_reward') + ` ${data.r}`);
                    }

                    this.mePlayer.updateGold(data.go);
                }
            });

        });

        this.updateSoundMusic();
    }

    isMePlayer(id: string): boolean {
        return id === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`;
    }

    onDestroy() {
        // audioEngine.stopAll();
    }

    update(dt: number) {
        this.enableX2 = this.x2CurrentAmount >= this.x2LimitAmount;
        this.updateShoot(dt);

        if (this.currentTimeToUseTarget > 0) {
            this.currentTimeToUseTarget -= dt;
            this.targetTimeCounter.fillRange = 1 - (this.currentTimeToUseTarget / this.timeToUseTarget);
        }

        this.timeNoAction += dt;
        if (this.timeNoAction > 80) {
            this.timeNoAction = 0;
            this.lobby.actShowDialog(App.instance.getTextLang('fish_pu7'));
            this.scheduleOnce(() => {
                this.lobby.hideDialog();
            }, 3);
        }

        if (this.shootCooldown > 0) {
            this.shootCooldown -= dt;
        }
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            if (bullet.node == null || !bullet.node.active) {
                this.bullets.splice(i, 1);
            } else {
                bullet.updateRealTime(dt);
            }
        }

        for (let i = this.listFish.length - 1; i >= 0; i--) {
            const fish = this.listFish[i];
            if (fish.node == null || !fish.node.active) {
                this.listFish.splice(i, 1);
            } else {
                fish.updateRealTime(dt);
            }
        }
    }

    headShotResponseHandle(data: any) {
        var playerHSR = this.getPlayerById(data.i);
        if (playerHSR) {
            playerHSR.updateGold(data.go);
        }

        const airplane0 = this.getFishById(data.f[0]);
        const position0 = airplane0?.node.position;
        var nodeWinText = new Node();
        for (let i = 0; i < data.f.length; i++) {
            let fish = this.getFishById(data.f[i]);
            if (fish && playerHSR) {
                nodeWinText.position = fish.node.position;
                fish.die(playerHSR, data.gor);
            }
        }

        this.updateJackpotObject(data.j, null);

        if (this.isMePlayer(data.i)) {
            if (data.bi && Object.keys(data.bi).length > 0) {
                for (const key in data.bi) {
                    if (data.bi.hasOwnProperty(key)) {
                        let bonusItem = data.bi[key];
                        this.countItemTarget += bonusItem[0];

                        if (bonusItem[0] > 0) {
                            const endWorldPos = this.targetNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                            const endLocalPos = this.containerFish.getComponent(UITransform).convertToNodeSpaceAR(endWorldPos);

                            const bonusNode = new Node();
                            bonusNode.addComponent(Sprite).spriteFrame = this.iconTarget;
                            bonusNode.setPosition(position0);
                            this.containerFish.addChild(bonusNode);

                            tween(bonusNode)
                                .to(1, { position: endLocalPos })
                                .call(() => {
                                    bonusNode.destroy();
                                    this.lblCountItemTarget.string = this.countItemTarget.toString();
                                })
                                .start();
                        }
                    }
                }
            }

            this.x2CurrentAmount = data.a;
            this.updateX2Object();
        }
    }

    updateSoundMusic() {
        let playSound = this.playSounds[this.playCount % 3];
        if (this.playCount % 2 === 0) {
            this.BG1.active = false;
            this.BG2.active = true;
        } else {
            this.BG1.active = true;
            this.BG2.active = false;
        }
        var isPlaying = sys.localStorage.getItem("SKILL_LV_is_playing") === "true";
        var isEffectOn = sys.localStorage.getItem("SKILL_LV_is_effect_on") === "true";
        if (playSound && isPlaying) {
            // audioEngine.playMusic(playSound, true);
        }

        // audioEngine.setEffectsVolume(isEffectOn ? 1 : 0);
    }

    togglePlayMusic() {
        var isPlaying = sys.localStorage.getItem("SKILL_LV_is_playing") === "true";
        isPlaying = !isPlaying;
        sys.localStorage.setItem("SKILL_LV_is_playing", isPlaying ? "true" : "false");

        if (isPlaying) {
            var playSound = this.playSounds[this.playCount % 3];
            if (playSound) {
                // audioEngine.playMusic(playSound, true);
            }
        } else {
            // audioEngine.stopMusic();
        }
    }

    private updateShoot(dt: number) {
        if (this.targetFish != null && this.targetFish.isDie) {
            this.targetFish = null;
            if (this.isTargetFish) {
                this.toggleAuto.isChecked = false;
                this.isShoot = false;
            }
        }

        if (this.toggleAuto.isChecked) {
            if (this.targetFish != null) {
                var gunWorldPos = this.mePlayer.gunContainer.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                var fishWorldPos = this.targetFish.node.getComponent(UITransform).convertToWorldSpaceAR(v3(this.targetFish.node.getComponent(UITransform).width / 2, 0));
                var distance = Utils.v2Distance(v2(fishWorldPos.x, fishWorldPos.y), v2(gunWorldPos.x, gunWorldPos.y));
                var dAngle = fishWorldPos.subtract(gunWorldPos);

                if (this.isTargetFish) {
                    if (Math.abs(this.targetFish.node.x) > 960 || Math.abs(this.targetFish.node.y) > 540) {
                        this.isShoot = false;
                    } else {
                        this.isShoot = true;
                        this.mePlayer.gunContainer.angle = Math.atan2(dAngle.y, dAngle.x) * Utils.Rad2Deg;
                    }
                } else if (Math.abs(this.targetFish.node.x) > 1920 * 0.8 || Math.abs(this.targetFish.node.y) > 1080 * 0.8 || distance < 200) {
                    this.targetFish = null;
                    this.curIntervalFindTargetFish = 0;
                } else {
                    this.mePlayer.gunContainer.angle = Math.atan2(dAngle.y, dAngle.x) * Utils.Rad2Deg;
                    if (this.mePlayer.gunContainer.angle < 0 || this.mePlayer.gunContainer.angle > 180) {
                        this.targetFish = null;
                        this.curIntervalFindTargetFish = 0;
                    }
                }
            } else if (!this.isTargetFish) {
                this.curIntervalFindTargetFish = Math.max(0, this.curIntervalFindTargetFish - dt);
                if (this.curIntervalFindTargetFish == 0) {
                    this.findFishInWorld();
                }
            }
        }

        if (this.isShoot && this.shootCooldown <= 0) {
            this.shootCooldown = this.shootInterval;

            var velocity = Utils.degreesToVec2(this.mePlayer.gunContainer.angle);
            this.shootingId = this.mePlayer.serverPos + "" + (new Date()).getTime() % 10000000;

            var data = {
                "id": this.shootingId,
                "gi": this.mePlayer.gunId,
                "p": this.mePlayer.serverPos,
                "vx": velocity.x,
                "vy": velocity.y,
                "t": (new Date()).toISOString(),
                "iu": 0
            }

            var params = [];
            params.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.PlayerShooting);
            params.push(LongVuongPhotonClient.EParameterCodes.ShootingMessage, JSON.stringify(data));

            LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
        }
    }

    private findFishInWorld() {
        this.curIntervalFindTargetFish = this.intervalFindTargetFish;

        let listFishActiveInWorld = [];

        var gunWorldPos = this.mePlayer.gunContainer.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
        for (let i = 0; i < this.listFish.length; i++) {
            var fishNode = this.listFish[i].node;
            if (fishNode == null) continue;
            if (fishNode.active && Math.abs(fishNode.position.x) <= 1920 * 0.8 && Math.abs(fishNode.position.y) <= 1080 * 0.8) {
                var fishWorldPos = fishNode.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                var distance = Utils.v2Distance(v2(gunWorldPos.x, gunWorldPos.y), v2(fishWorldPos.x, fishWorldPos.y));
                if (distance >= 200) {
                    listFishActiveInWorld.push({
                        fish: this.listFish[i],
                        distance: distance
                    });
                }
            }
        }
        if (listFishActiveInWorld.length > 0) {
            this.targetFish = listFishActiveInWorld[Utils.randomRangeInt(0, listFishActiveInWorld.length)]["fish"];
            this.isTargetFish = false;
        }
    }

    private stopAutoShoot() {
        this.isShoot = false;
        this.toggleAuto.isChecked = false;
        this.touchPad.active = true;
        this.curIntervalFindTargetFish = 0;
        this.targetFish = null;
    }

    private getBullet(pos: number, gunId: number): LongVuongBullet {
        let bullet: LongVuongBullet;
        let node = instantiate(this.bulletTemplate);
        node.parent = this.bulletTemplate.parent;
        var source = node.getChildByName(gunId + "").getComponent(PolygonCollider2D);
        const target = node.getComponent(PolygonCollider2D);
        target.points = source.points.map(p => v2(p.x, p.y));
        bullet = node.getComponent(LongVuongBullet);
        bullet.positionId = pos;
        bullet.gunId = gunId;
        this.bullets.push(bullet);
        bullet.node.active = false;
        bullet.updateBulletByGunId(gunId);

        return bullet;
    }

    private getFishById(id: number): LongVuongFish {
        for (let i = 0; i < this.listFish.length; i++) {
            if (this.listFish[i].id == id) return this.listFish[i];
        }
        return null;
    }

    public getPlayerById(id: string): LongVuongPlayer {
        if (id <= "") return null;
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].id != "" && this.players[i].id == id) return this.players[i];
        }
        return null;
    }

    private getPlayerByPos(pos: number): LongVuongPlayer {
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].serverPos == pos) return this.players[i];
        }
        return null
    }

    public getFishAnimByType(type: number): Node {
        for (var node of this.containerFishTemplate.children) {
            if (node.name && node.name === `fish${type}`) {
                return node;
            }
        }
        return this.containerFishTemplate.children[0];
    }

    public actBetUp() {
        const gcIndex = this.lobby.listGunConfig.findIndex(config => config.i === this.mePlayer.gunId);
        if (gcIndex === -1) return;

        const isMax = gcIndex === this.lobby.listGunConfig.length - 1;
        const newIndex = isMax ? 0 : gcIndex + 1;

        if (gcIndex !== newIndex) {
            this.actUpdateGun(this.lobby.listGunConfig[newIndex].i);
        }
    }

    public actBetDown() {
        const gcIndex = this.lobby.listGunConfig.findIndex(config => config.i === this.mePlayer.gunId);
        if (gcIndex === -1) return;

        const isMin = gcIndex === 0;
        const newIndex = isMin ? this.lobby.listGunConfig.length - 1 : gcIndex - 1;

        if (gcIndex !== newIndex) {
            this.actUpdateGun(this.lobby.listGunConfig[newIndex].i);
        }
    }

    public updateJackpotObject(jackpot: any, percent: any) {
        if (typeof jackpot === "number" && !isNaN(jackpot)) {
            this.currentJackpot = jackpot;
        }

        if (typeof percent === "number" && !isNaN(percent)) {
            this.currentJackpotPercent = percent;
        }

        // Tween.numberTo(this.lblJackpot, this.currentJackpot, 0.3);
        this.lblPercentJackpot.string = this.currentJackpotPercent * 100 + "%";
        var jackpotValue = Math.floor(this.currentJackpot * this.currentJackpotPercent);
        // Tween.numberTo(this.lblJackpotPerGun, jackpotValue, 0.3);
    }

    private actUpdateGun(gunId: number) {
        var data = {
            i: gunId
        }

        var params = [];
        params.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.ChangeGun);
        params.push(LongVuongPhotonClient.EParameterCodes.ChangeGunMessage, JSON.stringify(data));

        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
    }

    public actBack() {
        this.lobby.actShowConfirm(App.instance.getTextLang("fish_pu6"), (isConfirm) => {
            if (isConfirm) {
                // audioEngine.stopAll();
                this.back();
                this.lobby.playMusic();
            }
        });
    }

    actUseTarget() {
        if (this.countItemTarget <= 0 || !this.allowUseItemTarget) {
            return;
        }
        this.allowUseItemTarget = false;
        var data = [];
        data.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.UseTarget);
        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, data, true);
    }

    private back() {
        this.stopAutoShoot();

        var params = [];
        params.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.LeaveRoom);
        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);

        this.scheduleOnce(() => {
            LongVuongSyncTimeControl.instance.resetPingOffline();
            this.node.active = false;
            this.lobby.show(true);
        }, 5);
    }

    showToast(message: string) {
        this.toast.node.active = true;
        this.toast.string = message;
        this.scheduleOnce(() => {
            this.toast.node.active = false;
        }, 2);
    }

    togglePanel(target: any) {
        this.panelMenu.x = this.panelMenu.x === 0 ? 120 : 0;
        target.target.parent.scale = target.target.parent.scale * -1;
    }

    hidePanel() {
        if (this.panelMenu.x === 0) {
            this.panelMenu.x = 120;
            this.panelMenu.getChildByName("MenuArrow").setScale(-1, -1)
        }
    }

    actX2() {
        if (!this.enableX2) return;

        var params = [];
        params.push(LongVuongPhotonClient.EParameterCodes.OperationSubCode, LongVuongPhotonClient.EOperationSubCodes.GetDoubleCanPlay);
        LongVuongPhotonClient.getInstance().sendOperation(PhotonClient.EOperationCodes.Game, params, true);
    }

    hideX2() {
        this.containerX2.active = false;
    }

    actShowPopupSetting() {
        this.popupContainer.removeAllChildren();
        let popup = instantiate(this.popupSettingPrefab);
        this.popupContainer.addChild(popup);
        // @ts-ignore
        popup.getComponent("LongVuong.PopupSetting").showDialog();
    }

    actShowPopupGuide() {
        this.popupContainer.removeAllChildren();
        let popup = instantiate(this.popupGuidePrefab);
        this.popupContainer.addChild(popup);
        // @ts-ignore
        popup.getComponent("LongVuong.PopupGuide").show();
    }

    toggleChat() {
        this.chatNode.active = !this.chatNode.active;
    }
}

var forge=(()=>{var A=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var x=A((wt,Z)=>{Z.exports={options:{usePureJavaScript:!1}}});var Y=A((St,Q)=>{var M={};Q.exports=M;var X={};M.encode=function(t,e,r){if(typeof e!="string")throw new TypeError('"alphabet" must be a string.');if(r!==void 0&&typeof r!="number")throw new TypeError('"maxline" must be a number.');var i="";if(!(t instanceof Uint8Array))i=lt(t,e);else{var a=0,h=e.length,s=e.charAt(0),o=[0];for(a=0;a<t.length;++a){for(var f=0,u=t[a];f<o.length;++f)u+=o[f]<<8,o[f]=u%h,u=u/h|0;for(;u>0;)o.push(u%h),u=u/h|0}for(a=0;t[a]===0&&a<t.length-1;++a)i+=s;for(a=o.length-1;a>=0;--a)i+=e[o[a]]}if(r){var c=new RegExp(".{1,"+r+"}","g");i=i.match(c).join(`\r
`)}return i};M.decode=function(t,e){if(typeof t!="string")throw new TypeError('"input" must be a string.');if(typeof e!="string")throw new TypeError('"alphabet" must be a string.');var r=X[e];if(!r){r=X[e]=[];for(var i=0;i<e.length;++i)r[e.charCodeAt(i)]=i}t=t.replace(/\s/g,"");for(var a=e.length,h=e.charAt(0),s=[0],i=0;i<t.length;i++){var o=r[t.charCodeAt(i)];if(o===void 0)return;for(var f=0,u=o;f<s.length;++f)u+=s[f]*a,s[f]=u&255,u>>=8;for(;u>0;)s.push(u&255),u>>=8}for(var c=0;t[c]===h&&c<t.length-1;++c)s.push(0);return typeof Buffer<"u"?Buffer.from(s.reverse()):new Uint8Array(s.reverse())};function lt(t,e){var r=0,i=e.length,a=e.charAt(0),h=[0];for(r=0;r<t.length();++r){for(var s=0,o=t.at(r);s<h.length;++s)o+=h[s]<<8,h[s]=o%i,o=o/i|0;for(;o>0;)h.push(o%i),o=o/i|0}var f="";for(r=0;t.at(r)===0&&r<t.length()-1;++r)f+=a;for(r=h.length-1;r>=0;--r)f+=e[h[r]];return f}});var O=A((bt,rt)=>{var $=x(),tt=Y(),n=rt.exports=$.util=$.util||{};(function(){if(typeof process<"u"&&process.nextTick&&!process.browser){n.nextTick=process.nextTick,typeof setImmediate=="function"?n.setImmediate=setImmediate:n.setImmediate=n.nextTick;return}if(typeof setImmediate=="function"){n.setImmediate=function(){return setImmediate.apply(void 0,arguments)},n.nextTick=function(o){return setImmediate(o)};return}if(n.setImmediate=function(o){setTimeout(o,0)},typeof window<"u"&&typeof window.postMessage=="function"){let o=function(f){if(f.source===window&&f.data===t){f.stopPropagation();var u=e.slice();e.length=0,u.forEach(function(c){c()})}};var s=o,t="forge.setImmediate",e=[];n.setImmediate=function(f){e.push(f),e.length===1&&window.postMessage(t,"*")},window.addEventListener("message",o,!0)}if(typeof MutationObserver<"u"){var r=Date.now(),i=!0,a=document.createElement("div"),e=[];new MutationObserver(function(){var f=e.slice();e.length=0,f.forEach(function(u){u()})}).observe(a,{attributes:!0});var h=n.setImmediate;n.setImmediate=function(f){Date.now()-r>15?(r=Date.now(),h(f)):(e.push(f),e.length===1&&a.setAttribute("a",i=!i))}}n.nextTick=n.setImmediate})();n.isNodejs=typeof process<"u"&&process.versions&&process.versions.node;n.globalScope=function(){return n.isNodejs?global:typeof self>"u"?window:self}();n.isArray=Array.isArray||function(t){return Object.prototype.toString.call(t)==="[object Array]"};n.isArrayBuffer=function(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer};n.isArrayBufferView=function(t){return t&&n.isArrayBuffer(t.buffer)&&t.byteLength!==void 0};function L(t){if(!(t===8||t===16||t===24||t===32))throw new Error("Only 8, 16, 24, or 32 bits supported: "+t)}n.ByteBuffer=V;function V(t){if(this.data="",this.read=0,typeof t=="string")this.data=t;else if(n.isArrayBuffer(t)||n.isArrayBufferView(t))if(typeof Buffer<"u"&&t instanceof Buffer)this.data=t.toString("binary");else{var e=new Uint8Array(t);try{this.data=String.fromCharCode.apply(null,e)}catch{for(var r=0;r<e.length;++r)this.putByte(e[r])}}else(t instanceof V||typeof t=="object"&&typeof t.data=="string"&&typeof t.read=="number")&&(this.data=t.data,this.read=t.read);this._constructedStringLength=0}n.ByteStringBuffer=V;var dt=4096;n.ByteStringBuffer.prototype._optimizeConstructedString=function(t){this._constructedStringLength+=t,this._constructedStringLength>dt&&(this.data.substr(0,1),this._constructedStringLength=0)};n.ByteStringBuffer.prototype.length=function(){return this.data.length-this.read};n.ByteStringBuffer.prototype.isEmpty=function(){return this.length()<=0};n.ByteStringBuffer.prototype.putByte=function(t){return this.putBytes(String.fromCharCode(t))};n.ByteStringBuffer.prototype.fillWithByte=function(t,e){t=String.fromCharCode(t);for(var r=this.data;e>0;)e&1&&(r+=t),e>>>=1,e>0&&(t+=t);return this.data=r,this._optimizeConstructedString(e),this};n.ByteStringBuffer.prototype.putBytes=function(t){return this.data+=t,this._optimizeConstructedString(t.length),this};n.ByteStringBuffer.prototype.putString=function(t){return this.putBytes(n.encodeUtf8(t))};n.ByteStringBuffer.prototype.putInt16=function(t){return this.putBytes(String.fromCharCode(t>>8&255)+String.fromCharCode(t&255))};n.ByteStringBuffer.prototype.putInt24=function(t){return this.putBytes(String.fromCharCode(t>>16&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(t&255))};n.ByteStringBuffer.prototype.putInt32=function(t){return this.putBytes(String.fromCharCode(t>>24&255)+String.fromCharCode(t>>16&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(t&255))};n.ByteStringBuffer.prototype.putInt16Le=function(t){return this.putBytes(String.fromCharCode(t&255)+String.fromCharCode(t>>8&255))};n.ByteStringBuffer.prototype.putInt24Le=function(t){return this.putBytes(String.fromCharCode(t&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(t>>16&255))};n.ByteStringBuffer.prototype.putInt32Le=function(t){return this.putBytes(String.fromCharCode(t&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(t>>16&255)+String.fromCharCode(t>>24&255))};n.ByteStringBuffer.prototype.putInt=function(t,e){L(e);var r="";do e-=8,r+=String.fromCharCode(t>>e&255);while(e>0);return this.putBytes(r)};n.ByteStringBuffer.prototype.putSignedInt=function(t,e){return t<0&&(t+=2<<e-1),this.putInt(t,e)};n.ByteStringBuffer.prototype.putBuffer=function(t){return this.putBytes(t.getBytes())};n.ByteStringBuffer.prototype.getByte=function(){return this.data.charCodeAt(this.read++)};n.ByteStringBuffer.prototype.getInt16=function(){var t=this.data.charCodeAt(this.read)<<8^this.data.charCodeAt(this.read+1);return this.read+=2,t};n.ByteStringBuffer.prototype.getInt24=function(){var t=this.data.charCodeAt(this.read)<<16^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2);return this.read+=3,t};n.ByteStringBuffer.prototype.getInt32=function(){var t=this.data.charCodeAt(this.read)<<24^this.data.charCodeAt(this.read+1)<<16^this.data.charCodeAt(this.read+2)<<8^this.data.charCodeAt(this.read+3);return this.read+=4,t};n.ByteStringBuffer.prototype.getInt16Le=function(){var t=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8;return this.read+=2,t};n.ByteStringBuffer.prototype.getInt24Le=function(){var t=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16;return this.read+=3,t};n.ByteStringBuffer.prototype.getInt32Le=function(){var t=this.data.charCodeAt(this.read)^this.data.charCodeAt(this.read+1)<<8^this.data.charCodeAt(this.read+2)<<16^this.data.charCodeAt(this.read+3)<<24;return this.read+=4,t};n.ByteStringBuffer.prototype.getInt=function(t){L(t);var e=0;do e=(e<<8)+this.data.charCodeAt(this.read++),t-=8;while(t>0);return e};n.ByteStringBuffer.prototype.getSignedInt=function(t){var e=this.getInt(t),r=2<<t-2;return e>=r&&(e-=r<<1),e};n.ByteStringBuffer.prototype.getBytes=function(t){var e;return t?(t=Math.min(this.length(),t),e=this.data.slice(this.read,this.read+t),this.read+=t):t===0?e="":(e=this.read===0?this.data:this.data.slice(this.read),this.clear()),e};n.ByteStringBuffer.prototype.bytes=function(t){return typeof t>"u"?this.data.slice(this.read):this.data.slice(this.read,this.read+t)};n.ByteStringBuffer.prototype.at=function(t){return this.data.charCodeAt(this.read+t)};n.ByteStringBuffer.prototype.setAt=function(t,e){return this.data=this.data.substr(0,this.read+t)+String.fromCharCode(e)+this.data.substr(this.read+t+1),this};n.ByteStringBuffer.prototype.last=function(){return this.data.charCodeAt(this.data.length-1)};n.ByteStringBuffer.prototype.copy=function(){var t=n.createBuffer(this.data);return t.read=this.read,t};n.ByteStringBuffer.prototype.compact=function(){return this.read>0&&(this.data=this.data.slice(this.read),this.read=0),this};n.ByteStringBuffer.prototype.clear=function(){return this.data="",this.read=0,this};n.ByteStringBuffer.prototype.truncate=function(t){var e=Math.max(0,this.length()-t);return this.data=this.data.substr(this.read,e),this.read=0,this};n.ByteStringBuffer.prototype.toHex=function(){for(var t="",e=this.read;e<this.data.length;++e){var r=this.data.charCodeAt(e);r<16&&(t+="0"),t+=r.toString(16)}return t};n.ByteStringBuffer.prototype.toString=function(){return n.decodeUtf8(this.bytes())};function pt(t,e){e=e||{},this.read=e.readOffset||0,this.growSize=e.growSize||1024;var r=n.isArrayBuffer(t),i=n.isArrayBufferView(t);if(r||i){r?this.data=new DataView(t):this.data=new DataView(t.buffer,t.byteOffset,t.byteLength),this.write="writeOffset"in e?e.writeOffset:this.data.byteLength;return}this.data=new DataView(new ArrayBuffer(0)),this.write=0,t!=null&&this.putBytes(t),"writeOffset"in e&&(this.write=e.writeOffset)}n.DataBuffer=pt;n.DataBuffer.prototype.length=function(){return this.write-this.read};n.DataBuffer.prototype.isEmpty=function(){return this.length()<=0};n.DataBuffer.prototype.accommodate=function(t,e){if(this.length()>=t)return this;e=Math.max(e||this.growSize,t);var r=new Uint8Array(this.data.buffer,this.data.byteOffset,this.data.byteLength),i=new Uint8Array(this.length()+e);return i.set(r),this.data=new DataView(i.buffer),this};n.DataBuffer.prototype.putByte=function(t){return this.accommodate(1),this.data.setUint8(this.write++,t),this};n.DataBuffer.prototype.fillWithByte=function(t,e){this.accommodate(e);for(var r=0;r<e;++r)this.data.setUint8(t);return this};n.DataBuffer.prototype.putBytes=function(t,e){if(n.isArrayBufferView(t)){var r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength),i=r.byteLength-r.byteOffset;this.accommodate(i);var a=new Uint8Array(this.data.buffer,this.write);return a.set(r),this.write+=i,this}if(n.isArrayBuffer(t)){var r=new Uint8Array(t);this.accommodate(r.byteLength);var a=new Uint8Array(this.data.buffer);return a.set(r,this.write),this.write+=r.byteLength,this}if(t instanceof n.DataBuffer||typeof t=="object"&&typeof t.read=="number"&&typeof t.write=="number"&&n.isArrayBufferView(t.data)){var r=new Uint8Array(t.data.byteLength,t.read,t.length());this.accommodate(r.byteLength);var a=new Uint8Array(t.data.byteLength,this.write);return a.set(r),this.write+=r.byteLength,this}if(t instanceof n.ByteStringBuffer&&(t=t.data,e="binary"),e=e||"binary",typeof t=="string"){var h;if(e==="hex")return this.accommodate(Math.ceil(t.length/2)),h=new Uint8Array(this.data.buffer,this.write),this.write+=n.binary.hex.decode(t,h,this.write),this;if(e==="base64")return this.accommodate(Math.ceil(t.length/4)*3),h=new Uint8Array(this.data.buffer,this.write),this.write+=n.binary.base64.decode(t,h,this.write),this;if(e==="utf8"&&(t=n.encodeUtf8(t),e="binary"),e==="binary"||e==="raw")return this.accommodate(t.length),h=new Uint8Array(this.data.buffer,this.write),this.write+=n.binary.raw.decode(h),this;if(e==="utf16")return this.accommodate(t.length*2),h=new Uint16Array(this.data.buffer,this.write),this.write+=n.text.utf16.encode(h),this;throw new Error("Invalid encoding: "+e)}throw Error("Invalid parameter: "+t)};n.DataBuffer.prototype.putBuffer=function(t){return this.putBytes(t),t.clear(),this};n.DataBuffer.prototype.putString=function(t){return this.putBytes(t,"utf16")};n.DataBuffer.prototype.putInt16=function(t){return this.accommodate(2),this.data.setInt16(this.write,t),this.write+=2,this};n.DataBuffer.prototype.putInt24=function(t){return this.accommodate(3),this.data.setInt16(this.write,t>>8&65535),this.data.setInt8(this.write,t>>16&255),this.write+=3,this};n.DataBuffer.prototype.putInt32=function(t){return this.accommodate(4),this.data.setInt32(this.write,t),this.write+=4,this};n.DataBuffer.prototype.putInt16Le=function(t){return this.accommodate(2),this.data.setInt16(this.write,t,!0),this.write+=2,this};n.DataBuffer.prototype.putInt24Le=function(t){return this.accommodate(3),this.data.setInt8(this.write,t>>16&255),this.data.setInt16(this.write,t>>8&65535,!0),this.write+=3,this};n.DataBuffer.prototype.putInt32Le=function(t){return this.accommodate(4),this.data.setInt32(this.write,t,!0),this.write+=4,this};n.DataBuffer.prototype.putInt=function(t,e){L(e),this.accommodate(e/8);do e-=8,this.data.setInt8(this.write++,t>>e&255);while(e>0);return this};n.DataBuffer.prototype.putSignedInt=function(t,e){return L(e),this.accommodate(e/8),t<0&&(t+=2<<e-1),this.putInt(t,e)};n.DataBuffer.prototype.getByte=function(){return this.data.getInt8(this.read++)};n.DataBuffer.prototype.getInt16=function(){var t=this.data.getInt16(this.read);return this.read+=2,t};n.DataBuffer.prototype.getInt24=function(){var t=this.data.getInt16(this.read)<<8^this.data.getInt8(this.read+2);return this.read+=3,t};n.DataBuffer.prototype.getInt32=function(){var t=this.data.getInt32(this.read);return this.read+=4,t};n.DataBuffer.prototype.getInt16Le=function(){var t=this.data.getInt16(this.read,!0);return this.read+=2,t};n.DataBuffer.prototype.getInt24Le=function(){var t=this.data.getInt8(this.read)^this.data.getInt16(this.read+1,!0)<<8;return this.read+=3,t};n.DataBuffer.prototype.getInt32Le=function(){var t=this.data.getInt32(this.read,!0);return this.read+=4,t};n.DataBuffer.prototype.getInt=function(t){L(t);var e=0;do e=(e<<8)+this.data.getInt8(this.read++),t-=8;while(t>0);return e};n.DataBuffer.prototype.getSignedInt=function(t){var e=this.getInt(t),r=2<<t-2;return e>=r&&(e-=r<<1),e};n.DataBuffer.prototype.getBytes=function(t){var e;return t?(t=Math.min(this.length(),t),e=this.data.slice(this.read,this.read+t),this.read+=t):t===0?e="":(e=this.read===0?this.data:this.data.slice(this.read),this.clear()),e};n.DataBuffer.prototype.bytes=function(t){return typeof t>"u"?this.data.slice(this.read):this.data.slice(this.read,this.read+t)};n.DataBuffer.prototype.at=function(t){return this.data.getUint8(this.read+t)};n.DataBuffer.prototype.setAt=function(t,e){return this.data.setUint8(t,e),this};n.DataBuffer.prototype.last=function(){return this.data.getUint8(this.write-1)};n.DataBuffer.prototype.copy=function(){return new n.DataBuffer(this)};n.DataBuffer.prototype.compact=function(){if(this.read>0){var t=new Uint8Array(this.data.buffer,this.read),e=new Uint8Array(t.byteLength);e.set(t),this.data=new DataView(e),this.write-=this.read,this.read=0}return this};n.DataBuffer.prototype.clear=function(){return this.data=new DataView(new ArrayBuffer(0)),this.read=this.write=0,this};n.DataBuffer.prototype.truncate=function(t){return this.write=Math.max(0,this.length()-t),this.read=Math.min(this.read,this.write),this};n.DataBuffer.prototype.toHex=function(){for(var t="",e=this.read;e<this.data.byteLength;++e){var r=this.data.getUint8(e);r<16&&(t+="0"),t+=r.toString(16)}return t};n.DataBuffer.prototype.toString=function(t){var e=new Uint8Array(this.data,this.read,this.length());if(t=t||"utf8",t==="binary"||t==="raw")return n.binary.raw.encode(e);if(t==="hex")return n.binary.hex.encode(e);if(t==="base64")return n.binary.base64.encode(e);if(t==="utf8")return n.text.utf8.decode(e);if(t==="utf16")return n.text.utf16.decode(e);throw new Error("Invalid encoding: "+t)};n.createBuffer=function(t,e){return e=e||"raw",t!==void 0&&e==="utf8"&&(t=n.encodeUtf8(t)),new n.ByteBuffer(t)};n.fillString=function(t,e){for(var r="";e>0;)e&1&&(r+=t),e>>>=1,e>0&&(t+=t);return r};n.xorBytes=function(t,e,r){for(var i="",a="",h="",s=0,o=0;r>0;--r,++s)a=t.charCodeAt(s)^e.charCodeAt(s),o>=10&&(i+=h,h="",o=0),h+=String.fromCharCode(a),++o;return i+=h,i};n.hexToBytes=function(t){var e="",r=0;for(t.length&!0&&(r=1,e+=String.fromCharCode(parseInt(t[0],16)));r<t.length;r+=2)e+=String.fromCharCode(parseInt(t.substr(r,2),16));return e};n.bytesToHex=function(t){return n.createBuffer(t).toHex()};n.int32ToBytes=function(t){return String.fromCharCode(t>>24&255)+String.fromCharCode(t>>16&255)+String.fromCharCode(t>>8&255)+String.fromCharCode(t&255)};var k="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",I=[62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,64,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],et="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";n.encode64=function(t,e){for(var r="",i="",a,h,s,o=0;o<t.length;)a=t.charCodeAt(o++),h=t.charCodeAt(o++),s=t.charCodeAt(o++),r+=k.charAt(a>>2),r+=k.charAt((a&3)<<4|h>>4),isNaN(h)?r+="==":(r+=k.charAt((h&15)<<2|s>>6),r+=isNaN(s)?"=":k.charAt(s&63)),e&&r.length>e&&(i+=r.substr(0,e)+`\r
`,r=r.substr(e));return i+=r,i};n.decode64=function(t){t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");for(var e="",r,i,a,h,s=0;s<t.length;)r=I[t.charCodeAt(s++)-43],i=I[t.charCodeAt(s++)-43],a=I[t.charCodeAt(s++)-43],h=I[t.charCodeAt(s++)-43],e+=String.fromCharCode(r<<2|i>>4),a!==64&&(e+=String.fromCharCode((i&15)<<4|a>>2),h!==64&&(e+=String.fromCharCode((a&3)<<6|h)));return e};n.encodeUtf8=function(t){return unescape(encodeURIComponent(t))};n.decodeUtf8=function(t){return decodeURIComponent(escape(t))};n.binary={raw:{},hex:{},base64:{},base58:{},baseN:{encode:tt.encode,decode:tt.decode}};n.binary.raw.encode=function(t){return String.fromCharCode.apply(null,t)};n.binary.raw.decode=function(t,e,r){var i=e;i||(i=new Uint8Array(t.length)),r=r||0;for(var a=r,h=0;h<t.length;++h)i[a++]=t.charCodeAt(h);return e?a-r:i};n.binary.hex.encode=n.bytesToHex;n.binary.hex.decode=function(t,e,r){var i=e;i||(i=new Uint8Array(Math.ceil(t.length/2))),r=r||0;var a=0,h=r;for(t.length&1&&(a=1,i[h++]=parseInt(t[0],16));a<t.length;a+=2)i[h++]=parseInt(t.substr(a,2),16);return e?h-r:i};n.binary.base64.encode=function(t,e){for(var r="",i="",a,h,s,o=0;o<t.byteLength;)a=t[o++],h=t[o++],s=t[o++],r+=k.charAt(a>>2),r+=k.charAt((a&3)<<4|h>>4),isNaN(h)?r+="==":(r+=k.charAt((h&15)<<2|s>>6),r+=isNaN(s)?"=":k.charAt(s&63)),e&&r.length>e&&(i+=r.substr(0,e)+`\r
`,r=r.substr(e));return i+=r,i};n.binary.base64.decode=function(t,e,r){var i=e;i||(i=new Uint8Array(Math.ceil(t.length/4)*3)),t=t.replace(/[^A-Za-z0-9\+\/\=]/g,""),r=r||0;for(var a,h,s,o,f=0,u=r;f<t.length;)a=I[t.charCodeAt(f++)-43],h=I[t.charCodeAt(f++)-43],s=I[t.charCodeAt(f++)-43],o=I[t.charCodeAt(f++)-43],i[u++]=a<<2|h>>4,s!==64&&(i[u++]=(h&15)<<4|s>>2,o!==64&&(i[u++]=(s&3)<<6|o));return e?u-r:i.subarray(0,u)};n.binary.base58.encode=function(t,e){return n.binary.baseN.encode(t,et,e)};n.binary.base58.decode=function(t,e){return n.binary.baseN.decode(t,et,e)};n.text={utf8:{},utf16:{}};n.text.utf8.encode=function(t,e,r){t=n.encodeUtf8(t);var i=e;i||(i=new Uint8Array(t.length)),r=r||0;for(var a=r,h=0;h<t.length;++h)i[a++]=t.charCodeAt(h);return e?a-r:i};n.text.utf8.decode=function(t){return n.decodeUtf8(String.fromCharCode.apply(null,t))};n.text.utf16.encode=function(t,e,r){var i=e;i||(i=new Uint8Array(t.length*2));var a=new Uint16Array(i.buffer);r=r||0;for(var h=r,s=r,o=0;o<t.length;++o)a[s++]=t.charCodeAt(o),h+=2;return e?h-r:i};n.text.utf16.decode=function(t){return String.fromCharCode.apply(null,new Uint16Array(t.buffer))};n.deflate=function(t,e,r){if(e=n.decode64(t.deflate(n.encode64(e)).rval),r){var i=2,a=e.charCodeAt(1);a&32&&(i=6),e=e.substring(i,e.length-4)}return e};n.inflate=function(t,e,r){var i=t.inflate(n.encode64(e)).rval;return i===null?null:n.decode64(i)};var q=function(t,e,r){if(!t)throw new Error("WebStorage not available.");var i;if(r===null?i=t.removeItem(e):(r=n.encode64(JSON.stringify(r)),i=t.setItem(e,r)),typeof i<"u"&&i.rval!==!0){var a=new Error(i.error.message);throw a.id=i.error.id,a.name=i.error.name,a}},P=function(t,e){if(!t)throw new Error("WebStorage not available.");var r=t.getItem(e);if(t.init)if(r.rval===null){if(r.error){var i=new Error(r.error.message);throw i.id=r.error.id,i.name=r.error.name,i}r=null}else r=r.rval;return r!==null&&(r=JSON.parse(n.decode64(r))),r},gt=function(t,e,r,i){var a=P(t,e);a===null&&(a={}),a[r]=i,q(t,e,a)},yt=function(t,e,r){var i=P(t,e);return i!==null&&(i=r in i?i[r]:null),i},vt=function(t,e,r){var i=P(t,e);if(i!==null&&r in i){delete i[r];var a=!0;for(var h in i){a=!1;break}a&&(i=null),q(t,e,i)}},Bt=function(t,e){q(t,e,null)},U=function(t,e,r){var i=null;typeof r>"u"&&(r=["web","flash"]);var a,h=!1,s=null;for(var o in r){a=r[o];try{if(a==="flash"||a==="both"){if(e[0]===null)throw new Error("Flash local storage not available.");i=t.apply(this,e),h=a==="flash"}(a==="web"||a==="both")&&(e[0]=localStorage,i=t.apply(this,e),h=!0)}catch(f){s=f}if(h)break}if(!h)throw s;return i};n.setItem=function(t,e,r,i,a){U(gt,arguments,a)};n.getItem=function(t,e,r,i){return U(yt,arguments,i)};n.removeItem=function(t,e,r,i){U(vt,arguments,i)};n.clearItems=function(t,e,r){U(Bt,arguments,r)};n.isEmpty=function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0};n.format=function(t){for(var e=/%./g,r,i,a=0,h=[],s=0;r=e.exec(t);){i=t.substring(s,e.lastIndex-2),i.length>0&&h.push(i),s=e.lastIndex;var o=r[0][1];switch(o){case"s":case"o":a<arguments.length?h.push(arguments[a+++1]):h.push("<?>");break;case"%":h.push("%");break;default:h.push("<%"+o+"?>")}}return h.push(t.substring(s)),h.join("")};n.formatNumber=function(t,e,r,i){var a=t,h=isNaN(e=Math.abs(e))?2:e,s=r===void 0?",":r,o=i===void 0?".":i,f=a<0?"-":"",u=parseInt(a=Math.abs(+a||0).toFixed(h),10)+"",c=u.length>3?u.length%3:0;return f+(c?u.substr(0,c)+o:"")+u.substr(c).replace(/(\d{3})(?=\d)/g,"$1"+o)+(h?s+Math.abs(a-u).toFixed(h).slice(2):"")};n.formatSize=function(t){return t>=1073741824?t=n.formatNumber(t/1073741824,2,".","")+" GiB":t>=1048576?t=n.formatNumber(t/1048576,2,".","")+" MiB":t>=1024?t=n.formatNumber(t/1024,0)+" KiB":t=n.formatNumber(t,0)+" bytes",t};n.bytesFromIP=function(t){return t.indexOf(".")!==-1?n.bytesFromIPv4(t):t.indexOf(":")!==-1?n.bytesFromIPv6(t):null};n.bytesFromIPv4=function(t){if(t=t.split("."),t.length!==4)return null;for(var e=n.createBuffer(),r=0;r<t.length;++r){var i=parseInt(t[r],10);if(isNaN(i))return null;e.putByte(i)}return e.getBytes()};n.bytesFromIPv6=function(t){var e=0;t=t.split(":").filter(function(s){return s.length===0&&++e,!0});for(var r=(8-t.length+e)*2,i=n.createBuffer(),a=0;a<8;++a){if(!t[a]||t[a].length===0){i.fillWithByte(0,r),r=0;continue}var h=n.hexToBytes(t[a]);h.length<2&&i.putByte(0),i.putBytes(h)}return i.getBytes()};n.bytesToIP=function(t){return t.length===4?n.bytesToIPv4(t):t.length===16?n.bytesToIPv6(t):null};n.bytesToIPv4=function(t){if(t.length!==4)return null;for(var e=[],r=0;r<t.length;++r)e.push(t.charCodeAt(r));return e.join(".")};n.bytesToIPv6=function(t){if(t.length!==16)return null;for(var e=[],r=[],i=0,a=0;a<t.length;a+=2){for(var h=n.bytesToHex(t[a]+t[a+1]);h[0]==="0"&&h!=="0";)h=h.substr(1);if(h==="0"){var s=r[r.length-1],o=e.length;!s||o!==s.end+1?r.push({start:o,end:o}):(s.end=o,s.end-s.start>r[i].end-r[i].start&&(i=r.length-1))}e.push(h)}if(r.length>0){var f=r[i];f.end-f.start>0&&(e.splice(f.start,f.end-f.start+1,""),f.start===0&&e.unshift(""),f.end===7&&e.push(""))}return e.join(":")};n.estimateCores=function(t,e){if(typeof t=="function"&&(e=t,t={}),t=t||{},"cores"in n&&!t.update)return e(null,n.cores);if(typeof navigator<"u"&&"hardwareConcurrency"in navigator&&navigator.hardwareConcurrency>0)return n.cores=navigator.hardwareConcurrency,e(null,n.cores);if(typeof Worker>"u")return n.cores=1,e(null,n.cores);if(typeof Blob>"u")return n.cores=2,e(null,n.cores);var r=URL.createObjectURL(new Blob(["(",function(){self.addEventListener("message",function(s){for(var o=Date.now(),f=o+4;Date.now()<f;);self.postMessage({st:o,et:f})})}.toString(),")()"],{type:"application/javascript"}));i([],5,16);function i(s,o,f){if(o===0){var u=Math.floor(s.reduce(function(c,d){return c+d},0)/s.length);return n.cores=Math.max(1,u),URL.revokeObjectURL(r),e(null,n.cores)}a(f,function(c,d){s.push(h(f,d)),i(s,o-1,f)})}function a(s,o){for(var f=[],u=[],c=0;c<s;++c){var d=new Worker(r);d.addEventListener("message",function(y){if(u.push(y.data),u.length===s){for(var g=0;g<s;++g)f[g].terminate();o(null,u)}}),f.push(d)}for(var c=0;c<s;++c)f[c].postMessage(c)}function h(s,o){for(var f=[],u=0;u<s;++u)for(var c=o[u],d=f[u]=[],y=0;y<s;++y)if(u!==y){var g=o[y];(c.st>g.st&&c.st<g.et||g.st>c.st&&g.st<c.et)&&d.push(y)}return f.reduce(function(m,w){return Math.max(m,w.length)},0)}}});var j=A((kt,it)=>{var v=x();O();it.exports=v.cipher=v.cipher||{};v.cipher.algorithms=v.cipher.algorithms||{};v.cipher.createCipher=function(t,e){var r=t;if(typeof r=="string"&&(r=v.cipher.getAlgorithm(r),r&&(r=r())),!r)throw new Error("Unsupported algorithm: "+t);return new v.cipher.BlockCipher({algorithm:r,key:e,decrypt:!1})};v.cipher.createDecipher=function(t,e){var r=t;if(typeof r=="string"&&(r=v.cipher.getAlgorithm(r),r&&(r=r())),!r)throw new Error("Unsupported algorithm: "+t);return new v.cipher.BlockCipher({algorithm:r,key:e,decrypt:!0})};v.cipher.registerAlgorithm=function(t,e){t=t.toUpperCase(),v.cipher.algorithms[t]=e};v.cipher.getAlgorithm=function(t){return t=t.toUpperCase(),t in v.cipher.algorithms?v.cipher.algorithms[t]:null};var R=v.cipher.BlockCipher=function(t){this.algorithm=t.algorithm,this.mode=this.algorithm.mode,this.blockSize=this.mode.blockSize,this._finish=!1,this._input=null,this.output=null,this._op=t.decrypt?this.mode.decrypt:this.mode.encrypt,this._decrypt=t.decrypt,this.algorithm.initialize(t)};R.prototype.start=function(t){t=t||{};var e={};for(var r in t)e[r]=t[r];e.decrypt=this._decrypt,this._finish=!1,this._input=v.util.createBuffer(),this.output=t.output||v.util.createBuffer(),this.mode.start(e)};R.prototype.update=function(t){for(t&&this._input.putBuffer(t);!this._op.call(this.mode,this._input,this.output,this._finish)&&!this._finish;);this._input.compact()};R.prototype.finish=function(t){t&&(this.mode.name==="ECB"||this.mode.name==="CBC")&&(this.mode.pad=function(r){return t(this.blockSize,r,!1)},this.mode.unpad=function(r){return t(this.blockSize,r,!0)});var e={};return e.decrypt=this._decrypt,e.overflow=this._input.length()%this.blockSize,!(!this._decrypt&&this.mode.pad&&!this.mode.pad(this._input,e)||(this._finish=!0,this.update(),this._decrypt&&this.mode.unpad&&!this.mode.unpad(this.output,e))||this.mode.afterFinish&&!this.mode.afterFinish(this.output,e))}});var nt=A((It,at)=>{var B=x();O();B.cipher=B.cipher||{};var l=at.exports=B.cipher.modes=B.cipher.modes||{};l.ecb=function(t){t=t||{},this.name="ECB",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)};l.ecb.prototype.start=function(t){};l.ecb.prototype.encrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var i=0;i<this._ints;++i)this._inBlock[i]=t.getInt32();this.cipher.encrypt(this._inBlock,this._outBlock);for(var i=0;i<this._ints;++i)e.putInt32(this._outBlock[i])};l.ecb.prototype.decrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var i=0;i<this._ints;++i)this._inBlock[i]=t.getInt32();this.cipher.decrypt(this._inBlock,this._outBlock);for(var i=0;i<this._ints;++i)e.putInt32(this._outBlock[i])};l.ecb.prototype.pad=function(t,e){var r=t.length()===this.blockSize?this.blockSize:this.blockSize-t.length();return t.fillWithByte(r,r),!0};l.ecb.prototype.unpad=function(t,e){if(e.overflow>0)return!1;var r=t.length(),i=t.at(r-1);return i>this.blockSize<<2?!1:(t.truncate(i),!0)};l.cbc=function(t){t=t||{},this.name="CBC",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints)};l.cbc.prototype.start=function(t){if(t.iv===null){if(!this._prev)throw new Error("Invalid IV parameter.");this._iv=this._prev.slice(0)}else if("iv"in t)this._iv=E(t.iv,this.blockSize),this._prev=this._iv.slice(0);else throw new Error("Invalid IV parameter.")};l.cbc.prototype.encrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var i=0;i<this._ints;++i)this._inBlock[i]=this._prev[i]^t.getInt32();this.cipher.encrypt(this._inBlock,this._outBlock);for(var i=0;i<this._ints;++i)e.putInt32(this._outBlock[i]);this._prev=this._outBlock};l.cbc.prototype.decrypt=function(t,e,r){if(t.length()<this.blockSize&&!(r&&t.length()>0))return!0;for(var i=0;i<this._ints;++i)this._inBlock[i]=t.getInt32();this.cipher.decrypt(this._inBlock,this._outBlock);for(var i=0;i<this._ints;++i)e.putInt32(this._prev[i]^this._outBlock[i]);this._prev=this._inBlock.slice(0)};l.cbc.prototype.pad=function(t,e){var r=t.length()===this.blockSize?this.blockSize:this.blockSize-t.length();return t.fillWithByte(r,r),!0};l.cbc.prototype.unpad=function(t,e){if(e.overflow>0)return!1;var r=t.length(),i=t.at(r-1);return i>this.blockSize<<2?!1:(t.truncate(i),!0)};l.cfb=function(t){t=t||{},this.name="CFB",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialBlock=new Array(this._ints),this._partialOutput=B.util.createBuffer(),this._partialBytes=0};l.cfb.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");this._iv=E(t.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0};l.cfb.prototype.encrypt=function(t,e,r){var i=t.length();if(i===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&i>=this.blockSize){for(var a=0;a<this._ints;++a)this._inBlock[a]=t.getInt32()^this._outBlock[a],e.putInt32(this._inBlock[a]);return}var h=(this.blockSize-i)%this.blockSize;h>0&&(h=this.blockSize-h),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialBlock[a]=t.getInt32()^this._outBlock[a],this._partialOutput.putInt32(this._partialBlock[a]);if(h>0)t.read-=this.blockSize;else for(var a=0;a<this._ints;++a)this._inBlock[a]=this._partialBlock[a];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),h>0&&!r)return e.putBytes(this._partialOutput.getBytes(h-this._partialBytes)),this._partialBytes=h,!0;e.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=0};l.cfb.prototype.decrypt=function(t,e,r){var i=t.length();if(i===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&i>=this.blockSize){for(var a=0;a<this._ints;++a)this._inBlock[a]=t.getInt32(),e.putInt32(this._inBlock[a]^this._outBlock[a]);return}var h=(this.blockSize-i)%this.blockSize;h>0&&(h=this.blockSize-h),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialBlock[a]=t.getInt32(),this._partialOutput.putInt32(this._partialBlock[a]^this._outBlock[a]);if(h>0)t.read-=this.blockSize;else for(var a=0;a<this._ints;++a)this._inBlock[a]=this._partialBlock[a];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),h>0&&!r)return e.putBytes(this._partialOutput.getBytes(h-this._partialBytes)),this._partialBytes=h,!0;e.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=0};l.ofb=function(t){t=t||{},this.name="OFB",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=B.util.createBuffer(),this._partialBytes=0};l.ofb.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");this._iv=E(t.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0};l.ofb.prototype.encrypt=function(t,e,r){var i=t.length();if(t.length()===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&i>=this.blockSize){for(var a=0;a<this._ints;++a)e.putInt32(t.getInt32()^this._outBlock[a]),this._inBlock[a]=this._outBlock[a];return}var h=(this.blockSize-i)%this.blockSize;h>0&&(h=this.blockSize-h),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialOutput.putInt32(t.getInt32()^this._outBlock[a]);if(h>0)t.read-=this.blockSize;else for(var a=0;a<this._ints;++a)this._inBlock[a]=this._outBlock[a];if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),h>0&&!r)return e.putBytes(this._partialOutput.getBytes(h-this._partialBytes)),this._partialBytes=h,!0;e.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=0};l.ofb.prototype.decrypt=l.ofb.prototype.encrypt;l.ctr=function(t){t=t||{},this.name="CTR",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=null,this._outBlock=new Array(this._ints),this._partialOutput=B.util.createBuffer(),this._partialBytes=0};l.ctr.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");this._iv=E(t.iv,this.blockSize),this._inBlock=this._iv.slice(0),this._partialBytes=0};l.ctr.prototype.encrypt=function(t,e,r){var i=t.length();if(i===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&i>=this.blockSize)for(var a=0;a<this._ints;++a)e.putInt32(t.getInt32()^this._outBlock[a]);else{var h=(this.blockSize-i)%this.blockSize;h>0&&(h=this.blockSize-h),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialOutput.putInt32(t.getInt32()^this._outBlock[a]);if(h>0&&(t.read-=this.blockSize),this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),h>0&&!r)return e.putBytes(this._partialOutput.getBytes(h-this._partialBytes)),this._partialBytes=h,!0;e.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=0}T(this._inBlock)};l.ctr.prototype.decrypt=l.ctr.prototype.encrypt;l.gcm=function(t){t=t||{},this.name="GCM",this.cipher=t.cipher,this.blockSize=t.blockSize||16,this._ints=this.blockSize/4,this._inBlock=new Array(this._ints),this._outBlock=new Array(this._ints),this._partialOutput=B.util.createBuffer(),this._partialBytes=0,this._R=3774873600};l.gcm.prototype.start=function(t){if(!("iv"in t))throw new Error("Invalid IV parameter.");var e=B.util.createBuffer(t.iv);this._cipherLength=0;var r;if("additionalData"in t?r=B.util.createBuffer(t.additionalData):r=B.util.createBuffer(),"tagLength"in t?this._tagLength=t.tagLength:this._tagLength=128,this._tag=null,t.decrypt&&(this._tag=B.util.createBuffer(t.tag).getBytes(),this._tag.length!==this._tagLength/8))throw new Error("Authentication tag does not match tag length.");this._hashBlock=new Array(this._ints),this.tag=null,this._hashSubkey=new Array(this._ints),this.cipher.encrypt([0,0,0,0],this._hashSubkey),this.componentBits=4,this._m=this.generateHashTable(this._hashSubkey,this.componentBits);var i=e.length();if(i===12)this._j0=[e.getInt32(),e.getInt32(),e.getInt32(),1];else{for(this._j0=[0,0,0,0];e.length()>0;)this._j0=this.ghash(this._hashSubkey,this._j0,[e.getInt32(),e.getInt32(),e.getInt32(),e.getInt32()]);this._j0=this.ghash(this._hashSubkey,this._j0,[0,0].concat(H(i*8)))}this._inBlock=this._j0.slice(0),T(this._inBlock),this._partialBytes=0,r=B.util.createBuffer(r),this._aDataLength=H(r.length()*8);var a=r.length()%this.blockSize;for(a&&r.fillWithByte(0,this.blockSize-a),this._s=[0,0,0,0];r.length()>0;)this._s=this.ghash(this._hashSubkey,this._s,[r.getInt32(),r.getInt32(),r.getInt32(),r.getInt32()])};l.gcm.prototype.encrypt=function(t,e,r){var i=t.length();if(i===0)return!0;if(this.cipher.encrypt(this._inBlock,this._outBlock),this._partialBytes===0&&i>=this.blockSize){for(var a=0;a<this._ints;++a)e.putInt32(this._outBlock[a]^=t.getInt32());this._cipherLength+=this.blockSize}else{var h=(this.blockSize-i)%this.blockSize;h>0&&(h=this.blockSize-h),this._partialOutput.clear();for(var a=0;a<this._ints;++a)this._partialOutput.putInt32(t.getInt32()^this._outBlock[a]);if(h<=0||r){if(r){var s=i%this.blockSize;this._cipherLength+=s,this._partialOutput.truncate(this.blockSize-s)}else this._cipherLength+=this.blockSize;for(var a=0;a<this._ints;++a)this._outBlock[a]=this._partialOutput.getInt32();this._partialOutput.read-=this.blockSize}if(this._partialBytes>0&&this._partialOutput.getBytes(this._partialBytes),h>0&&!r)return t.read-=this.blockSize,e.putBytes(this._partialOutput.getBytes(h-this._partialBytes)),this._partialBytes=h,!0;e.putBytes(this._partialOutput.getBytes(i-this._partialBytes)),this._partialBytes=0}this._s=this.ghash(this._hashSubkey,this._s,this._outBlock),T(this._inBlock)};l.gcm.prototype.decrypt=function(t,e,r){var i=t.length();if(i<this.blockSize&&!(r&&i>0))return!0;this.cipher.encrypt(this._inBlock,this._outBlock),T(this._inBlock),this._hashBlock[0]=t.getInt32(),this._hashBlock[1]=t.getInt32(),this._hashBlock[2]=t.getInt32(),this._hashBlock[3]=t.getInt32(),this._s=this.ghash(this._hashSubkey,this._s,this._hashBlock);for(var a=0;a<this._ints;++a)e.putInt32(this._outBlock[a]^this._hashBlock[a]);i<this.blockSize?this._cipherLength+=i%this.blockSize:this._cipherLength+=this.blockSize};l.gcm.prototype.afterFinish=function(t,e){var r=!0;e.decrypt&&e.overflow&&t.truncate(this.blockSize-e.overflow),this.tag=B.util.createBuffer();var i=this._aDataLength.concat(H(this._cipherLength*8));this._s=this.ghash(this._hashSubkey,this._s,i);var a=[];this.cipher.encrypt(this._j0,a);for(var h=0;h<this._ints;++h)this.tag.putInt32(this._s[h]^a[h]);return this.tag.truncate(this.tag.length()%(this._tagLength/8)),e.decrypt&&this.tag.bytes()!==this._tag&&(r=!1),r};l.gcm.prototype.multiply=function(t,e){for(var r=[0,0,0,0],i=e.slice(0),a=0;a<128;++a){var h=t[a/32|0]&1<<31-a%32;h&&(r[0]^=i[0],r[1]^=i[1],r[2]^=i[2],r[3]^=i[3]),this.pow(i,i)}return r};l.gcm.prototype.pow=function(t,e){for(var r=t[3]&1,i=3;i>0;--i)e[i]=t[i]>>>1|(t[i-1]&1)<<31;e[0]=t[0]>>>1,r&&(e[0]^=this._R)};l.gcm.prototype.tableMultiply=function(t){for(var e=[0,0,0,0],r=0;r<32;++r){var i=r/8|0,a=t[i]>>>(7-r%8)*4&15,h=this._m[r][a];e[0]^=h[0],e[1]^=h[1],e[2]^=h[2],e[3]^=h[3]}return e};l.gcm.prototype.ghash=function(t,e,r){return e[0]^=r[0],e[1]^=r[1],e[2]^=r[2],e[3]^=r[3],this.tableMultiply(e)};l.gcm.prototype.generateHashTable=function(t,e){for(var r=8/e,i=4*r,a=16*r,h=new Array(a),s=0;s<a;++s){var o=[0,0,0,0],f=s/i|0,u=(i-1-s%i)*e;o[f]=1<<e-1<<u,h[s]=this.generateSubHashTable(this.multiply(o,t),e)}return h};l.gcm.prototype.generateSubHashTable=function(t,e){var r=1<<e,i=r>>>1,a=new Array(r);a[i]=t.slice(0);for(var h=i>>>1;h>0;)this.pow(a[2*h],a[h]=[]),h>>=1;for(h=2;h<i;){for(var s=1;s<h;++s){var o=a[h],f=a[s];a[h+s]=[o[0]^f[0],o[1]^f[1],o[2]^f[2],o[3]^f[3]]}h*=2}for(a[0]=[0,0,0,0],h=i+1;h<r;++h){var u=a[h^i];a[h]=[t[0]^u[0],t[1]^u[1],t[2]^u[2],t[3]^u[3]]}return a};function E(t,e){if(typeof t=="string"&&(t=B.util.createBuffer(t)),B.util.isArray(t)&&t.length>4){var r=t;t=B.util.createBuffer();for(var i=0;i<r.length;++i)t.putByte(r[i])}if(t.length()<e)throw new Error("Invalid IV length; got "+t.length()+" bytes and expected "+e+" bytes.");if(!B.util.isArray(t)){for(var a=[],h=e/4,i=0;i<h;++i)a.push(t.getInt32());t=a}return t}function T(t){t[t.length-1]=t[t.length-1]+1&4294967295}function H(t){return[t/4294967296|0,t&4294967295]}});var ut=A((Ct,ft)=>{var p=x();j();nt();O();ft.exports=p.aes=p.aes||{};p.aes.startEncrypting=function(t,e,r,i){var a=N({key:t,output:r,decrypt:!1,mode:i});return a.start(e),a};p.aes.createEncryptionCipher=function(t,e){return N({key:t,output:null,decrypt:!1,mode:e})};p.aes.startDecrypting=function(t,e,r,i){var a=N({key:t,output:r,decrypt:!0,mode:i});return a.start(e),a};p.aes.createDecryptionCipher=function(t,e){return N({key:t,output:null,decrypt:!0,mode:e})};p.aes.Algorithm=function(t,e){J||st();var r=this;r.name=t,r.mode=new e({blockSize:16,cipher:{encrypt:function(i,a){return W(r._w,i,a,!1)},decrypt:function(i,a){return W(r._w,i,a,!0)}}}),r._init=!1};p.aes.Algorithm.prototype.initialize=function(t){if(!this._init){var e=t.key,r;if(typeof e=="string"&&(e.length===16||e.length===24||e.length===32))e=p.util.createBuffer(e);else if(p.util.isArray(e)&&(e.length===16||e.length===24||e.length===32)){r=e,e=p.util.createBuffer();for(var i=0;i<r.length;++i)e.putByte(r[i])}if(!p.util.isArray(e)){r=e,e=[];var a=r.length();if(a===16||a===24||a===32){a=a>>>2;for(var i=0;i<a;++i)e.push(r.getInt32())}}if(!p.util.isArray(e)||!(e.length===4||e.length===6||e.length===8))throw new Error("Invalid key parameter.");var h=this.mode.name,s=["CFB","OFB","CTR","GCM"].indexOf(h)!==-1;this._w=ot(e,t.decrypt&&!s),this._init=!0}};p.aes._expandKey=function(t,e){return J||st(),ot(t,e)};p.aes._updateBlock=W;D("AES-ECB",p.cipher.modes.ecb);D("AES-CBC",p.cipher.modes.cbc);D("AES-CFB",p.cipher.modes.cfb);D("AES-OFB",p.cipher.modes.ofb);D("AES-CTR",p.cipher.modes.ctr);D("AES-GCM",p.cipher.modes.gcm);function D(t,e){var r=function(){return new p.aes.Algorithm(t,e)};p.cipher.registerAlgorithm(t,r)}var J=!1,z=4,_,G,ht,F,S;function st(){J=!0,ht=[0,1,2,4,8,16,32,64,128,27,54];for(var t=new Array(256),e=0;e<128;++e)t[e]=e<<1,t[e+128]=e+128<<1^283;_=new Array(256),G=new Array(256),F=new Array(4),S=new Array(4);for(var e=0;e<4;++e)F[e]=new Array(256),S[e]=new Array(256);for(var r=0,i=0,a,h,s,o,f,u,c,e=0;e<256;++e){o=i^i<<1^i<<2^i<<3^i<<4,o=o>>8^o&255^99,_[r]=o,G[o]=r,f=t[o],a=t[r],h=t[a],s=t[h],u=f<<24^o<<16^o<<8^(o^f),c=(a^h^s)<<24^(r^s)<<16^(r^h^s)<<8^(r^a^s);for(var d=0;d<4;++d)F[d][r]=u,S[d][o]=c,u=u<<24|u>>>8,c=c<<24|c>>>8;r===0?r=i=1:(r=a^t[t[t[a^s]]],i^=t[t[i]])}}function ot(t,e){for(var r=t.slice(0),i,a=1,h=r.length,s=h+6+1,o=z*s,f=h;f<o;++f)i=r[f-1],f%h===0?(i=_[i>>>16&255]<<24^_[i>>>8&255]<<16^_[i&255]<<8^_[i>>>24]^ht[a]<<24,a++):h>6&&f%h===4&&(i=_[i>>>24]<<24^_[i>>>16&255]<<16^_[i>>>8&255]<<8^_[i&255]),r[f]=r[f-h]^i;if(e){var u,c=S[0],d=S[1],y=S[2],g=S[3],m=r.slice(0);o=r.length;for(var f=0,w=o-z;f<o;f+=z,w-=z)if(f===0||f===o-z)m[f]=r[w],m[f+1]=r[w+3],m[f+2]=r[w+2],m[f+3]=r[w+1];else for(var C=0;C<z;++C)u=r[w+C],m[f+(3&-C)]=c[_[u>>>24]]^d[_[u>>>16&255]]^y[_[u>>>8&255]]^g[_[u&255]];r=m}return r}function W(t,e,r,i){var a=t.length/4-1,h,s,o,f,u;i?(h=S[0],s=S[1],o=S[2],f=S[3],u=G):(h=F[0],s=F[1],o=F[2],f=F[3],u=_);var c,d,y,g,m,w,C;c=e[0]^t[0],d=e[i?3:1]^t[1],y=e[2]^t[2],g=e[i?1:3]^t[3];for(var b=3,K=1;K<a;++K)m=h[c>>>24]^s[d>>>16&255]^o[y>>>8&255]^f[g&255]^t[++b],w=h[d>>>24]^s[y>>>16&255]^o[g>>>8&255]^f[c&255]^t[++b],C=h[y>>>24]^s[g>>>16&255]^o[c>>>8&255]^f[d&255]^t[++b],g=h[g>>>24]^s[c>>>16&255]^o[d>>>8&255]^f[y&255]^t[++b],c=m,d=w,y=C;r[0]=u[c>>>24]<<24^u[d>>>16&255]<<16^u[y>>>8&255]<<8^u[g&255]^t[++b],r[i?3:1]=u[d>>>24]<<24^u[y>>>16&255]<<16^u[g>>>8&255]<<8^u[c&255]^t[++b],r[2]=u[y>>>24]<<24^u[g>>>16&255]<<16^u[c>>>8&255]<<8^u[d&255]^t[++b],r[i?1:3]=u[g>>>24]<<24^u[c>>>16&255]<<16^u[d>>>8&255]<<8^u[y&255]^t[++b]}function N(t){t=t||{};var e=(t.mode||"CBC").toUpperCase(),r="AES-"+e,i;t.decrypt?i=p.cipher.createDecipher(r,t.key):i=p.cipher.createCipher(r,t.key);var a=i.start;return i.start=function(h,s){var o=null;s instanceof p.util.ByteBuffer&&(o=s,s={}),s=s||{},s.output=o,s.iv=h,a.call(i,s)},i}});var _t=A((At,ct)=>{ct.exports=x();ut();j();O()});return _t();})();

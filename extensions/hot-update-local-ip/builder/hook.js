
'use strict';

const os = require("os");
const fs = require("fs");
const path = require("path");
const { execSync } = require('child_process');

const remote_port = 8000;

function getIPAdress() {
    return "hupdate.bavenoth.com"; 
}


exports.onAfterBuild = function (options, result) {
    console.warn("node 16+ require for fs.cpSync recursive")
    let resdir = 'assets';
    if ((options.platform == "android" || options.platform == "ios") && options.md5Cache == false) {

        if (fs.existsSync(path.join(result.dest, 'data'))) {
            resdir = 'data';
        }

        let cmd = `node version_generator.js -v 1.0.0 -u https://${getIPAdress()}/assets_${options.platform}/ -s ${path.join(result.dest, resdir)} -d ${path.join(result.dest, resdir)}`
        console.warn(cmd);

        execSync(cmd, { cwd: Editor.Project.path }, (err, stdout, stderr) => {
            if (!err) return;
            console.error(err);
        });
    }
    if ((options.platform == "android" || options.platform == "ios") && options.md5Cache == true) {

        if (fs.existsSync(path.join(result.dest, 'remote'))) {
            resdir = 'remote';
        }

        let cmd1 = `node version_bundle_generator.js -v 1.0.0 -u https://${getIPAdress()}/bundle -s ${path.join(result.dest, resdir)} -d ${path.join(result.dest, "remote")}`
        console.warn(cmd1);

        execSync(cmd1, { cwd: Editor.Project.path }, (err, stdout, stderr) => {
            if (!err) return;
            console.error(err);
        });
    }
    if (options.platform == "web-mobile" || options.platform == "web-desktop") {
        let cmd3 = `node copyweb.js -platform ${options.platform} -dest ${result.dest}`
        console.warn(cmd3);

        execSync(cmd3, { cwd: Editor.Project.path }, (err, stdout, stderr) => {
            if (!err) return;
            console.error(err);
        });
    }
}
